#ifndef BACKGROUNDCONFIGDIALOG_H
#define BACKGROUNDCONFIGDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QRadioButton>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QSlider>
#include <QLabel>
#include <QColorDialog>
#include <QFileDialog>
#include <QVariantMap>

class BackgroundConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit BackgroundConfigDialog(QWidget *parent = nullptr);
    ~BackgroundConfigDialog();

    void setConfig(const QVariantMap &config);
    QVariantMap getConfig() const;

public slots:
    void accept() override;
    void reject() override;

signals:
    void configChanged(const QString &key, const QVariant &value);

private slots:
    void onBackgroundTypeChanged();
    void onChooseColor();
    void onChooseColor2();
    void onChooseImage();
    void onOpacityChanged(int value);
    void onPreview();
    void onResetToDefaults();

private:
    void setupUI();
    void loadConfig();
    void saveConfig();
    void updateUI();
    void updatePreview();
    void updateColorButton();
    void updateColor2Button();
    void updateOpacityLabel();

private:
    // 背景类型
    QGroupBox *m_typeGroup;
    QRadioButton *m_colorRadio;
    QRadioButton *m_gradientRadio;
    QRadioButton *m_imageRadio;

    // 颜色配置
    QGroupBox *m_colorGroup;
    QPushButton *m_colorBtn;
    QPushButton *m_color2Btn;
    QLabel *m_colorLabel;
    QLabel *m_color2Label;

    // 图片配置
    QGroupBox *m_imageGroup;
    QLineEdit *m_imagePathEdit;
    QPushButton *m_browseImageBtn;
    QComboBox *m_imageModeCombo;

    // 透明度配置
    QGroupBox *m_opacityGroup;
    QSlider *m_opacitySlider;
    QLabel *m_opacityValueLabel;

    // 预览
    QGroupBox *m_previewGroup;
    QLabel *m_previewLabel;

    // 按钮
    QPushButton *m_okBtn;
    QPushButton *m_cancelBtn;
    QPushButton *m_resetBtn;
    QPushButton *m_previewBtn;

    // 配置数据
    QVariantMap m_config;
    QString m_currentColor;
    QString m_currentColor2;
    QString m_currentImagePath;
    double m_currentOpacity;
};

#endif // BACKGROUNDCONFIGDIALOG_H
