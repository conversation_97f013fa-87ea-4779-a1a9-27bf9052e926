RF Debug Tool Qt Version - Complete Standalone Package
=====================================================

Package Time: 2025-06-30 19:19:43
Author: flex (<EMAIL>)

COMPLETE STANDALONE PACKAGE:
This package includes ALL required Qt libraries and dependencies.
It should run on ANY Windows computer without additional installations.

File Description:
  * RFTool_Qt.exe - Main program
  * Start.bat - Quick start script
  * Qt5*.dll - Qt runtime libraries
  * platforms/ - Qt platform plugins
  * config.ini - Configuration file (if exists)
  * Other DLLs - Additional runtime libraries

How to use:
  1. Copy this entire folder to any Windows computer
  2. Double-click RFTool_Qt.exe to run directly
  3. Or double-click "Start.bat" to run
  4. No additional software installation required

Main Features:
  * Multiple connection types: ADB, Serial, TCP
  * Smart log management and storage
  * Remote collaboration debugging
  * Search and highlight functions
  * Professional terminal interface
  * Connection history management
  * Customizable background and transparency

System Requirements:
  * Windows 7 SP1 or higher
  * At least 100MB available disk space
  * No additional software required

Technical Support: <EMAIL>
