[General]
quick_command_1=ls -la
quick_command_1_name=日志保存
quick_command_2=pwd
quick_command_2_name=路径
quick_command_3=whoami
quick_command_3_name=用户
commands=@Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bQ\x85[XO\x7fu(\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\0\0\0\x10\x66>y:Q\x85[XO\x7fu(`\xc5Q\xb5\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xb9\x4&j\x9c\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\xe\0\x66\0r\0\x65\0\x65\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bR\x17Q\xfa\x65\x87N\xf6\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\0\0\0\x1a\x66>y:_SRMv\xee_Uv\x84\x8b\xe6~\xc6\x65\x87N\xf6R\x17\x88h\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xb9\x4&j\x9c\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\f\0l\0s\0 \0-\0l\0\x61), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b_SRMv\xee_U\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\0\0\0\x10\x66>y:_SRM]\xe5O\\v\xee_U\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xb9\x4&j\x9c\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x6\0p\0w\0\x64), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bx\xc1v\xd8O\x7fu(\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\0\0\0\x10\x66>y:x\xc1v\xd8O\x7fu(`\xc5Q\xb5\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xb9\x4&j\x9c\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\n\0\x64\0\x66\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b|\xfb~\xdfO\xe1`o\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\0\0\0\ff>y:|\xfb~\xdfO\xe1`o\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xb9\x4&j\x9c\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x10\0u\0n\0\x61\0m\0\x65\0 \0-\0\x61)
background_type=color
background_color=#000000
background_color2=#333333
background_image=
background_opacity=0.99
background_image_mode=center
log_timestamp_enabled=true
log_timestamp_format=yyyy-MM-dd hh:mm:ss
log_file_enabled=true
log_file_path=C:/Users/<USER>/Desktop/rf_tool.log
log_auto_save=true
log_auto_save_interval=30
log_echo_enabled=true
log_level=1
log_max_lines=10000
log_rotation_enabled=false
log_max_file_size=10
log_max_backup_files=5
timestamp_enabled=true

[app]
version=2.0.0
language=zh_CN
theme=default

[window]
width=1200
height=800
maximized=false

[connection]
timeout=30
auto_reconnect=false
max_reconnect_attempts=3

[log]
timestamp_enabled=true
timestamp_format=yyyy-MM-dd hh:mm:ss
echo_enabled=true
file_enabled=false
file_path=
auto_save=true
auto_save_interval=30
level=1
max_lines=10000
rotation_enabled=false
max_file_size=10
max_backup_files=5

[terminal]
timestamp_enabled=true
timestamp_format=hh:mm:ss
echo_enabled=true
max_history=1000

[background]
type=color
color=#f8f9fa
color2=#e9ecef
image=
opacity=1.0
