@echo off
setlocal enabledelayedexpansion
echo ========================================
echo    RF Debug Tool - Complete Package
echo ========================================
echo.

color 0A
title RF Debug Tool Complete Package

REM Get timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

REM Set paths
set "PROJECT_DIR=%~dp0"
set "BIN_DIR=%PROJECT_DIR%bin"
set "PACKAGE_DIR=%PROJECT_DIR%RFTool_Complete_%timestamp%"
set "EXE_NAME=RFTool_Qt.exe"

echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Project Directory: %PROJECT_DIR%
echo.

REM Check executable file
echo [1/5] Checking executable file...
if not exist "%BIN_DIR%\%EXE_NAME%" (
    echo ERROR: Executable file not found: %BIN_DIR%\%EXE_NAME%
    echo.
    echo Please compile the project in Qt Creator first:
    echo   1. Open RFTool_Qt.pro in Qt Creator
    echo   2. Select Release mode
    echo   3. Click Build - Build Project
    echo   4. Make sure compilation is successful
    echo.
    pause
    exit /b 1
)
echo OK Found executable file: %EXE_NAME%
echo.

REM Find Qt installation and setup environment
echo [2/5] Setting up Qt environment...
set "QT_FOUND="
set "QT_BIN_PATH="

REM Common Qt installation paths
set "QT_PATHS="
set "QT_PATHS=%QT_PATHS% C:\Qt\5.12.9\mingw73_64\bin"
set "QT_PATHS=%QT_PATHS% C:\Qt\5.15.2\mingw81_64\bin"
set "QT_PATHS=%QT_PATHS% C:\Qt\6.2.4\mingw_64\bin"
set "QT_PATHS=%QT_PATHS% C:\Qt\6.5.3\mingw_64\bin"
set "QT_PATHS=%QT_PATHS% D:\Qt\5.12.9\mingw73_64\bin"
set "QT_PATHS=%QT_PATHS% D:\Qt\5.15.2\mingw81_64\bin"

REM Check if windeployqt is already in PATH
where windeployqt >nul 2>&1
if %errorlevel% equ 0 (
    echo OK windeployqt found in PATH
    set "QT_FOUND=1"
    goto :qt_setup_done
)

REM Search for Qt installation
for %%p in (%QT_PATHS%) do (
    if exist "%%p\windeployqt.exe" (
        echo Found Qt at: %%p
        set "QT_BIN_PATH=%%p"
        set "PATH=%%p;!PATH!"
        set "QT_FOUND=1"
        goto :qt_setup_done
    )
)

:qt_setup_done
if not defined QT_FOUND (
    echo ERROR: Qt installation with windeployqt not found
    echo.
    echo Please ensure Qt is installed and try one of these:
    echo   1. Run this script from Qt Creator terminal
    echo   2. Add Qt bin directory to system PATH
    echo   3. Install Qt to a standard location
    echo.
    echo Searched paths:
    for %%p in (%QT_PATHS%) do echo     %%p
    echo.
    pause
    exit /b 1
)
echo OK Qt environment ready
echo.

REM Create package directory
echo [3/5] Creating package directory...
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"
echo OK Package directory created: %PACKAGE_DIR%
echo.

REM Copy main program
echo [4/5] Copying and deploying application...
copy "%BIN_DIR%\%EXE_NAME%" "%PACKAGE_DIR%\"
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy main program
    pause
    exit /b 1
)
echo OK Main program copied

REM Use windeployqt to deploy all Qt dependencies
echo Running windeployqt to package all dependencies...
cd /d "%PACKAGE_DIR%"
windeployqt --release --no-translations --no-system-d3d-compiler --no-opengl-sw "%EXE_NAME%"
if %errorlevel% neq 0 (
    echo WARNING: windeployqt completed with warnings
) else (
    echo OK windeployqt completed successfully
)

REM Copy additional files
echo Copying additional files...
if exist "%PROJECT_DIR%config.ini" (
    copy "%PROJECT_DIR%config.ini" "%PACKAGE_DIR%\" >nul
    echo OK Config file copied
)

if exist "%PROJECT_DIR%resources" (
    xcopy "%PROJECT_DIR%resources" "%PACKAGE_DIR%\resources" /e /i /y /q >nul 2>&1
    echo OK Resource files copied
)

if exist "%PROJECT_DIR%icons" (
    xcopy "%PROJECT_DIR%icons" "%PACKAGE_DIR%\icons" /e /i /y /q >nul 2>&1
    echo OK Icon files copied
)

REM Copy any additional DLLs from bin directory that windeployqt might have missed
if exist "%BIN_DIR%\*.dll" (
    for %%f in ("%BIN_DIR%\*.dll") do (
        if not exist "%PACKAGE_DIR%\%%~nxf" (
            copy "%%f" "%PACKAGE_DIR%\" >nul 2>&1
        )
    )
    echo OK Additional DLLs checked and copied if needed
)

cd /d "%PROJECT_DIR%"
echo.

REM Create documentation
echo [5/5] Creating documentation...
(
echo RF Debug Tool Qt Version - Complete Standalone Package
echo =====================================================
echo.
echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Author: flex ^(<EMAIL>^)
echo.
echo COMPLETE STANDALONE PACKAGE:
echo This package includes ALL required Qt libraries and dependencies.
echo It should run on ANY Windows computer without additional installations.
echo.
echo File Description:
echo   * %EXE_NAME% - Main program
echo   * Start.bat - Quick start script
echo   * Qt5*.dll - Qt runtime libraries
echo   * platforms/ - Qt platform plugins
echo   * config.ini - Configuration file ^(if exists^)
echo   * Other DLLs - Additional runtime libraries
echo.
echo How to use:
echo   1. Copy this entire folder to any Windows computer
echo   2. Double-click %EXE_NAME% to run directly
echo   3. Or double-click "Start.bat" to run
echo   4. No additional software installation required
echo.
echo Main Features:
echo   * Multiple connection types: ADB, Serial, TCP
echo   * Smart log management and storage
echo   * Remote collaboration debugging
echo   * Search and highlight functions
echo   * Professional terminal interface
echo   * Connection history management
echo   * Customizable background and transparency
echo.
echo System Requirements:
echo   * Windows 7 SP1 or higher
echo   * At least 100MB available disk space
echo   * No additional software required
echo.
echo Technical Support: <EMAIL>
) > "%PACKAGE_DIR%\README.txt"

REM Create startup script
echo @echo off > "%PACKAGE_DIR%\Start.bat"
echo cd /d "%%~dp0" >> "%PACKAGE_DIR%\Start.bat"
echo echo Starting RF Debug Tool... >> "%PACKAGE_DIR%\Start.bat"
echo start "" "%EXE_NAME%" >> "%PACKAGE_DIR%\Start.bat"

echo OK Documentation files created
echo.

REM Display results
echo ========================================
echo      Complete Package Finished!
echo ========================================
echo.
echo Package Directory: %PACKAGE_DIR%
echo.
echo Package Contents:
dir "%PACKAGE_DIR%" /b | findstr /v "^$"
echo.
echo Package Size:
for /f "tokens=3" %%a in ('dir "%PACKAGE_DIR%" /s /-c ^| find "File(s)"') do echo   Total Size: %%a bytes
echo.

REM Quality Check
echo Quality Check:
if exist "%PACKAGE_DIR%\%EXE_NAME%" (
    echo   OK Main program exists
) else (
    echo   ERROR Main program missing
)
if exist "%PACKAGE_DIR%\Qt5Core.dll" (
    echo   OK Qt Core library exists
) else (
    echo   ERROR Qt Core library missing
)
if exist "%PACKAGE_DIR%\Qt5SerialPort.dll" (
    echo   OK Qt SerialPort library exists
) else (
    echo   ERROR Qt SerialPort library missing
)
if exist "%PACKAGE_DIR%\platforms\qwindows.dll" (
    echo   OK Qt Platform plugin exists
) else (
    echo   ERROR Qt Platform plugin missing
)
echo.

echo COMPLETE STANDALONE PACKAGE READY!
echo This package should run on any Windows computer.
echo.

REM Auto open package directory
echo Opening package directory...
explorer "%PACKAGE_DIR%"

echo.
echo ========================================
echo    Complete Package Script Finished!
echo ========================================
echo Package Directory: %PACKAGE_DIR%
echo.
echo The complete standalone package is ready!
echo.
pause
