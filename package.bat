@echo off
setlocal enabledelayedexpansion
echo ========================================
echo      RF Debug Tool - Package Script
echo ========================================
echo.

color 0B
title RF Debug Tool Package

REM Get timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

REM Set version info
set "VERSION=2.0"
set "BUILD_NUMBER=%YYYY:~2,2%%MM%%DD%"

REM Set paths
set "PROJECT_DIR=%~dp0"
set "BIN_DIR=%PROJECT_DIR%bin"
set "PACKAGE_DIR=%PROJECT_DIR%RFTool_Package_%timestamp%"
set "DIST_DIR=%PROJECT_DIR%Distribution"
set "EXE_NAME=RFTool_Qt.exe"
set "ZIP_NAME=RFTool_v%VERSION%_%timestamp%.zip"

echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Version: %VERSION%.%BUILD_NUMBER%
echo Project Directory: %PROJECT_DIR%
echo.

REM Check executable file
echo [1/6] Checking executable file...
if not exist "%BIN_DIR%\%EXE_NAME%" (
    echo ERROR: Executable file not found: %BIN_DIR%\%EXE_NAME%
    echo.
    echo Please compile the project in Qt Creator first:
    echo   1. Open RFTool_Qt.pro
    echo   2. Select Release mode
    echo   3. Click Build - Build Project
    echo.
    pause
    exit /b 1
)
echo OK Found executable file: %EXE_NAME%
echo.

REM Check and setup Qt environment
echo [2/6] Checking Qt deployment tool...
where windeployqt >nul 2>&1
if %errorlevel% neq 0 (
    echo windeployqt not found in PATH, searching for Qt installation...

    REM Common Qt installation paths
    set "QT_PATHS="
    set "QT_PATHS=%QT_PATHS% C:\Qt\5.12.9\mingw73_64\bin"
    set "QT_PATHS=%QT_PATHS% C:\Qt\5.15.2\mingw81_64\bin"
    set "QT_PATHS=%QT_PATHS% C:\Qt\6.2.4\mingw_64\bin"
    set "QT_PATHS=%QT_PATHS% C:\Qt\6.5.3\mingw_64\bin"
    set "QT_PATHS=%QT_PATHS% D:\Qt\5.12.9\mingw73_64\bin"
    set "QT_PATHS=%QT_PATHS% D:\Qt\5.15.2\mingw81_64\bin"

    set "QT_FOUND="
    for %%p in (%QT_PATHS%) do (
        if exist "%%p\windeployqt.exe" (
            echo Found Qt at: %%p
            set "PATH=%%p;!PATH!"
            set "QT_FOUND=1"
            goto :qt_found
        )
    )

    :qt_found
    if not defined QT_FOUND (
        echo ERROR: Qt installation not found automatically
        echo.
        echo Please do one of the following:
        echo   1. Run this script from Qt Creator terminal
        echo   2. Add Qt bin directory to PATH manually
        echo   3. Install Qt to a standard location
        echo.
        echo Standard Qt paths checked:
        for %%p in (%QT_PATHS%) do echo     %%p
        echo.
        pause
        exit /b 1
    )
)

REM Verify windeployqt is now available
where windeployqt >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: windeployqt still not found after path setup
    pause
    exit /b 1
)
echo OK Qt deployment tool found and ready
echo.

REM Clean old package directories
echo [3/6] Cleaning old package directories...
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
if exist "%DIST_DIR%" rmdir /s /q "%DIST_DIR%"
mkdir "%PACKAGE_DIR%"
mkdir "%DIST_DIR%"
echo OK Cleanup completed

REM Copy main program
echo [4/6] Copying main program...
copy "%BIN_DIR%\%EXE_NAME%" "%PACKAGE_DIR%\"
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy main program
    pause
    exit /b 1
)
echo OK Main program copied

REM Deploy Qt dependencies
echo [5/6] Deploying Qt dependencies...
echo Running windeployqt...
windeployqt --release --no-translations --no-system-d3d-compiler --no-opengl-sw "%PACKAGE_DIR%\%EXE_NAME%"
if %errorlevel% neq 0 (
    echo ERROR: windeployqt execution failed
    pause
    exit /b 1
)
echo OK Qt dependencies deployed

REM Copy config files and resources
if exist "%PROJECT_DIR%config.ini" (
    copy "%PROJECT_DIR%config.ini" "%PACKAGE_DIR%\" >nul
    echo OK Config file copied
)
if exist "%PROJECT_DIR%resources" (
    xcopy "%PROJECT_DIR%resources" "%PACKAGE_DIR%\resources" /e /i /y /q
    echo OK Resource files copied
)
if exist "%PROJECT_DIR%icons" (
    xcopy "%PROJECT_DIR%icons" "%PACKAGE_DIR%\icons" /e /i /y /q
    echo OK Icon files copied
)
echo.

REM Create documentation and additional files
echo [6/6] Creating documentation and additional files...

REM Create user manual
(
echo RF Debug Tool Qt Version - User Manual
echo =====================================
echo.
echo Version: %VERSION%.%BUILD_NUMBER%
echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Author: flex ^(<EMAIL>^)
echo.
echo File Description:
echo   * %EXE_NAME% - Main program
echo   * Start.bat - Quick start script
echo   * config.ini - Configuration file
echo   * platforms/ - Qt platform plugins
echo   * Other dll files - Runtime dependencies
echo.
echo How to use:
echo   1. Double-click %EXE_NAME% to run directly
echo   2. Or double-click "Start.bat" to run
echo.
echo Main Features:
echo   * Multiple connection types: ADB, Serial, TCP
echo   * Smart log management and storage
echo   * Remote collaboration debugging
echo   * Search and highlight functions
echo   * Professional terminal interface
echo   * Connection history management
echo   * Customizable background and transparency
echo   * Timestamp display
echo   * Command history
echo.
echo System Requirements:
echo   * Windows 7 SP1 or higher
echo   * .NET Framework 4.0 or higher
echo   * At least 100MB available disk space
echo.
echo Notes:
echo   * First run will create configuration files automatically
echo   * Supports high DPI displays
echo   * Configuration files use UTF-8 encoding
echo.
echo Technical Support: <EMAIL>
) > "%PACKAGE_DIR%\User_Manual.txt"

REM Create startup script
echo @echo off > "%PACKAGE_DIR%\Start.bat"
echo cd /d "%%~dp0" >> "%PACKAGE_DIR%\Start.bat"
echo start "" "%EXE_NAME%" >> "%PACKAGE_DIR%\Start.bat"

REM Create version info
(
echo RF Debug Tool Qt Version - Version Info
echo ======================================
echo Version: %VERSION%.%BUILD_NUMBER%
echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Build Environment: Windows + Qt5.12.9 + MinGW
echo Package Method: Qt IDE + windeployqt
echo.
echo Update Log:
echo * Added remote collaboration debugging
echo * Added search and highlight functions
echo * Optimized terminal performance and stability
echo * Fixed multiple known issues
echo * Enhanced user interface experience
echo * Support for high DPI displays
echo * Optimized memory usage
echo * Improved connection stability
echo.
echo Technical Support: <EMAIL>
) > "%PACKAGE_DIR%\Version_Info.txt"

echo OK Documentation files created
echo.

REM Create portable version and zip
echo Creating portable version...
xcopy "%PACKAGE_DIR%" "%DIST_DIR%\RFTool_Portable_v%VERSION%_%timestamp%" /e /i /y /q
echo OK Portable version created

REM Create zip package
echo Creating zip package...
set "SEVEN_ZIP="
if exist "C:\Program Files\7-Zip\7z.exe" set "SEVEN_ZIP=C:\Program Files\7-Zip\7z.exe"
if exist "C:\Program Files (x86)\7-Zip\7z.exe" set "SEVEN_ZIP=C:\Program Files (x86)\7-Zip\7z.exe"

if defined SEVEN_ZIP (
    echo Using 7-Zip to create zip package...
    "%SEVEN_ZIP%" a -tzip "%DIST_DIR%\%ZIP_NAME%" "%PACKAGE_DIR%\*" -mx9 >nul
    if %errorlevel% equ 0 (
        echo OK Zip package created successfully: %ZIP_NAME%
    ) else (
        echo WARNING: 7-Zip compression failed, using PowerShell...
        goto :powershell_zip
    )
) else (
    echo Using PowerShell to create zip package...
    :powershell_zip
    powershell -command "Compress-Archive -Path '%PACKAGE_DIR%\*' -DestinationPath '%DIST_DIR%\%ZIP_NAME%' -CompressionLevel Optimal" 2>nul
    if %errorlevel% equ 0 (
        echo OK PowerShell zip package created successfully: %ZIP_NAME%
    ) else (
        echo WARNING: Zip compression failed
    )
)
echo.

REM Display results
echo ========================================
echo          Package Complete!
echo ========================================
echo.
echo Package Directory: %PACKAGE_DIR%
echo Distribution Directory: %DIST_DIR%
echo.
echo Generated Files:
dir "%DIST_DIR%" /b 2>nul
echo.
echo Package Contents:
dir "%PACKAGE_DIR%" /b | findstr /v "^$"
echo.
echo Package Size:
for /f "tokens=3" %%a in ('dir "%PACKAGE_DIR%" /s /-c ^| find "File(s)"') do echo   Package Size: %%a bytes
if exist "%DIST_DIR%\%ZIP_NAME%" (
    for %%f in ("%DIST_DIR%\%ZIP_NAME%") do echo   Zip Size: %%~zf bytes
)
echo.
echo Quality Check:
if exist "%PACKAGE_DIR%\%EXE_NAME%" (
    echo   OK Main program exists
) else (
    echo   ERROR Main program missing
)
if exist "%PACKAGE_DIR%\platforms" (
    echo   OK Qt plugins complete
) else (
    echo   ERROR Qt plugins missing
)
echo.
echo How to use:
echo   1. Copy package folder to target computer
echo   2. Double-click %EXE_NAME% to run
echo   3. Or double-click Start.bat to run
echo   4. Use zip file for distribution
echo.

REM Ask what to do next
echo What would you like to do next?
echo   [1] Open distribution directory
echo   [2] Test the program
echo   [3] Exit
echo.
set /p choice="Please select (1-3): "

if "%choice%"=="1" (
    explorer "%DIST_DIR%"
) else if "%choice%"=="2" (
    echo.
    echo Starting program for testing...
    start "" "%PACKAGE_DIR%\%EXE_NAME%"
) else (
    echo Goodbye!
)

echo.
echo Package script completed successfully!
echo Package Directory: %PACKAGE_DIR%
echo Distribution Directory: %DIST_DIR%
pause
