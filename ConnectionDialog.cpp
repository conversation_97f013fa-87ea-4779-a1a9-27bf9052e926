#include "ConnectionDialog.h"
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QSerialPortInfo>
#include <QProcess>
#include <QDebug>
#include <QShowEvent>
#include <QTimer>

ConnectionDialog::ConnectionDialog(QWidget *parent)
    : QDialog(parent), m_currentType(Serial), m_isAdvancedVisible(false), m_isTesting(false)
{
    setWindowTitle("连接设备");
    setModal(true);
    resize(500, 400);

    setupUI();
    loadHistory();
}

ConnectionDialog::~ConnectionDialog()
{
    saveHistory();
}

void ConnectionDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 创建标签页
    m_tabWidget = new QTabWidget;

    setupSerialTab();
    setupTCPTab();
    setupADBTab();
    setupNetworkTab();
    setupFTPTab();
    setupHistoryTab();

    m_mainLayout->addWidget(m_tabWidget);

    // 连接标签页切换信号
    connect(m_tabWidget, &QTabWidget::currentChanged, this, &ConnectionDialog::onConnectionTypeChanged);

    setupButtons();
}

void ConnectionDialog::setupSerialTab()
{
    m_serialTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_serialTab);

    // 端口选择 - 支持手动输入
    m_serialPortCombo = new QComboBox;
    m_serialPortCombo->setEditable(true); // 允许手动输入
    m_serialPortCombo->setInsertPolicy(QComboBox::NoInsert);
    m_serialPortCombo->lineEdit()->setPlaceholderText("选择或输入端口号，如: COM1, /dev/ttyUSB0");

    m_refreshPortsBtn = new QPushButton("刷新端口");
    QHBoxLayout *portLayout = new QHBoxLayout;
    portLayout->addWidget(m_serialPortCombo);
    portLayout->addWidget(m_refreshPortsBtn);
    layout->addRow("串口:", portLayout);

    // 波特率选择 - 支持手动输入和高波特率
    m_baudRateCombo = new QComboBox;
    m_baudRateCombo->setEditable(true); // 允许手动输入
    m_baudRateCombo->setInsertPolicy(QComboBox::NoInsert);
    m_baudRateCombo->addItems({"9600", "19200", "38400", "57600", "115200", "230400",
                               "460800", "921600", "1000000", "1500000", "2000000",
                               "2500000", "3000000"});
    m_baudRateCombo->setCurrentText("115200");
    m_baudRateCombo->lineEdit()->setPlaceholderText("选择或输入波特率 (9600-3000000)");
    layout->addRow("波特率:", m_baudRateCombo);

    m_dataBitsCombo = new QComboBox;
    m_dataBitsCombo->addItems({"5", "6", "7", "8"});
    m_dataBitsCombo->setCurrentText("8");
    layout->addRow("数据位:", m_dataBitsCombo);

    m_stopBitsCombo = new QComboBox;
    m_stopBitsCombo->addItems({"1", "1.5", "2"});
    layout->addRow("停止位:", m_stopBitsCombo);

    m_parityCombo = new QComboBox;
    m_parityCombo->addItems({"无", "奇校验", "偶校验"});
    layout->addRow("校验位:", m_parityCombo);

    m_flowControlCombo = new QComboBox;
    m_flowControlCombo->addItems({"无", "硬件", "软件"});
    layout->addRow("流控制:", m_flowControlCombo);

    m_tabWidget->addTab(m_serialTab, "串口");

    connect(m_refreshPortsBtn, &QPushButton::clicked, this, &ConnectionDialog::onSerialPortRefresh);
}

void ConnectionDialog::setupTCPTab()
{
    m_tcpTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_tcpTab);

    m_tcpHostEdit = new QLineEdit;
    layout->addRow("主机:", m_tcpHostEdit);

    m_tcpPortSpin = new QSpinBox;
    m_tcpPortSpin->setRange(1, 65535);
    m_tcpPortSpin->setValue(8080);
    layout->addRow("端口:", m_tcpPortSpin);

    m_tcpProtocolCombo = new QComboBox;
    m_tcpProtocolCombo->addItems({"TCP", "UDP"});
    layout->addRow("协议:", m_tcpProtocolCombo);

    m_tcpLineEndingEdit = new QLineEdit;
    m_tcpLineEndingEdit->setText("\\r\\n");
    m_tcpLineEndingEdit->setPlaceholderText("行结束符，如 \\r\\n");
    layout->addRow("行结束符:", m_tcpLineEndingEdit);

    m_tcpEncodingCombo = new QComboBox;
    m_tcpEncodingCombo->addItems({"UTF-8", "ASCII", "GBK", "GB2312"});
    layout->addRow("编码:", m_tcpEncodingCombo);

    m_tcpKeepAliveCheck = new QCheckBox("启用保活");
    layout->addRow(m_tcpKeepAliveCheck);

    m_tcpTimeoutSpin = new QSpinBox;
    m_tcpTimeoutSpin->setRange(1, 300);
    m_tcpTimeoutSpin->setValue(30);
    m_tcpTimeoutSpin->setSuffix(" 秒");
    layout->addRow("连接超时:", m_tcpTimeoutSpin);

    m_tabWidget->addTab(m_tcpTab, "TCP");
}

void ConnectionDialog::setupADBTab()
{
    m_adbTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_adbTab);

    m_adbDeviceCombo = new QComboBox;
    m_refreshADBBtn = new QPushButton("刷新设备");
    QHBoxLayout *deviceLayout = new QHBoxLayout;
    deviceLayout->addWidget(m_adbDeviceCombo);
    deviceLayout->addWidget(m_refreshADBBtn);
    layout->addRow("设备:", deviceLayout);

    m_adbCommandEdit = new QLineEdit;
    m_adbCommandEdit->setPlaceholderText("可选：自定义ADB路径");
    layout->addRow("ADB路径:", m_adbCommandEdit);

    m_tabWidget->addTab(m_adbTab, "ADB");

    connect(m_refreshADBBtn, &QPushButton::clicked, this, &ConnectionDialog::onADBDeviceRefresh);
}

void ConnectionDialog::setupNetworkTab()
{
    m_networkTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_networkTab);

    m_networkHostEdit = new QLineEdit;
    layout->addRow("主机:", m_networkHostEdit);

    m_networkPortSpin = new QSpinBox;
    m_networkPortSpin->setRange(1, 65535);
    m_networkPortSpin->setValue(8080);
    layout->addRow("端口:", m_networkPortSpin);

    m_networkProtocolCombo = new QComboBox;
    m_networkProtocolCombo->addItems({"TCP", "UDP"});
    layout->addRow("协议:", m_networkProtocolCombo);

    m_tabWidget->addTab(m_networkTab, "网络");
}

void ConnectionDialog::setupFTPTab()
{
    m_ftpTab = new QWidget;
    QFormLayout *layout = new QFormLayout(m_ftpTab);

    m_ftpHostEdit = new QLineEdit;
    layout->addRow("主机:", m_ftpHostEdit);

    m_ftpPortSpin = new QSpinBox;
    m_ftpPortSpin->setRange(1, 65535);
    m_ftpPortSpin->setValue(21);
    layout->addRow("端口:", m_ftpPortSpin);

    m_ftpUserEdit = new QLineEdit;
    layout->addRow("用户名:", m_ftpUserEdit);

    m_ftpPasswordEdit = new QLineEdit;
    m_ftpPasswordEdit->setEchoMode(QLineEdit::Password);
    layout->addRow("密码:", m_ftpPasswordEdit);

    m_ftpPassiveModeCheck = new QCheckBox("被动模式");
    m_ftpPassiveModeCheck->setChecked(true);
    layout->addRow(m_ftpPassiveModeCheck);

    m_tabWidget->addTab(m_ftpTab, "FTP");
}

void ConnectionDialog::setupHistoryTab()
{
    m_historyTab = new QWidget;
    QVBoxLayout *layout = new QVBoxLayout(m_historyTab);

    m_historyCombo = new QComboBox;
    layout->addWidget(m_historyCombo);

    QHBoxLayout *historyBtnLayout = new QHBoxLayout;
    m_saveHistoryBtn = new QPushButton("保存当前");
    m_deleteHistoryBtn = new QPushButton("删除选中");
    historyBtnLayout->addWidget(m_saveHistoryBtn);
    historyBtnLayout->addWidget(m_deleteHistoryBtn);
    historyBtnLayout->addStretch();
    layout->addLayout(historyBtnLayout);

    m_historyDetailsEdit = new QTextEdit;
    m_historyDetailsEdit->setReadOnly(true);
    layout->addWidget(m_historyDetailsEdit);

    m_tabWidget->addTab(m_historyTab, "历史记录");

    connect(m_historyCombo, QOverload<int>::of(&QComboBox::currentIndexChanged),
            this, &ConnectionDialog::onHistoryItemSelected);
    connect(m_saveHistoryBtn, &QPushButton::clicked, this, &ConnectionDialog::onSaveToHistory);
    connect(m_deleteHistoryBtn, &QPushButton::clicked, this, &ConnectionDialog::onDeleteFromHistory);
}

void ConnectionDialog::setupButtons()
{
    QHBoxLayout *buttonLayout = new QHBoxLayout;

    m_testBtn = new QPushButton("测试连接");
    m_connectBtn = new QPushButton("连接");
    m_cancelBtn = new QPushButton("取消");
    m_advancedBtn = new QPushButton("高级选项");

    buttonLayout->addWidget(m_testBtn);
    buttonLayout->addWidget(m_advancedBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_connectBtn);
    buttonLayout->addWidget(m_cancelBtn);

    m_mainLayout->addLayout(buttonLayout);

    // 状态显示
    m_statusLabel = new QLabel;
    m_progressBar = new QProgressBar;
    m_progressBar->setVisible(false);

    m_mainLayout->addWidget(m_statusLabel);
    m_mainLayout->addWidget(m_progressBar);

    // 连接信号
    connect(m_testBtn, &QPushButton::clicked, this, &ConnectionDialog::testConnection);
    connect(m_connectBtn, &QPushButton::clicked, this, &ConnectionDialog::connectToDevice);
    connect(m_cancelBtn, &QPushButton::clicked, this, &QDialog::reject);
    connect(m_advancedBtn, &QPushButton::clicked, this, &ConnectionDialog::onAdvancedToggled);

    // 测试超时定时器
    m_testTimer = new QTimer(this);
    m_testTimer->setSingleShot(true);
    connect(m_testTimer, &QTimer::timeout, this, &ConnectionDialog::onTestTimeout);
}

void ConnectionDialog::setConnectionType(ConnectionType type)
{
    m_currentType = type;
    m_tabWidget->setCurrentIndex(static_cast<int>(type));
}

ConnectionDialog::ConnectionType ConnectionDialog::connectionType() const
{
    return static_cast<ConnectionType>(m_tabWidget->currentIndex());
}

QVariantMap ConnectionDialog::getConnectionParams() const
{
    QVariantMap params;

    ConnectionType type = connectionType();
    // 转换为字符串而不是整数
    QString typeStr;
    switch (type)
    {
    case Serial:
        typeStr = "serial";
        break;
    case TCP:
        typeStr = "tcp";
        break;
    case ADB:
        typeStr = "adb";
        break;
    case Network:
        typeStr = "network";
        break;
    case FTP:
        typeStr = "ftp";
        break;
    default:
        typeStr = "serial";
        break;
    }
    params["type"] = typeStr;

    switch (type)
    {
    case Serial:
        params.unite(getSerialParams());
        break;
    case TCP:
        params.unite(getTCPParams());
        break;
    case ADB:
        params.unite(getADBParams());
        break;
    case Network:
        params.unite(getNetworkParams());
        break;
    case FTP:
        params.unite(getFTPParams());
        break;
    }

    return params;
}

void ConnectionDialog::setConnectionParams(const QVariantMap &params)
{
    // 支持字符串和整数两种格式
    ConnectionType type = Serial; // 默认值
    QVariant typeValue = params.value("type", "serial");

    if (typeValue.type() == QVariant::String)
    {
        QString typeStr = typeValue.toString().toLower();
        if (typeStr == "serial" || typeStr == "串口")
            type = Serial;
        else if (typeStr == "tcp" || typeStr == "网络")
            type = TCP;
        else if (typeStr == "adb")
            type = ADB;
        else if (typeStr == "network" || typeStr == "网络")
            type = Network;
        else if (typeStr == "ftp")
            type = FTP;
    }
    else
    {
        // 兼容旧的整数格式
        type = static_cast<ConnectionType>(typeValue.toInt());
    }

    setConnectionType(type);

    switch (type)
    {
    case Serial:
        setSerialParams(params);
        break;
    case TCP:
        setTCPParams(params);
        break;
    case ADB:
        setADBParams(params);
        break;
    case Network:
        setNetworkParams(params);
        break;
    case FTP:
        setFTPParams(params);
        break;
    }
}

// 简化的实现方法
QVariantMap ConnectionDialog::getSerialParams() const
{
    QVariantMap params;

    // 提取端口名（去掉描述信息）
    QString portText = m_serialPortCombo->currentText();
    QString portName = portText;

    // 如果包含描述信息，提取端口名部分
    if (portText.contains(" ("))
    {
        portName = portText.split(" (").first();
    }

    params["port"] = portName;

    // 验证波特率
    bool ok;
    int baudRate = m_baudRateCombo->currentText().toInt(&ok);
    if (!ok || baudRate < 9600 || baudRate > 3000000)
    {
        baudRate = 115200; // 使用默认值
    }
    params["baud_rate"] = baudRate;

    params["data_bits"] = m_dataBitsCombo->currentText().toInt();
    params["stop_bits"] = m_stopBitsCombo->currentText();
    params["parity"] = m_parityCombo->currentText();
    params["flow_control"] = m_flowControlCombo->currentText();
    return params;
}

QVariantMap ConnectionDialog::getTCPParams() const
{
    QVariantMap params;
    params["host"] = m_tcpHostEdit->text();
    params["port"] = m_tcpPortSpin->value();
    params["protocol"] = m_tcpProtocolCombo->currentText();
    params["line_ending"] = m_tcpLineEndingEdit->text().replace("\\r", "\r").replace("\\n", "\n");
    params["encoding"] = m_tcpEncodingCombo->currentText();
    params["keep_alive"] = m_tcpKeepAliveCheck->isChecked();
    params["timeout"] = m_tcpTimeoutSpin->value();
    return params;
}

QVariantMap ConnectionDialog::getADBParams() const
{
    QVariantMap params;
    params["device"] = m_adbDeviceCombo->currentText();
    params["adb_path"] = m_adbCommandEdit->text();
    return params;
}

QVariantMap ConnectionDialog::getNetworkParams() const
{
    QVariantMap params;
    params["host"] = m_networkHostEdit->text();
    params["port"] = m_networkPortSpin->value();
    params["protocol"] = m_networkProtocolCombo->currentText();
    return params;
}

QVariantMap ConnectionDialog::getFTPParams() const
{
    QVariantMap params;
    params["host"] = m_ftpHostEdit->text();
    params["port"] = m_ftpPortSpin->value();
    params["username"] = m_ftpUserEdit->text();
    params["password"] = m_ftpPasswordEdit->text();
    params["passive_mode"] = m_ftpPassiveModeCheck->isChecked();
    return params;
}

void ConnectionDialog::setSerialParams(const QVariantMap &params)
{
    m_serialPortCombo->setCurrentText(params.value("port").toString());
    m_baudRateCombo->setCurrentText(params.value("baud_rate", 115200).toString());
    m_dataBitsCombo->setCurrentText(params.value("data_bits", 8).toString());
    m_stopBitsCombo->setCurrentText(params.value("stop_bits", "1").toString());
    m_parityCombo->setCurrentText(params.value("parity", "无").toString());
    m_flowControlCombo->setCurrentText(params.value("flow_control", "无").toString());
}

void ConnectionDialog::setTCPParams(const QVariantMap &params)
{
    m_tcpHostEdit->setText(params.value("host").toString());
    m_tcpPortSpin->setValue(params.value("port", 8080).toInt());
    m_tcpProtocolCombo->setCurrentText(params.value("protocol", "TCP").toString());
    QString lineEnding = params.value("line_ending", "\r\n").toString();
    lineEnding = lineEnding.replace("\r", "\\r").replace("\n", "\\n");
    m_tcpLineEndingEdit->setText(lineEnding);
    m_tcpEncodingCombo->setCurrentText(params.value("encoding", "UTF-8").toString());
    m_tcpKeepAliveCheck->setChecked(params.value("keep_alive", false).toBool());
    m_tcpTimeoutSpin->setValue(params.value("timeout", 30).toInt());
}

void ConnectionDialog::setADBParams(const QVariantMap &params)
{
    m_adbDeviceCombo->setCurrentText(params.value("device").toString());
    m_adbCommandEdit->setText(params.value("adb_path").toString());
}

void ConnectionDialog::setNetworkParams(const QVariantMap &params)
{
    m_networkHostEdit->setText(params.value("host").toString());
    m_networkPortSpin->setValue(params.value("port", 8080).toInt());
    m_networkProtocolCombo->setCurrentText(params.value("protocol", "TCP").toString());
}

void ConnectionDialog::setFTPParams(const QVariantMap &params)
{
    m_ftpHostEdit->setText(params.value("host").toString());
    m_ftpPortSpin->setValue(params.value("port", 21).toInt());
    m_ftpUserEdit->setText(params.value("username").toString());
    m_ftpPasswordEdit->setText(params.value("password").toString());
    m_ftpPassiveModeCheck->setChecked(params.value("passive_mode", true).toBool());
}

void ConnectionDialog::testConnection()
{
    m_isTesting = true;
    m_testBtn->setEnabled(false);
    m_progressBar->setVisible(true);
    m_statusLabel->setText("正在测试连接...");

    QVariantMap params = getConnectionParams();
    emit testConnectionRequested(params);

    m_testTimer->start(10000); // 10秒超时
}

void ConnectionDialog::connectToDevice()
{
    QVariantMap params = getConnectionParams();
    emit connectionRequested(params);
    accept();
}

void ConnectionDialog::refreshCurrentDevices()
{
    // 根据当前选中的标签页刷新对应的设备列表
    ConnectionType currentType = connectionType();

    switch (currentType)
    {
    case Serial:
        onSerialPortRefresh();
        break;
    case ADB:
        onADBDeviceRefresh();
        break;
    default:
        // 其他类型不需要刷新设备列表
        break;
    }
}

void ConnectionDialog::onTestResult(bool success, const QString &message)
{
    m_isTesting = false;
    m_testBtn->setEnabled(true);
    m_progressBar->setVisible(false);

    if (success)
    {
        m_statusLabel->setText("✅ " + message);
    }
    else
    {
        m_statusLabel->setText("❌ " + message);
    }
}

void ConnectionDialog::onSerialPortRefresh()
{
    m_statusLabel->setText("🔄 正在刷新串口列表...");
    m_refreshPortsBtn->setEnabled(false);

    m_serialPortCombo->clear();

    // 获取可用的串口列表
    QStringList availablePorts = getAvailableSerialPorts();

    if (availablePorts.isEmpty())
    {
        m_serialPortCombo->addItem("未检测到串口设备");
        m_statusLabel->setText("⚠️ 未检测到可用的串口设备");
    }
    else
    {
        m_serialPortCombo->addItems(availablePorts);
        m_statusLabel->setText(QString("✅ 检测到 %1 个串口设备").arg(availablePorts.size()));
    }

    m_refreshPortsBtn->setEnabled(true);
}

void ConnectionDialog::onADBDeviceRefresh()
{
    m_statusLabel->setText("🔄 正在刷新ADB设备列表...");
    m_refreshADBBtn->setEnabled(false);

    m_adbDeviceCombo->clear();

    // 获取可用的ADB设备列表
    QStringList availableDevices = getAvailableADBDevices();

    if (availableDevices.isEmpty() ||
        (availableDevices.size() == 1 && availableDevices.first().contains("未检测到")))
    {
        m_adbDeviceCombo->addItems(availableDevices);
        m_statusLabel->setText("⚠️ 未检测到可用的ADB设备");
    }
    else
    {
        m_adbDeviceCombo->addItems(availableDevices);
        m_statusLabel->setText(QString("✅ 检测到 %1 个ADB设备").arg(availableDevices.size()));
    }

    m_refreshADBBtn->setEnabled(true);
}

void ConnectionDialog::onAdvancedToggled(bool /*show*/)
{
    // 简化实现 - 这里可以显示/隐藏高级选项
    m_isAdvancedVisible = !m_isAdvancedVisible;
    m_advancedBtn->setText(m_isAdvancedVisible ? "隐藏高级选项" : "显示高级选项");
}

void ConnectionDialog::onHistoryItemSelected()
{
    // 简化实现
    int index = m_historyCombo->currentIndex();
    if (index >= 0 && index < m_connectionHistory.size())
    {
        QVariantMap params = m_connectionHistory[index].toMap();
        setConnectionParams(params);

        QString details = QString("类型: %1\n主机: %2\n端口: %3")
                              .arg(params.value("type").toString())
                              .arg(params.value("host").toString())
                              .arg(params.value("port").toString());
        m_historyDetailsEdit->setText(details);
    }
}

void ConnectionDialog::onSaveToHistory()
{
    QVariantMap params = getConnectionParams();
    addToHistory(params);
}

void ConnectionDialog::onDeleteFromHistory()
{
    int index = m_historyCombo->currentIndex();
    if (index >= 0 && index < m_connectionHistory.size())
    {
        m_connectionHistory.removeAt(index);
        m_historyCombo->removeItem(index);
        saveHistory();
    }
}

void ConnectionDialog::onTestTimeout()
{
    if (m_isTesting)
    {
        onTestResult(false, "测试连接超时");
    }
}

void ConnectionDialog::addToHistory(const QVariantMap &params)
{
    m_connectionHistory.prepend(QVariant::fromValue(params));

    // 限制历史记录数量
    while (m_connectionHistory.size() > 20)
    {
        m_connectionHistory.removeLast();
    }

    saveHistory();
    loadHistory();
}

void ConnectionDialog::loadHistory()
{
    // 简化实现 - 实际应该从配置文件加载
    m_historyCombo->clear();
    for (int i = 0; i < m_connectionHistory.size(); ++i)
    {
        QVariantMap params = m_connectionHistory[i].toMap();
        QString name = QString("%1:%2").arg(params.value("host").toString()).arg(params.value("port").toString());
        m_historyCombo->addItem(name);
    }
}

void ConnectionDialog::saveHistory()
{
    // 简化实现 - 实际应该保存到配置文件
}

void ConnectionDialog::onConnectionTypeChanged()
{
    // 连接类型改变时的处理逻辑
    ConnectionType newType = connectionType();

    if (newType != m_currentType)
    {
        m_currentType = newType;

        // 清空状态信息
        m_statusLabel->setText("请选择连接参数");
        m_progressBar->setVisible(false);

        // 自动刷新当前类型的设备列表
        QTimer::singleShot(50, this, &ConnectionDialog::refreshCurrentDevices);
    }
}

// 设备检测方法实现
QStringList ConnectionDialog::getAvailableSerialPorts()
{
    QStringList portList;

    // 使用Qt的QSerialPortInfo获取可用串口
    const auto serialPortInfos = QSerialPortInfo::availablePorts();

    for (const QSerialPortInfo &portInfo : serialPortInfos)
    {
        QString portName = portInfo.portName();
        QString description = portInfo.description();
        QString manufacturer = portInfo.manufacturer();

        // 构建显示名称
        QString displayName = portName;
        if (!description.isEmpty())
        {
            displayName += QString(" (%1)").arg(description);
        }
        if (!manufacturer.isEmpty() && manufacturer != description)
        {
            displayName += QString(" [%1]").arg(manufacturer);
        }

        portList.append(displayName);

        qDebug() << "Found serial port:" << portName
                 << "Description:" << description
                 << "Manufacturer:" << manufacturer;
    }

    // 按端口名排序
    portList.sort();

    return portList;
}

QStringList ConnectionDialog::getAvailableADBDevices()
{
    QStringList deviceList;

    // 使用QProcess执行adb devices命令
    QProcess adbProcess;
    adbProcess.setProgram("adb");
    adbProcess.setArguments({"devices"});

    // 设置超时时间
    adbProcess.start();
    if (!adbProcess.waitForFinished(1000)) // 减少到1秒超时
    {
        qDebug() << "ADB command timeout or failed to start";
        deviceList.append("ADB命令执行失败或超时");
        return deviceList;
    }

    // 解析输出
    QString output = QString::fromUtf8(adbProcess.readAllStandardOutput());
    QStringList lines = output.split('\n', QString::SkipEmptyParts);

    for (const QString &line : lines)
    {
        // 跳过标题行
        if (line.contains("List of devices attached"))
        {
            continue;
        }

        // 解析设备行格式: "device_id    device_status"
        QStringList parts = line.split('\t', QString::SkipEmptyParts);
        if (parts.size() >= 2)
        {
            QString deviceId = parts[0].trimmed();
            QString status = parts[1].trimmed();

            if (status == "device")
            {
                deviceList.append(deviceId);
                qDebug() << "Found ADB device:" << deviceId;
            }
            else if (status == "offline")
            {
                deviceList.append(deviceId + " (离线)");
                qDebug() << "Found ADB device (offline):" << deviceId;
            }
            else if (status == "unauthorized")
            {
                deviceList.append(deviceId + " (未授权)");
                qDebug() << "Found ADB device (unauthorized):" << deviceId;
            }
        }
    }

    if (deviceList.isEmpty())
    {
        deviceList.append("未检测到ADB设备");
    }

    return deviceList;
}

void ConnectionDialog::showEvent(QShowEvent *event)
{
    QDialog::showEvent(event);

    // 对话框显示时自动刷新当前标签页的设备列表
    QTimer::singleShot(100, this, &ConnectionDialog::refreshCurrentDevices);
}
