#include "SerialConnection.h"
#include <QDebug>
#include <QThread>
#include <QTextCodec>

SerialConnection::SerialConnection(QObject *parent)
    : QObject(parent), m_serialPort(nullptr), m_baudRate(115200), m_dataBits(8), m_stopBits(1.0), m_parity("None"), m_flowControl("None"), m_autoLogin(false), m_loginState(NotLoggedIn), m_loginTimer(nullptr), m_pendingAction(NoAction), m_readTimeout(3000), m_writeTimeout(3000)
{
    // 初始化串口对象
    m_serialPort = new QSerialPort(this);

    // 连接信号
    QObject::connect(m_serialPort, &QSerialPort::readyRead,
                     this, &SerialConnection::onReadyRead);
    QObject::connect(m_serialPort, QOverload<QSerialPort::SerialPortError>::of(&QSerialPort::error),
                     this, &SerialConnection::onErrorOccurred);

    // 初始化登录定时器
    m_loginTimer = new QTimer(this);
    m_loginTimer->setSingleShot(true);
    m_loginTimer->setInterval(10000); // 10秒超时
    QObject::connect(m_loginTimer, &QTimer::timeout, this, &SerialConnection::onLoginTimeout);

    // 初始化延迟执行定时器
    m_delayTimer = new QTimer(this);
    m_delayTimer->setSingleShot(true);
    QObject::connect(m_delayTimer, &QTimer::timeout, this, &SerialConnection::onDelayedAction);

    // 初始化登录提示符
    m_loginPrompts << "login:" << "username:" << "user:";
    m_passwordPrompts << "password:" << "passwd:";
    m_commandPrompts << "# " << "$ " << "> " << "~$ " << "~# ";
}

SerialConnection::~SerialConnection()
{
    disconnect();
}

bool SerialConnection::connect(const QVariantMap &params)
{
    qDebug() << "SerialConnection::connect() - 开始串口连接";

    bool success = false;
    bool shouldEmitConnected = false;

    {
        QMutexLocker locker(&m_mutex);

        if (m_serialPort->isOpen())
        {
            qDebug() << "SerialConnection::connect() - 断开现有串口连接";
            disconnect();
        }

        // 解析连接参数 - 兼容多种参数名格式
        m_portName = params.value("port").toString();
        // 兼容 baud_rate 和 baudRate 两种格式
        m_baudRate = params.value("baud_rate", params.value("baudRate", 115200)).toInt();
        // 兼容 data_bits 和 dataBits 两种格式
        m_dataBits = params.value("data_bits", params.value("dataBits", 8)).toInt();
        // 兼容 stop_bits 和 stopBits 两种格式
        m_stopBits = params.value("stop_bits", params.value("stopBits", 1.0)).toFloat();
        m_parity = params.value("parity", "None").toString();
        // 兼容 flow_control 和 flowControl 两种格式
        m_flowControl = params.value("flow_control", params.value("flowControl", "None")).toString();

        // 登录参数
        m_username = params.value("username").toString();
        m_password = params.value("password").toString();
        m_autoLogin = !m_username.isEmpty() && !m_password.isEmpty();

        // 配置串口
        m_serialPort->setPortName(m_portName);
        m_serialPort->setBaudRate(intToBaudRate(m_baudRate));
        m_serialPort->setDataBits(intToDataBits(m_dataBits));
        m_serialPort->setStopBits(floatToStopBits(m_stopBits));
        m_serialPort->setParity(stringToParity(m_parity));
        m_serialPort->setFlowControl(stringToFlowControl(m_flowControl));

        // 尝试打开串口
        qDebug() << "SerialConnection::connect() - 尝试打开串口:" << m_portName;
        if (!m_serialPort->open(QIODevice::ReadWrite))
        {
            qDebug() << "SerialConnection::connect() - 串口打开失败:" << m_serialPort->errorString();
            emit errorOccurred(QString("无法打开串口 %1: %2")
                                   .arg(m_portName)
                                   .arg(m_serialPort->errorString()));
            return false;
        }
        qDebug() << "SerialConnection::connect() - 串口打开成功";

        // 清空缓冲区
        qDebug() << "SerialConnection::connect() - 清空缓冲区";
        m_dataBuffer.clear();
        m_lineBuffer.clear();
        m_loginState = NotLoggedIn;

        success = true;
        shouldEmitConnected = true;

        // 如果启用自动登录，延迟发送回车获取提示符
        if (m_autoLogin)
        {
            qDebug() << "SerialConnection::connect() - 启用自动登录";
            m_pendingAction = SendInitialCarriageReturn;
            m_delayTimer->start(500); // 500ms后执行
            m_loginTimer->start();
        }
    } // 释放互斥锁

    // 在锁外发出信号
    if (shouldEmitConnected)
    {
        qDebug() << "SerialConnection::connect() - 发出connected信号";
        emit connected();
        qDebug() << "SerialConnection::connect() - connected信号发出完成";
    }

    qDebug() << "SerialConnection::connect() - 连接完成，返回:" << success;
    return success;
}

void SerialConnection::disconnect()
{
    qDebug() << "SerialConnection::disconnect() - 开始断开串口连接";

    bool shouldEmitDisconnected = false;

    {
        QMutexLocker locker(&m_mutex);

        if (m_serialPort && m_serialPort->isOpen())
        {
            qDebug() << "SerialConnection::disconnect() - 关闭串口";
            m_serialPort->close();
            shouldEmitDisconnected = true;
        }

        if (m_loginTimer)
        {
            qDebug() << "SerialConnection::disconnect() - 停止登录定时器";
            m_loginTimer->stop();
        }

        m_loginState = NotLoggedIn;
    } // 释放互斥锁

    // 在锁外发出信号
    if (shouldEmitDisconnected)
    {
        qDebug() << "SerialConnection::disconnect() - 发出disconnected信号";
        emit disconnected();
        qDebug() << "SerialConnection::disconnect() - disconnected信号发出完成";
    }

    qDebug() << "SerialConnection::disconnect() - 断开完成";
}

bool SerialConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return isConnectedInternal();
}

bool SerialConnection::isConnectedInternal() const
{
    return m_serialPort && m_serialPort->isOpen();
}

bool SerialConnection::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);

    if (!m_serialPort || !m_serialPort->isOpen())
    {
        return false;
    }

    qint64 bytesWritten = m_serialPort->write(data);
    if (bytesWritten == -1)
    {
        emit errorOccurred("数据发送失败: " + m_serialPort->errorString());
        return false;
    }

    // 确保数据立即发送
    if (!m_serialPort->flush())
    {
        qWarning() << "串口数据刷新失败";
    }

    return bytesWritten == data.size();
}

bool SerialConnection::sendCommand(const QString &command)
{
    qDebug() << "SerialConnection::sendCommand() - 发送命令:" << command;

    QString cmd = command;
    if (!cmd.endsWith("\r\n") && !cmd.endsWith("\n") && !cmd.endsWith("\r"))
    {
        cmd += "\r\n";
    }

    bool result = sendData(cmd.toUtf8());
    qDebug() << "SerialConnection::sendCommand() - 发送结果:" << result;
    return result;
}

void SerialConnection::onReadyRead()
{
    QByteArray data = m_serialPort->readAll();
    qDebug() << "SerialConnection::onReadyRead() - 收到串口数据:" << QString::fromLatin1(data.left(50));
    qDebug() << "SerialConnection::onReadyRead() - 原始数据hex:" << data.left(50).toHex();

    m_dataBuffer.append(data);

    processReceivedData();
}

void SerialConnection::processReceivedData()
{
    // 直接发送原始数据，不进行行处理
    // 这样可以避免重复处理和显示问题
    if (!m_dataBuffer.isEmpty())
    {
        emit dataReceived(m_dataBuffer);

        // 保存数据用于登录流程处理，使用更智能的编码检测
        QString dataStr;

        // 尝试多种编码方式，优先使用UTF-8
        QTextCodec *utf8Codec = QTextCodec::codecForName("UTF-8");
        QTextCodec *gbkCodec = QTextCodec::codecForName("GBK");
        QTextCodec *latin1Codec = QTextCodec::codecForName("ISO-8859-1");
        Q_UNUSED(latin1Codec);

        // 首先尝试UTF-8
        if (utf8Codec)
        {
            QTextCodec::ConverterState state;
            dataStr = utf8Codec->toUnicode(m_dataBuffer.constData(), m_dataBuffer.size(), &state);
            if (state.invalidChars == 0)
            {
                // UTF-8解码成功
                qDebug() << "SerialConnection::processReceivedData() - 使用UTF-8编码";
            }
            else
            {
                // UTF-8解码失败，尝试GBK
                if (gbkCodec)
                {
                    dataStr = gbkCodec->toUnicode(m_dataBuffer);
                    qDebug() << "SerialConnection::processReceivedData() - 使用GBK编码";
                }
                else
                {
                    // 最后使用Latin-1作为兜底
                    dataStr = QString::fromLatin1(m_dataBuffer);
                    qDebug() << "SerialConnection::processReceivedData() - 使用Latin-1编码";
                }
            }
        }
        else
        {
            // 如果没有UTF-8编解码器，直接使用Latin-1
            dataStr = QString::fromLatin1(m_dataBuffer);
            qDebug() << "SerialConnection::processReceivedData() - 使用Latin-1编码（默认）";
        }

        // 处理登录流程
        if (m_autoLogin)
        {
            handleLoginFlow(dataStr);
        }

        // 检查登录状态
        if (m_loginState != LoggedIn)
        {
            // 检查命令提示符
            if (isCommandPrompt(dataStr))
            {
                m_loginState = LoggedIn;
                m_loginTimer->stop();
                emit loginCompleted(true);
            }
        }

        m_dataBuffer.clear();
    }
}

bool SerialConnection::isLoginPrompt(const QString &text) const
{
    QString lowerText = text.toLower();
    for (const QString &prompt : m_loginPrompts)
    {
        if (lowerText.contains(prompt))
        {
            return true;
        }
    }
    for (const QString &prompt : m_passwordPrompts)
    {
        if (lowerText.contains(prompt))
        {
            return true;
        }
    }
    return false;
}

bool SerialConnection::isCommandPrompt(const QString &text) const
{
    for (const QString &prompt : m_commandPrompts)
    {
        if (text.endsWith(prompt))
        {
            return true;
        }
    }
    return false;
}

void SerialConnection::handleLoginFlow(const QString &data)
{
    QString lowerData = data.toLower();

    // 检查是否是用户名提示
    bool isUsernamePrompt = false;
    for (const QString &prompt : m_loginPrompts)
    {
        if (lowerData.contains(prompt))
        {
            isUsernamePrompt = true;
            break;
        }
    }

    // 检查是否是密码提示
    bool isPasswordPrompt = false;
    for (const QString &prompt : m_passwordPrompts)
    {
        if (lowerData.contains(prompt))
        {
            isPasswordPrompt = true;
            break;
        }
    }

    // 处理登录状态机
    if (isUsernamePrompt && m_loginState == NotLoggedIn)
    {
        m_loginState = WaitingForLogin;
        emit loginPromptDetected("用户名提示");

        // 延迟发送用户名
        m_pendingAction = SendUsername;
        m_pendingData = m_username;
        m_delayTimer->start(200);
    }
    else if (isPasswordPrompt && (m_loginState == WaitingForLogin || m_loginState == NotLoggedIn))
    {
        m_loginState = WaitingForPassword;
        emit loginPromptDetected("密码提示");

        // 延迟发送密码
        m_pendingAction = SendPassword;
        m_pendingData = m_password;
        m_delayTimer->start(200);
    }
    else if (lowerData.contains("incorrect") || lowerData.contains("failed") || lowerData.contains("denied"))
    {
        m_loginState = LoginFailed;
        m_loginTimer->stop();
        emit loginCompleted(false);
        emit errorOccurred("登录失败: 用户名或密码错误");
    }
    else if (isCommandPrompt(data))
    {
        m_loginState = LoggedIn;
        m_loginTimer->stop();
        emit loginCompleted(true);
    }
}

void SerialConnection::onErrorOccurred(QSerialPort::SerialPortError error)
{
    if (error != QSerialPort::NoError)
    {
        QString errorString;
        switch (error)
        {
        case QSerialPort::DeviceNotFoundError:
            errorString = "设备未找到";
            break;
        case QSerialPort::PermissionError:
            errorString = "权限错误";
            break;
        case QSerialPort::OpenError:
            errorString = "打开失败";
            break;
        case QSerialPort::WriteError:
            errorString = "写入错误";
            break;
        case QSerialPort::ReadError:
            errorString = "读取错误";
            break;
        case QSerialPort::ResourceError:
            errorString = "资源错误";
            break;
        case QSerialPort::UnsupportedOperationError:
            errorString = "不支持的操作";
            break;
        case QSerialPort::TimeoutError:
            errorString = "超时错误";
            break;
        default:
            errorString = "未知错误";
            break;
        }

        emit errorOccurred(QString("串口错误: %1").arg(errorString));
    }
}

void SerialConnection::onLoginTimeout()
{
    if (m_loginState != LoggedIn)
    {
        m_loginState = LoginFailed;
        emit loginCompleted(false);
        emit errorOccurred("登录超时");
    }
}

QStringList SerialConnection::getAvailablePorts()
{
    QStringList ports;
    const auto portInfos = QSerialPortInfo::availablePorts();
    for (const QSerialPortInfo &portInfo : portInfos)
    {
        QString portDescription = QString("%1 - %2")
                                      .arg(portInfo.portName())
                                      .arg(portInfo.description());
        ports << portDescription;
    }
    return ports;
}

// 辅助转换函数
QSerialPort::BaudRate SerialConnection::intToBaudRate(int baudRate) const
{
    switch (baudRate)
    {
    case 1200:
        return QSerialPort::Baud1200;
    case 2400:
        return QSerialPort::Baud2400;
    case 4800:
        return QSerialPort::Baud4800;
    case 9600:
        return QSerialPort::Baud9600;
    case 19200:
        return QSerialPort::Baud19200;
    case 38400:
        return QSerialPort::Baud38400;
    case 57600:
        return QSerialPort::Baud57600;
    case 115200:
        return QSerialPort::Baud115200;
    default:
        // 对于自定义波特率，使用setBaudRate(int)方法
        return static_cast<QSerialPort::BaudRate>(baudRate);
    }
}

QSerialPort::DataBits SerialConnection::intToDataBits(int dataBits) const
{
    switch (dataBits)
    {
    case 5:
        return QSerialPort::Data5;
    case 6:
        return QSerialPort::Data6;
    case 7:
        return QSerialPort::Data7;
    case 8:
        return QSerialPort::Data8;
    default:
        return QSerialPort::Data8;
    }
}

QSerialPort::StopBits SerialConnection::floatToStopBits(float stopBits) const
{
    if (stopBits == 1.0)
        return QSerialPort::OneStop;
    else if (stopBits == 1.5)
        return QSerialPort::OneAndHalfStop;
    else if (stopBits == 2.0)
        return QSerialPort::TwoStop;
    else
        return QSerialPort::OneStop;
}

QSerialPort::Parity SerialConnection::stringToParity(const QString &parity) const
{
    QString p = parity.toLower();
    if (p == "even")
        return QSerialPort::EvenParity;
    else if (p == "odd")
        return QSerialPort::OddParity;
    else if (p == "mark")
        return QSerialPort::MarkParity;
    else if (p == "space")
        return QSerialPort::SpaceParity;
    else
        return QSerialPort::NoParity;
}

QSerialPort::FlowControl SerialConnection::stringToFlowControl(const QString &flowControl) const
{
    QString fc = flowControl.toLower();
    if (fc == "rts/cts")
        return QSerialPort::HardwareControl;
    else if (fc == "xon/xoff")
        return QSerialPort::SoftwareControl;
    else
        return QSerialPort::NoFlowControl;
}

void SerialConnection::onDelayedAction()
{
    switch (m_pendingAction)
    {
    case SendInitialCarriageReturn:
        sendData("\r\n");
        break;
    case SendUsername:
        sendCommand(m_pendingData);
        break;
    case SendPassword:
        sendCommand(m_pendingData);
        break;
    default:
        break;
    }

    m_pendingAction = NoAction;
    m_pendingData.clear();
}
