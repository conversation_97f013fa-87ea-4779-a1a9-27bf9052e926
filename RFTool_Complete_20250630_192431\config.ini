[General]
# 快捷命令配置
quick_command_1=ls -la
quick_command_1_name=日志保存
quick_command_2=pwd
quick_command_2_name=路径
quick_command_3=whoami
quick_command_3_name=用户

# 应用程序配置
app\version=2.0.0
app\language=zh_CN
app\theme=default

# 窗口配置
window\width=1200
window\height=800
window\maximized=false

# 连接配置
connection\timeout=30
connection\auto_reconnect=false
connection\max_reconnect_attempts=3

# 日志配置
log\timestamp_enabled=true
log\timestamp_format=yyyy-MM-dd hh:mm:ss
log\echo_enabled=true
log\file_enabled=false
log\file_path=
log\auto_save=true
log\auto_save_interval=30
log\level=1
log\max_lines=10000
log\rotation_enabled=false
log\max_file_size=10
log\max_backup_files=5

# 终端配置
terminal\timestamp_enabled=true
terminal\timestamp_format=hh:mm:ss
terminal\echo_enabled=true
terminal\max_history=1000

# 背景配置
background\type=color
background\color=#f8f9fa
background\color2=#e9ecef
background\image=
background\opacity=1.0

# 其他配置
commands=@Invalid()
background_type=color
background_color=#000000
background_color2=#333333
background_image=
background_opacity=0.99
background_image_mode=center
log_timestamp_enabled=true
log_timestamp_format=yyyy-MM-dd hh:mm:ss
log_file_enabled=true
log_file_path=C:/Users/<USER>/Desktop/rf_tool.log
log_auto_save=true
log_auto_save_interval=30
log_echo_enabled=true
log_level=1
log_max_lines=10000
log_rotation_enabled=false
log_max_file_size=10
log_max_backup_files=5
timestamp_enabled=true
