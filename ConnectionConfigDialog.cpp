#include "ConnectionConfigDialog.h"
#include <QMessageBox>
#include <QSerialPortInfo>
#include <QRegularExpression>
#include <QRegularExpressionValidator>

ConnectionConfigDialog::ConnectionConfigDialog(const QString &connectionType,
                                               const QString &currentConfig,
                                               QWidget *parent)
    : QDialog(parent), m_connectionType(connectionType), m_originalConfig(currentConfig), m_mainLayout(nullptr), m_configGroup(nullptr), m_formLayout(nullptr), m_buttonBox(nullptr), m_testButton(nullptr), m_portCombo(nullptr), m_baudRateCombo(nullptr), m_dataBitsCombo(nullptr), m_stopBitsCombo(nullptr), m_parityCombo(nullptr), m_hostEdit(nullptr), m_port<PERSON>pin(nullptr), m_adbLabel(nullptr)
{
    setWindowTitle(QString("修改%1配置").arg(connectionType));
    setModal(true);
    resize(400, 300);

    setupUI();
    loadConfig(currentConfig);
}

QString ConnectionConfigDialog::getConfig() const
{
    return buildConfig();
}

void ConnectionConfigDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 配置组
    m_configGroup = new QGroupBox(QString("%1连接配置").arg(m_connectionType));
    m_formLayout = new QFormLayout(m_configGroup);

    // 根据连接类型设置UI
    if (m_connectionType == "串口")
    {
        setupSerialUI();
    }
    else if (m_connectionType == "TCP")
    {
        setupTcpUI();
    }
    else if (m_connectionType == "ADB")
    {
        setupAdbUI();
    }

    m_mainLayout->addWidget(m_configGroup);

    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;

    m_testButton = new QPushButton("🔍 测试连接");
    connect(m_testButton, &QPushButton::clicked, this, &ConnectionConfigDialog::onTestConnection);

    m_buttonBox = new QDialogButtonBox(QDialogButtonBox::Ok | QDialogButtonBox::Cancel);
    connect(m_buttonBox, &QDialogButtonBox::accepted, this, &QDialog::accept);
    connect(m_buttonBox, &QDialogButtonBox::rejected, this, &QDialog::reject);

    buttonLayout->addWidget(m_testButton);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_buttonBox);

    m_mainLayout->addLayout(buttonLayout);

    // 设置样式
    setStyleSheet(
        "QGroupBox {"
        "    font-weight: bold;"
        "    border: 2px solid #cccccc;"
        "    border-radius: 8px;"
        "    margin-top: 1ex;"
        "    padding-top: 10px;"
        "}"
        "QGroupBox::title {"
        "    subcontrol-origin: margin;"
        "    left: 10px;"
        "    padding: 0 5px 0 5px;"
        "}"
        "QPushButton {"
        "    padding: 8px 16px;"
        "    border-radius: 4px;"
        "    font-weight: bold;"
        "}"
        "QPushButton:hover {"
        "    background-color: #e3f2fd;"
        "}");
}

void ConnectionConfigDialog::setupSerialUI()
{
    // 串口选择
    m_portCombo = new QComboBox;
    m_portCombo->setEditable(true);

    // 获取可用串口
    QStringList availablePorts;
    for (const QSerialPortInfo &info : QSerialPortInfo::availablePorts())
    {
        availablePorts << info.portName();
    }
    m_portCombo->addItems(availablePorts);

    // 波特率
    m_baudRateCombo = new QComboBox;
    m_baudRateCombo->setEditable(true);
    QStringList baudRates = {"9600", "19200", "38400", "57600", "115200", "230400", "460800", "921600"};
    m_baudRateCombo->addItems(baudRates);
    m_baudRateCombo->setCurrentText("115200");

    // 数据位
    m_dataBitsCombo = new QComboBox;
    m_dataBitsCombo->addItems({"5", "6", "7", "8"});
    m_dataBitsCombo->setCurrentText("8");

    // 停止位
    m_stopBitsCombo = new QComboBox;
    m_stopBitsCombo->addItems({"1", "1.5", "2"});
    m_stopBitsCombo->setCurrentText("1");

    // 校验位
    m_parityCombo = new QComboBox;
    m_parityCombo->addItems({"无", "奇校验", "偶校验", "标记", "空格"});
    m_parityCombo->setCurrentText("无");

    m_formLayout->addRow("串口:", m_portCombo);
    m_formLayout->addRow("波特率:", m_baudRateCombo);
    m_formLayout->addRow("数据位:", m_dataBitsCombo);
    m_formLayout->addRow("停止位:", m_stopBitsCombo);
    m_formLayout->addRow("校验位:", m_parityCombo);

    // 连接信号
    connect(m_portCombo, static_cast<void (QComboBox::*)(const QString &)>(&QComboBox::currentTextChanged),
            this, &ConnectionConfigDialog::onConfigChanged);
    connect(m_baudRateCombo, static_cast<void (QComboBox::*)(const QString &)>(&QComboBox::currentTextChanged),
            this, &ConnectionConfigDialog::onConfigChanged);
}

void ConnectionConfigDialog::setupTcpUI()
{
    // IP地址
    m_hostEdit = new QLineEdit;
    m_hostEdit->setPlaceholderText("例如: *************");

    // 端口
    m_portSpin = new QSpinBox;
    m_portSpin->setRange(1, 65535);
    m_portSpin->setValue(8080);

    m_formLayout->addRow("IP地址:", m_hostEdit);
    m_formLayout->addRow("端口:", m_portSpin);

    // 连接信号
    connect(m_hostEdit, &QLineEdit::textChanged, this, &ConnectionConfigDialog::onConfigChanged);
    connect(m_portSpin, static_cast<void (QSpinBox::*)(int)>(&QSpinBox::valueChanged),
            this, &ConnectionConfigDialog::onConfigChanged);
}

void ConnectionConfigDialog::setupAdbUI()
{
    m_adbLabel = new QLabel("ADB连接无需额外配置参数\n\n"
                            "请确保：\n"
                            "• 设备已连接并开启USB调试\n"
                            "• 已授权此计算机进行调试\n"
                            "• ADB驱动已正确安装");
    m_adbLabel->setWordWrap(true);
    m_adbLabel->setStyleSheet("QLabel { color: #666; padding: 10px; }");

    m_formLayout->addRow(m_adbLabel);

    // ADB连接禁用测试按钮
    if (m_testButton)
    {
        m_testButton->setEnabled(false);
        m_testButton->setText("ADB无需测试");
    }
}

void ConnectionConfigDialog::loadConfig(const QString &config)
{
    if (m_connectionType == "串口")
    {
        QStringList parts = config.split(',');
        if (parts.size() >= 2)
        {
            m_portCombo->setCurrentText(parts[0].trimmed());
            m_baudRateCombo->setCurrentText(parts[1].trimmed());
        }
        if (parts.size() >= 3)
            m_dataBitsCombo->setCurrentText(parts[2].trimmed());
        if (parts.size() >= 4)
            m_stopBitsCombo->setCurrentText(parts[3].trimmed());
        if (parts.size() >= 5)
            m_parityCombo->setCurrentText(parts[4].trimmed());
    }
    else if (m_connectionType == "TCP")
    {
        QStringList parts = config.split(':');
        if (parts.size() == 2)
        {
            m_hostEdit->setText(parts[0].trimmed());
            m_portSpin->setValue(parts[1].trimmed().toInt());
        }
    }
}

QString ConnectionConfigDialog::buildConfig() const
{
    if (m_connectionType == "串口")
    {
        return QString("%1,%2,%3,%4,%5")
            .arg(m_portCombo->currentText().trimmed())
            .arg(m_baudRateCombo->currentText().trimmed())
            .arg(m_dataBitsCombo->currentText())
            .arg(m_stopBitsCombo->currentText())
            .arg(m_parityCombo->currentText());
    }
    else if (m_connectionType == "TCP")
    {
        return QString("%1:%2")
            .arg(m_hostEdit->text().trimmed())
            .arg(m_portSpin->value());
    }
    else if (m_connectionType == "ADB")
    {
        return "adb_device";
    }

    return "";
}

bool ConnectionConfigDialog::validateConfig() const
{
    if (m_connectionType == "串口")
    {
        if (m_portCombo->currentText().trimmed().isEmpty())
        {
            QMessageBox::warning(const_cast<ConnectionConfigDialog *>(this),
                                 "配置错误", "请选择或输入串口名称");
            return false;
        }

        bool ok;
        int baudRate = m_baudRateCombo->currentText().toInt(&ok);
        if (!ok || baudRate <= 0)
        {
            QMessageBox::warning(const_cast<ConnectionConfigDialog *>(this),
                                 "配置错误", "请输入有效的波特率");
            return false;
        }
    }
    else if (m_connectionType == "TCP")
    {
        if (m_hostEdit->text().trimmed().isEmpty())
        {
            QMessageBox::warning(const_cast<ConnectionConfigDialog *>(this),
                                 "配置错误", "请输入IP地址");
            return false;
        }

        if (m_portSpin->value() <= 0 || m_portSpin->value() > 65535)
        {
            QMessageBox::warning(const_cast<ConnectionConfigDialog *>(this),
                                 "配置错误", "请输入有效的端口号(1-65535)");
            return false;
        }
    }

    return true;
}

void ConnectionConfigDialog::onTestConnection()
{
    if (!validateConfig())
        return;

    // TODO: 实现连接测试功能
    QMessageBox::information(this, "测试连接", "连接测试功能待实现");
}

void ConnectionConfigDialog::onConfigChanged()
{
    // 配置改变时的处理
    m_buttonBox->button(QDialogButtonBox::Ok)->setEnabled(validateConfig());
}
