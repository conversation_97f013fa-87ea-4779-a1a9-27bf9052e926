#ifndef CONNECTIONCONFIGDIALOG_H
#define CONNECTIONCONFIGDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QLabel>
#include <QLineEdit>
#include <QComboBox>
#include <QSpinBox>
#include <QPushButton>
#include <QGroupBox>
#include <QDialogButtonBox>

class ConnectionConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit ConnectionConfigDialog(const QString &connectionType, 
                                   const QString &currentConfig, 
                                   QWidget *parent = nullptr);

    QString getConfig() const;

private slots:
    void onTestConnection();
    void onConfigChanged();

private:
    void setupUI();
    void setupSerialUI();
    void setupTcpUI();
    void setupAdbUI();
    void loadConfig(const QString &config);
    QString buildConfig() const;
    bool validateConfig() const;

private:
    QString m_connectionType;
    QString m_originalConfig;
    
    // UI组件
    QVBoxLayout *m_mainLayout;
    QGroupBox *m_configGroup;
    QFormLayout *m_formLayout;
    QDialogButtonBox *m_buttonBox;
    QPushButton *m_testButton;
    
    // 串口配置
    QComboBox *m_portCombo;
    QComboBox *m_baudRateCombo;
    QComboBox *m_dataBitsCombo;
    QComboBox *m_stopBitsCombo;
    QComboBox *m_parityCombo;
    
    // TCP配置
    QLineEdit *m_hostEdit;
    QSpinBox *m_portSpin;
    
    // ADB配置
    QLabel *m_adbLabel;
};

#endif // CONNECTIONCONFIGDIALOG_H
