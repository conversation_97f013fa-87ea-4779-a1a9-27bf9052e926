@echo off
setlocal enabledelayedexpansion
echo ========================================
echo    RF Debug Tool - Complete Package
echo ========================================
echo.

color 0A
title RF Debug Tool Complete Package

REM Get timestamp
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set "YYYY=%dt:~0,4%" & set "MM=%dt:~4,2%" & set "DD=%dt:~6,2%"
set "HH=%dt:~8,2%" & set "Min=%dt:~10,2%" & set "Sec=%dt:~12,2%"
set "timestamp=%YYYY%%MM%%DD%_%HH%%Min%%Sec%"

REM Set paths
set "PROJECT_DIR=%~dp0"
set "BIN_DIR=%PROJECT_DIR%bin"
set "PACKAGE_DIR=%PROJECT_DIR%RFTool_Complete_%timestamp%"
set "EXE_NAME=RFTool_Qt.exe"

echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Project Directory: %PROJECT_DIR%
echo.

REM Check executable file
echo [1/4] Checking executable file...
if not exist "%BIN_DIR%\%EXE_NAME%" (
    echo ERROR: Executable file not found: %BIN_DIR%\%EXE_NAME%
    echo.
    echo Please compile the project in Qt Creator first:
    echo   1. Open RFTool_Qt.pro in Qt Creator
    echo   2. Select Release mode
    echo   3. Click Build - Build Project
    echo   4. Make sure compilation is successful
    echo.
    pause
    exit /b 1
)
echo OK Found executable file: %EXE_NAME%
echo.

REM Create package directory
echo [2/4] Creating package directory...
if exist "%PACKAGE_DIR%" rmdir /s /q "%PACKAGE_DIR%"
mkdir "%PACKAGE_DIR%"
echo OK Package directory created: %PACKAGE_DIR%
echo.

REM Copy main program
echo [3/4] Copying main program and basic files...
copy "%BIN_DIR%\%EXE_NAME%" "%PACKAGE_DIR%\"
if %errorlevel% neq 0 (
    echo ERROR: Failed to copy main program
    pause
    exit /b 1
)
echo OK Main program copied

REM Copy config files if they exist
if exist "%PROJECT_DIR%config.ini" (
    copy "%PROJECT_DIR%config.ini" "%PACKAGE_DIR%\" >nul
    echo OK Config file copied
)

REM Copy DLL files from bin directory
if exist "%BIN_DIR%\*.dll" (
    copy "%BIN_DIR%\*.dll" "%PACKAGE_DIR%\" >nul
    echo OK DLL files from bin directory copied
)

REM Try to find and copy Qt DLLs from common locations
echo Searching for Qt runtime libraries...
set "QT_DLLS_FOUND="

REM Common Qt DLL paths
set "QT_DLL_PATHS="
set "QT_DLL_PATHS=%QT_DLL_PATHS% C:\Qt\5.12.9\mingw73_64\bin"
set "QT_DLL_PATHS=%QT_DLL_PATHS% C:\Qt\5.15.2\mingw81_64\bin"
set "QT_DLL_PATHS=%QT_DLL_PATHS% C:\Qt\6.2.4\mingw_64\bin"
set "QT_DLL_PATHS=%QT_DLL_PATHS% C:\Qt\6.5.3\mingw_64\bin"
set "QT_DLL_PATHS=%QT_DLL_PATHS% D:\Qt\5.12.9\mingw73_64\bin"
set "QT_DLL_PATHS=%QT_DLL_PATHS% D:\Qt\5.15.2\mingw81_64\bin"

REM Essential Qt DLLs for this application
set "REQUIRED_DLLS=Qt5Core.dll Qt5Gui.dll Qt5Widgets.dll Qt5SerialPort.dll Qt5Network.dll"

for %%p in (%QT_DLL_PATHS%) do (
    if exist "%%p\Qt5Core.dll" (
        echo Found Qt libraries at: %%p
        for %%d in (%REQUIRED_DLLS%) do (
            if exist "%%p\%%d" (
                copy "%%p\%%d" "%PACKAGE_DIR%\" >nul 2>&1
                if !errorlevel! equ 0 echo   Copied: %%d
            )
        )

        REM Copy platform plugins
        if exist "%%p\..\plugins\platforms" (
            if not exist "%PACKAGE_DIR%\platforms" mkdir "%PACKAGE_DIR%\platforms"
            copy "%%p\..\plugins\platforms\qwindows.dll" "%PACKAGE_DIR%\platforms\" >nul 2>&1
            if !errorlevel! equ 0 echo   Copied: platforms\qwindows.dll
        )

        REM Copy MinGW runtime if exists
        if exist "%%p\libgcc_s_seh-1.dll" copy "%%p\libgcc_s_seh-1.dll" "%PACKAGE_DIR%\" >nul 2>&1
        if exist "%%p\libstdc++-6.dll" copy "%%p\libstdc++-6.dll" "%PACKAGE_DIR%\" >nul 2>&1
        if exist "%%p\libwinpthread-1.dll" copy "%%p\libwinpthread-1.dll" "%PACKAGE_DIR%\" >nul 2>&1

        set "QT_DLLS_FOUND=1"
        goto :qt_dlls_done
    )
)

:qt_dlls_done
if defined QT_DLLS_FOUND (
    echo OK Qt runtime libraries copied
) else (
    echo WARNING: Qt runtime libraries not found automatically
    echo The package may require Qt installation on target computer
)

REM Copy resources if they exist
if exist "%PROJECT_DIR%resources" (
    xcopy "%PROJECT_DIR%resources" "%PACKAGE_DIR%\resources" /e /i /y /q >nul 2>&1
    echo OK Resource files copied
)
if exist "%PROJECT_DIR%icons" (
    xcopy "%PROJECT_DIR%icons" "%PACKAGE_DIR%\icons" /e /i /y /q >nul 2>&1
    echo OK Icon files copied
)
echo.

REM Create documentation
echo [4/4] Creating documentation...

REM Create user manual
(
echo RF Debug Tool Qt Version - Simple Package
echo ========================================
echo.
echo Package Time: %YYYY%-%MM%-%DD% %HH%:%Min%:%Sec%
echo Author: flex ^(<EMAIL>^)
echo.
echo IMPORTANT NOTICE:
echo This is an ENHANCED SIMPLE package that includes common Qt libraries.
echo Most computers should be able to run this directly.
echo.
echo If the program still fails to start, please:
echo   1. Install Microsoft Visual C++ Redistributable, or
echo   2. Install Qt runtime libraries, or
echo   3. Use the full package created by package.bat
echo.
echo File Description:
echo   * %EXE_NAME% - Main program
echo   * Start.bat - Quick start script
echo   * config.ini - Configuration file ^(if exists^)
echo   * *.dll - Runtime libraries ^(if copied^)
echo.
echo How to use:
echo   1. Copy this entire folder to target computer
echo   2. Double-click %EXE_NAME% to run directly
echo   3. Or double-click "Start.bat" to run
echo.
echo Main Features:
echo   * Multiple connection types: ADB, Serial, TCP
echo   * Smart log management and storage
echo   * Remote collaboration debugging
echo   * Search and highlight functions
echo   * Professional terminal interface
echo   * Connection history management
echo   * Customizable background and transparency
echo.
echo System Requirements:
echo   * Windows 7 SP1 or higher
echo   * Qt runtime libraries ^(may need separate installation^)
echo   * At least 50MB available disk space
echo.
echo Technical Support: <EMAIL>
) > "%PACKAGE_DIR%\README.txt"

REM Create startup script
echo @echo off > "%PACKAGE_DIR%\Start.bat"
echo cd /d "%%~dp0" >> "%PACKAGE_DIR%\Start.bat"
echo echo Starting RF Debug Tool... >> "%PACKAGE_DIR%\Start.bat"
echo start "" "%EXE_NAME%" >> "%PACKAGE_DIR%\Start.bat"

REM Create Qt runtime installer script
(
echo @echo off
echo echo ========================================
echo echo    Qt Runtime Libraries Installer
echo echo ========================================
echo echo.
echo echo This script helps install Qt runtime libraries
echo echo if the main program fails to start.
echo echo.
echo echo Please visit one of these links to download Qt runtime:
echo echo.
echo echo 1. Microsoft Visual C++ Redistributable:
echo echo    https://aka.ms/vs/17/release/vc_redist.x64.exe
echo echo.
echo echo 2. Qt Online Installer:
echo echo    https://www.qt.io/download-qt-installer
echo echo.
echo echo 3. Or contact technical support: <EMAIL>
echo echo.
echo pause
) > "%PACKAGE_DIR%\Install_Qt_Runtime.bat"

echo OK Documentation files created
echo.

REM Display results
echo ========================================
echo        Simple Package Complete!
echo ========================================
echo.
echo Package Directory: %PACKAGE_DIR%
echo.
echo Package Contents:
dir "%PACKAGE_DIR%" /b | findstr /v "^$"
echo.
echo Package Size:
for /f "tokens=3" %%a in ('dir "%PACKAGE_DIR%" /s /-c ^| find "File(s)"') do echo   Total Size: %%a bytes
echo.
echo IMPORTANT NOTES:
echo   * This is a SIMPLE package
echo   * May require Qt runtime on target computer
echo   * For full standalone package, use package.bat instead
echo.
echo Quality Check:
if exist "%PACKAGE_DIR%\%EXE_NAME%" (
    echo   OK Main program exists
) else (
    echo   ERROR Main program missing
)
if exist "%PACKAGE_DIR%\README.txt" (
    echo   OK Documentation exists
) else (
    echo   ERROR Documentation missing
)
if exist "%PACKAGE_DIR%\Qt5Core.dll" (
    echo   OK Qt Core library exists
) else (
    echo   WARNING Qt Core library missing
)
if exist "%PACKAGE_DIR%\Qt5SerialPort.dll" (
    echo   OK Qt SerialPort library exists
) else (
    echo   WARNING Qt SerialPort library missing
)
if exist "%PACKAGE_DIR%\platforms\qwindows.dll" (
    echo   OK Qt Platform plugin exists
) else (
    echo   WARNING Qt Platform plugin missing
)
echo.

REM Auto open package directory
echo Opening package directory...
explorer "%PACKAGE_DIR%"

echo.
echo ========================================
echo     Simple Package Script Completed!
echo ========================================
echo Package Directory: %PACKAGE_DIR%
echo.
echo The package is ready for distribution!
echo.
pause
