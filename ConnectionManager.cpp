#include "ConnectionManager.h"
#include "SerialConnection.h"
#include "TCPConnection.h"
#include "ADBConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QTimer>

ConnectionManager::ConnectionManager(QObject *parent)
    : QObject(parent), m_serialConnection(nullptr), m_tcpConnection(nullptr), m_adbConnection(nullptr), m_networkConnection(nullptr), m_ftpConnection(nullptr), m_currentType(None), m_currentStatus(Disconnected), m_connectionTimeout(30), m_autoReconnectEnabled(false), m_reconnectAttempts(0), m_maxReconnectAttempts(3)
{
    // 初始化重连定时器
    m_reconnectTimer = new QTimer(this);
    m_reconnectTimer->setSingleShot(true);
    connect(m_reconnectTimer, &QTimer::timeout, this, &ConnectionManager::onReconnectTimer);

    initializeConnections();
}

ConnectionManager::~ConnectionManager()
{
    cleanupConnections();
}

void ConnectionManager::initializeConnections()
{
    // 创建串口连接对象
    m_serialConnection = new SerialConnection(this);
    QObject::connect(m_serialConnection, &SerialConnection::connected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_serialConnection, &SerialConnection::disconnected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_serialConnection, &SerialConnection::dataReceived,
                     this, &ConnectionManager::onDataReceived);
    QObject::connect(m_serialConnection, &SerialConnection::errorOccurred,
                     this, &ConnectionManager::onErrorOccurred);

    // 初始化TCP连接
    m_tcpConnection = new TCPConnection(this);
    QObject::connect(m_tcpConnection, &TCPConnection::connected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_tcpConnection, &TCPConnection::disconnected,
                     this, &ConnectionManager::onConnectionStatusChanged);
    QObject::connect(m_tcpConnection, &TCPConnection::dataReceived,
                     this, &ConnectionManager::onDataReceived);
    QObject::connect(m_tcpConnection, &TCPConnection::errorOccurred,
                     this, &ConnectionManager::onErrorOccurred);

    // 初始化ADB连接
    m_adbConnection = new ADBConnection(this);
    QObject::connect(m_adbConnection, &ADBConnection::connected,
                     this, &ConnectionManager::onAdbConnected);
    QObject::connect(m_adbConnection, &ADBConnection::disconnected,
                     this, &ConnectionManager::onAdbDisconnected);
    QObject::connect(m_adbConnection, &ADBConnection::dataReceived,
                     this, &ConnectionManager::onDataReceived);
    QObject::connect(m_adbConnection, &ADBConnection::errorOccurred,
                     this, &ConnectionManager::onErrorOccurred);

    // TODO: 初始化其他连接类型
    // m_networkConnection = new NetworkConnection(this);
    // m_ftpConnection = new FTPConnection(this);
}

void ConnectionManager::cleanupConnections()
{
    if (isConnected())
    {
        disconnectFromDevice();
    }

    // 清理连接对象
    if (m_serialConnection)
    {
        m_serialConnection->deleteLater();
        m_serialConnection = nullptr;
    }

    // TODO: 清理其他连接类型
}

bool ConnectionManager::connectToDevice(const QVariantMap &params)
{
    qDebug() << "ConnectionManager::connectToDevice() - 开始连接";

    // 先检查是否需要断开现有连接（不加锁）
    if (m_currentStatus == Connected || m_currentStatus == Connecting)
    {
        qDebug() << "ConnectionManager::connectToDevice() - 断开现有连接";
        disconnectFromDevice();
    }

    qDebug() << "ConnectionManager::connectToDevice() - 获取互斥锁";
    QMutexLocker locker(&m_mutex);

    qDebug() << "ConnectionManager::connectToDevice() - 设置连接状态";
    setStatus(Connecting, "正在连接...");

    QString typeStr = params.value("type", "serial").toString().toLower();
    ConnectionType type = stringToConnectionType(typeStr);
    qDebug() << "ConnectionManager::connectToDevice() - 连接类型:" << typeStr;

    m_connectionParams = params;
    m_currentType = type;

    qDebug() << "ConnectionManager::connectToDevice() - 创建连接";
    bool success = createConnection(type, params);

    if (!success)
    {
        qDebug() << "ConnectionManager::connectToDevice() - 连接失败";
        setStatus(Error, "连接失败");
        m_currentType = None;
        m_connectionParams.clear();
    }
    else
    {
        qDebug() << "ConnectionManager::connectToDevice() - 连接启动成功，等待异步结果";
    }
    // 如果成功启动连接，状态将通过onConnectionStatusChanged异步更新

    qDebug() << "ConnectionManager::connectToDevice() - 完成，返回:" << success;
    return success;
}

void ConnectionManager::disconnectFromDevice()
{
    qDebug() << "ConnectionManager::disconnectFromDevice() - 开始断开连接";

    // 使用作用域锁，确保快速释放
    {
        QMutexLocker locker(&m_mutex);

        if (m_currentStatus == Disconnected)
        {
            qDebug() << "ConnectionManager::disconnectFromDevice() - 已经断开，直接返回";
            return;
        }

        qDebug() << "ConnectionManager::disconnectFromDevice() - 设置断开状态";
        setStatus(Disconnecting, "正在断开连接...");

        // 停止重连定时器
        if (m_reconnectTimer->isActive())
        {
            qDebug() << "ConnectionManager::disconnectFromDevice() - 停止重连定时器";
            m_reconnectTimer->stop();
        }

        qDebug() << "ConnectionManager::disconnectFromDevice() - 销毁连接";
        destroyCurrentConnection();

        qDebug() << "ConnectionManager::disconnectFromDevice() - 设置已断开状态";
        setStatus(Disconnected, "已断开连接");
        m_currentType = None;
        m_connectionParams.clear();
    } // 释放互斥锁

    qDebug() << "ConnectionManager::disconnectFromDevice() - 发出connectionLost信号";
    emit connectionLost();
    qDebug() << "ConnectionManager::disconnectFromDevice() - 完成";
}

bool ConnectionManager::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentStatus == Connected;
}

ConnectionManager::ConnectionType ConnectionManager::currentConnectionType() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentType;
}

ConnectionManager::ConnectionStatus ConnectionManager::currentStatus() const
{
    QMutexLocker locker(&m_mutex);
    return m_currentStatus;
}

QString ConnectionManager::currentStatusString() const
{
    QMutexLocker locker(&m_mutex);
    return statusToString(m_currentStatus);
}

QString ConnectionManager::statusToString(ConnectionStatus status) const
{
    switch (status)
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Connected:
        return "已连接";
    case Disconnecting:
        return "断开中";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

QVariantMap ConnectionManager::currentConnectionParams() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionParams;
}

bool ConnectionManager::sendCommand(const QString &command)
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return false;
    }

    bool success = false;

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->sendCommand(command);
        }
        break;
    case TCP:
        if (m_tcpConnection)
        {
            success = m_tcpConnection->sendCommand(command);
        }
        break;
    case ADB:
        if (m_adbConnection)
        {
            success = m_adbConnection->executeShellCommand(command);
        }
        break;
    case Network:
        // TODO: 实现网络命令发送
        break;
    case FTP:
        // TODO: 实现FTP命令发送
        break;
    default:
        break;
    }

    return success;
}

bool ConnectionManager::sendData(const QByteArray &data)
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return false;
    }

    bool success = false;

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->sendData(data);
        }
        break;
    case TCP:
        if (m_tcpConnection)
        {
            success = m_tcpConnection->sendData(data);
        }
        break;
    case ADB:
        if (m_adbConnection)
        {
            // ADB通过交互式shell发送数据
            QString command = QString::fromUtf8(data);
            success = m_adbConnection->executeShellCommand(command);
        }
        break;
    case Network:
        // TODO: 实现网络数据发送
        break;
    case FTP:
        // TODO: 实现FTP数据发送
        break;
    default:
        break;
    }

    return success;
}

QString ConnectionManager::getConnectionInfo() const
{
    QMutexLocker locker(&m_mutex);

    if (m_currentStatus != Connected)
    {
        return "未连接";
    }

    QString info = QString("连接类型: %1\n").arg(connectionTypeToString(m_currentType));

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            info += QString("串口: %1\n").arg(m_connectionParams.value("port").toString());
            info += QString("波特率: %1\n").arg(m_connectionParams.value("baudRate").toInt());
        }
        break;
    case TCP:
        info += QString("主机: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        info += QString("协议: %1\n").arg(m_connectionParams.value("protocol", "TCP").toString());
        break;
    case ADB:
        info += QString("设备: %1\n").arg(m_connectionParams.value("device").toString());
        break;
    case Network:
        info += QString("地址: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        break;
    case FTP:
        info += QString("FTP服务器: %1:%2\n")
                    .arg(m_connectionParams.value("host").toString())
                    .arg(m_connectionParams.value("port").toInt());
        break;
    default:
        break;
    }

    return info;
}

QStringList ConnectionManager::getAvailablePorts() const
{
    return SerialConnection::getAvailablePorts();
}

QStringList ConnectionManager::getAvailableADBDevices() const
{
    if (m_adbConnection)
    {
        return m_adbConnection->getAvailableDevices();
    }
    return QStringList();
}

void ConnectionManager::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int ConnectionManager::connectionTimeout() const
{
    return m_connectionTimeout;
}

void ConnectionManager::setAutoReconnect(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

bool ConnectionManager::autoReconnectEnabled() const
{
    return m_autoReconnectEnabled;
}

void ConnectionManager::reconnect()
{
    if (!m_connectionParams.isEmpty())
    {
        connectToDevice(m_connectionParams);
    }
}

void ConnectionManager::testConnection(const QVariantMap &params)
{
    // 简单的连接测试
    bool success = false;
    QString message;

    QString typeStr = params.value("type", "serial").toString().toLower();
    ConnectionType type = stringToConnectionType(typeStr);

    switch (type)
    {
    case Serial:
    {
        QString port = params.value("port").toString();
        if (!port.isEmpty())
        {
            success = true;
            message = QString("串口 %1 可用").arg(port);
        }
        else
        {
            message = "串口参数无效";
        }
        break;
    }
    case TCP:
    {
        QString host = params.value("host").toString();
        int port = params.value("port", 8080).toInt();
        if (!host.isEmpty() && port > 0 && port <= 65535)
        {
            success = true;
            message = QString("TCP连接参数有效: %1:%2").arg(host).arg(port);
        }
        else
        {
            message = "TCP参数无效";
        }
        break;
    }
    default:
        message = "不支持的连接类型";
        break;
    }

    emit testResult(success, message);
}

void ConnectionManager::onConnectionStatusChanged()
{
    try
    {
        qDebug() << "ConnectionManager::onConnectionStatusChanged() - 开始处理连接状态变化";
        // 处理连接状态变化
        QObject *sender = this->sender();
        qDebug() << "ConnectionManager::onConnectionStatusChanged() - 发送者:" << sender;

        if (sender == m_serialConnection)
        {
            qDebug() << "ConnectionManager::onConnectionStatusChanged() - 串口连接状态变化";

            bool isConnected = false;
            try
            {
                isConnected = m_serialConnection->isConnected();
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - 串口连接状态:" << isConnected;
            }
            catch (...)
            {
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - 检查串口连接状态时发生异常";
                return;
            }

            if (isConnected)
            {
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - 串口已连接";
                setStatus(Connected, "串口连接成功");
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - 发出connectionEstablished信号";
                emit connectionEstablished();
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - connectionEstablished信号发出完成";
            }
            else
            {
                qDebug() << "ConnectionManager::onConnectionStatusChanged() - 串口已断开";
                setStatus(Disconnected, "串口连接断开");
                emit connectionLost();

                // 如果启用自动重连，尝试重连
                if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
                {
                    m_reconnectAttempts++;
                    m_reconnectTimer->start(5000); // 5秒后重连
                }
            }
        }
        else if (sender == m_tcpConnection)
        {
            qDebug() << "ConnectionManager::onConnectionStatusChanged() - TCP连接状态变化";

            // 使用QTimer延迟处理，避免在信号处理中调用可能死锁的方法
            QTimer::singleShot(0, this, [this]()
                               {
                qDebug() << "ConnectionManager - 延迟处理TCP状态变化";
                try
                {
                    bool isConnected = m_tcpConnection->isConnected();
                    qDebug() << "ConnectionManager - TCP连接状态:" << isConnected;

                    if (isConnected)
                    {
                        qDebug() << "ConnectionManager - TCP已连接";
                        setStatus(Connected, "TCP连接成功");
                        emit connectionEstablished();
                    }
                    else
                    {
                        qDebug() << "ConnectionManager - TCP已断开";
                        setStatus(Disconnected, "TCP连接断开");
                        emit connectionLost();

                        // 如果启用自动重连，尝试重连
                        if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
                        {
                            qDebug() << "ConnectionManager - TCP启动自动重连";
                            m_reconnectAttempts++;
                            m_reconnectTimer->start(5000); // 5秒后重连
                        }
                    }
                }
                catch (const std::exception &e)
                {
                    qDebug() << "ConnectionManager - TCP状态检查异常:" << e.what();
                }
                catch (...)
                {
                    qDebug() << "ConnectionManager - TCP状态检查未知异常";
                } });
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "ConnectionManager::onConnectionStatusChanged() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ConnectionManager::onConnectionStatusChanged() - 未知异常";
    }
}

void ConnectionManager::onDataReceived(const QByteArray &data)
{
    qDebug() << "ConnectionManager::onDataReceived() - 收到数据:" << QString::fromLatin1(data.left(50));
    qDebug() << "ConnectionManager::onDataReceived() - 原始数据hex:" << data.left(50).toHex();

    // 智能编码检测和转换
    QString dataStr;

    // 首先尝试UTF-8解码
    dataStr = QString::fromUtf8(data);

    // 如果UTF-8解码产生替换字符，说明不是有效的UTF-8，尝试其他编码
    if (dataStr.contains(QChar::ReplacementCharacter))
    {
        // 尝试Latin-1（兼容ASCII和扩展ASCII，常用于串口通信）
        dataStr = QString::fromLatin1(data);
        qDebug() << "ConnectionManager::onDataReceived() - 使用Latin-1编码";
    }
    else
    {
        qDebug() << "ConnectionManager::onDataReceived() - 使用UTF-8编码";
    }

    // 发出原始字节数据信号（用于正确的编码处理）
    qDebug() << "ConnectionManager::onDataReceived() - 发出rawDataReceived信号";
    emit rawDataReceived(data);

    // 同时发出字符串数据信号（向后兼容）
    qDebug() << "ConnectionManager::onDataReceived() - 发出dataReceived信号:" << dataStr.left(50);
    emit dataReceived(dataStr);

    // 不再进行行缓冲处理，避免重复处理和显示问题
    // 所有数据处理都交给终端组件处理
}

void ConnectionManager::onErrorOccurred(const QString &error)
{
    setStatus(Error, error);
    emit errorOccurred(error);

    // 如果启用自动重连，尝试重连
    if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
    {
        m_reconnectAttempts++;
        m_reconnectTimer->start(10000); // 10秒后重连
    }
}

void ConnectionManager::onReconnectTimer()
{
    if (m_autoReconnectEnabled && !m_connectionParams.isEmpty())
    {
        setStatus(Connecting, QString("自动重连中... (第%1次)").arg(m_reconnectAttempts));
        connectToDevice(m_connectionParams);
    }
}

void ConnectionManager::setStatus(ConnectionStatus status, const QString &details)
{
    qDebug() << "ConnectionManager::setStatus() - 设置状态:" << status << "详情:" << details;
    if (m_currentStatus != status)
    {
        m_currentStatus = status;
        m_statusDetails = details;

        QString statusStr = statusToString(status);
        qDebug() << "ConnectionManager::setStatus() - 发出statusChanged信号:" << statusStr;
        emit statusChanged(statusStr, details);
        qDebug() << "ConnectionManager::setStatus() - statusChanged信号发出完成";
    }
    else
    {
        qDebug() << "ConnectionManager::setStatus() - 状态未变化，跳过";
    }
}

bool ConnectionManager::createConnection(ConnectionType type, const QVariantMap &params)
{
    bool success = false;

    switch (type)
    {
    case Serial:
        if (m_serialConnection)
        {
            success = m_serialConnection->connect(params);
        }
        break;
    case TCP:
        if (m_tcpConnection)
        {
            success = m_tcpConnection->connect(params);
        }
        break;
    case ADB:
        qDebug() << "ConnectionManager::createConnection() - 创建ADB连接";
        if (m_adbConnection)
        {
            qDebug() << "ConnectionManager::createConnection() - ADB连接对象存在，调用connect";
            success = m_adbConnection->connect(params);
            qDebug() << "ConnectionManager::createConnection() - ADB连接结果:" << success;
        }
        else
        {
            qDebug() << "ConnectionManager::createConnection() - ADB连接对象不存在！";
        }
        break;
    case Network:
        // TODO: 实现网络连接创建
        break;
    case FTP:
        // TODO: 实现FTP连接创建
        break;
    default:
        break;
    }

    return success;
}

void ConnectionManager::destroyCurrentConnection()
{
    qDebug() << "ConnectionManager::destroyCurrentConnection() - 销毁连接类型:" << m_currentType;

    switch (m_currentType)
    {
    case Serial:
        if (m_serialConnection)
        {
            qDebug() << "ConnectionManager::destroyCurrentConnection() - 断开串口连接";
            // 直接调用，因为SerialConnection::disconnect已经修复了死锁问题
            m_serialConnection->disconnect();
        }
        break;
    case TCP:
        if (m_tcpConnection)
        {
            qDebug() << "ConnectionManager::destroyCurrentConnection() - 断开TCP连接";
            m_tcpConnection->disconnect();
        }
        break;
    case ADB:
        if (m_adbConnection)
        {
            qDebug() << "ConnectionManager::destroyCurrentConnection() - 断开ADB连接";
            m_adbConnection->disconnect();
        }
        break;
    case Network:
        qDebug() << "ConnectionManager::destroyCurrentConnection() - 断开网络连接 (TODO)";
        // TODO: 断开网络连接
        break;
    case FTP:
        qDebug() << "ConnectionManager::destroyCurrentConnection() - 断开FTP连接 (TODO)";
        // TODO: 断开FTP连接
        break;
    default:
        qDebug() << "ConnectionManager::destroyCurrentConnection() - 未知连接类型";
        break;
    }

    qDebug() << "ConnectionManager::destroyCurrentConnection() - 完成";
}

QString ConnectionManager::connectionTypeToString(ConnectionType type) const
{
    switch (type)
    {
    case Serial:
        return "串口";
    case TCP:
        return "TCP";
    case ADB:
        return "ADB";
    case Network:
        return "网络";
    case FTP:
        return "FTP";
    default:
        return "未知";
    }
}

ConnectionManager::ConnectionType ConnectionManager::stringToConnectionType(const QString &typeStr) const
{
    QString lower = typeStr.toLower();

    if (lower == "serial" || lower == "串口")
    {
        return Serial;
    }
    else if (lower == "tcp" || lower == "网络")
    {
        return TCP;
    }
    else if (lower == "adb")
    {
        return ADB;
    }
    else if (lower == "network" || lower == "网络")
    {
        return Network;
    }
    else if (lower == "ftp")
    {
        return FTP;
    }
    else
    {
        return None;
    }
}

void ConnectionManager::onAdbConnected()
{
    qDebug() << "ConnectionManager::onAdbConnected() - ADB连接成功";
    setStatus(Connected, "ADB连接成功");
    emit connectionEstablished();
}

void ConnectionManager::onAdbDisconnected()
{
    qDebug() << "ConnectionManager::onAdbDisconnected() - ADB连接断开";
    setStatus(Disconnected, "ADB连接断开");
    emit connectionLost();

    // 如果启用自动重连，尝试重连
    if (m_autoReconnectEnabled && m_reconnectAttempts < m_maxReconnectAttempts)
    {
        m_reconnectAttempts++;
        m_reconnectTimer->start(5000); // 5秒后重连
    }
}
