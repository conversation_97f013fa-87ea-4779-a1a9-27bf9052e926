[General]
quick_command_1=save_log
quick_command_1_name=保存日志
quick_command_2=pwd
quick_command_2_name=路径
quick_command_3=whoami
quick_command_3_name=用户
commands=@Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bQ\x85[XO\x7fu(\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\xff\xff\xff\xff\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4*\x99\x95\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\xe\0\x66\0r\0\x65\0\x65\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bR\x17Q\xfa\x65\x87N\xf6\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\xff\xff\xff\xff\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4*\x99\x96\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\f\0l\0s\0 \0-\0l\0\x61), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b_SRMv\xee_U\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\xff\xff\xff\xff\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4*\x99\x97\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x6\0p\0w\0\x64), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bx\xc1v\xd8O\x7fu(\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\xff\xff\xff\xff\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4*\x99\x98\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\n\0\x64\0\x66\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\a\0\0\0\x14\0u\0s\0\x61\0g\0\x65\0\x43\0o\0u\0n\0t\0\0\0\x2\0\0\0\0\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b|\xfb~\xdfO\xe1`o\0\0\0\x10\0l\0\x61\0s\0t\0U\0s\0\x65\0\x64\0\0\0\x10\0\0\0\0\xff\xff\xff\xff\xff\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\x16\0\x64\0\x65\0s\0\x63\0r\0i\0p\0t\0i\0o\0n\0\0\0\n\xff\xff\xff\xff\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4*\x99\x9a\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x10\0u\0n\0\x61\0m\0\x65\0 \0-\0\x61), @Variant(\0\0\0\b\0\0\0\x4\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bQ\x85[XO\x7fu(\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4\x31\x37\x83\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\xe\0\x66\0r\0\x65\0\x65\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\x4\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bR\x17Q\xfa\x65\x87N\xf6\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4\x31\x37\x84\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\f\0l\0s\0 \0-\0l\0\x61), @Variant(\0\0\0\b\0\0\0\x4\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b_SRMv\xee_U\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4\x31\x37\x85\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x6\0p\0w\0\x64), @Variant(\0\0\0\b\0\0\0\x4\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\bx\xc1v\xd8O\x7fu(\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4\x31\x37\x86\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\n\0\x64\0\x66\0 \0-\0h), @Variant(\0\0\0\b\0\0\0\x4\0\0\0\b\0n\0\x61\0m\0\x65\0\0\0\n\0\0\0\b|\xfb~\xdfO\xe1`o\0\0\0\xe\0\x65\0n\0\x61\0\x62\0l\0\x65\0\x64\0\0\0\x1\x1\0\0\0\xe\0\x63\0r\0\x65\0\x61\0t\0\x65\0\x64\0\0\0\x10\0%\x8c\xc8\x4\x31\x37\x88\xff\0\0\0\xe\0\x63\0o\0n\0t\0\x65\0n\0t\0\0\0\n\0\0\0\x10\0u\0n\0\x61\0m\0\x65\0 \0-\0\x61)
log_timestamp_enabled=true
log_timestamp_format=yyyy-MM-dd hh:mm:ss
log_file_enabled=false
log_file_path=C:/Users/<USER>/AppData/Roaming/RF Tools/RF调试工具/rf_tool.log
log_auto_save=true
log_auto_save_interval=30
log_echo_enabled=true
log_level=1
log_max_lines=10000
log_rotation_enabled=false
log_max_file_size=10
log_max_backup_files=5
timestamp_enabled=false
background_color=#55aa7f
background_color2=#55aa7f
background_image=
background_image_mode=center
background_opacity=1
background_type=gradient

[terminal]
font_size=21
echo_enabled=true
font_family=Consolas
font_weight=normal
max_history=1000
timestamp_enabled=true
timestamp_format=hh:mm:ss
scroll_bar_policy=0

[app]
language=zh_CN
theme=default
version=2.0.0

[background]
color=#000000
color2=#333333
image=
opacity=1
type=color

[connection]
auto_reconnect=false
max_reconnect_attempts=3
timeout=30

[log]
auto_save=true
auto_save_interval=30
buffer_enabled=true
buffer_size=50000
echo_enabled=true
file_enabled=false
file_path=
font_family=Consolas
font_size=11
font_weight=normal
level=1
max_backup_files=5
max_file_size=10
max_lines=10000
rotation_enabled=false
timestamp_enabled=true
timestamp_format=yyyy-MM-dd hh:mm:ss

[window]
height=800
maximized=false
width=1200
