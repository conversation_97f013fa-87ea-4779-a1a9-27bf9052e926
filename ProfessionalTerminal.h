#ifndef PROFESSIONALTERMINAL_H
#define PROFESSIONALTERMINAL_H

#include <QWidget>
#include <QVBoxLayout>
#include <QTextEdit>
#include <QLineEdit>
#include <QSplitter>
#include <QTimer>
#include <QKeyEvent>
#include <QScrollBar>
#include <QFont>
#include <QTextCursor>
#include <QTextCharFormat>
#include <QApplication>
#include <QClipboard>

/**
 * 专业的串口终端组件
 * 模拟SecureCRT/WindTerm的交互体验
 */
class ProfessionalTerminal : public QWidget
{
    Q_OBJECT

public:
    explicit ProfessionalTerminal(QWidget *parent = nullptr);
    ~ProfessionalTerminal();

    // 终端控制
    void clear();
    void setConnected(bool connected);
    bool isConnected() const { return m_isConnected; }

    // 数据处理
    void appendData(const QString &data);
    void appendMessage(const QString &message);

    // 配置
    void setFont(const QFont &font);
    void setTerminalFont(const QString &family, int size, const QString &weight = "normal");
    QFont font() const
    {
        if (m_display)
        {
            return m_display->font();
        }
        return m_terminalFont;
    }
    void setColors(const QColor &background, const QColor &foreground);
    void setEchoMode(bool enabled);
    void setTimestampEnabled(bool enabled);

    // 获取内部显示组件（用于样式设置）
    QTextEdit *getDisplay() const { return m_display; }

signals:
    void commandEntered(const QString &command);
    void dataToSend(const QByteArray &data);
    void timestampStateChanged(bool enabled);
    void fontSizeChanged(int size);

protected:
    bool eventFilter(QObject *obj, QEvent *event) override;
    void keyPressEvent(QKeyEvent *event) override;
    void mousePressEvent(QMouseEvent *event) override;
    void mouseReleaseEvent(QMouseEvent *event) override;
    void mouseMoveEvent(QMouseEvent *event) override;
    void wheelEvent(QWheelEvent *event) override;

private slots:
    void onCursorPositionChanged();
    void onTextChanged();
    void showContextMenu(const QPoint &pos);
    void clearTerminal();
    void copySelection();
    void pasteText();
    void selectAll();
    void saveToFile();
    void findText();

public:
    // 公共接口，支持外部调用
    void findText(const QString &searchText);

private:
    void setupUI();
    void setupFont();
    void setupColors();

    // 输入处理
    void processInput(const QString &input);
    bool handleSpecialKeys(QKeyEvent *event);
    void insertText(const QString &text);

    // 显示处理
    void appendToDisplay(const QString &text);
    void scrollToBottom();
    void updateCursor();

    // 历史记录
    void addToHistory(const QString &command);
    void navigateHistory(bool up);

    // 工具函数
    QString getCurrentLine() const;
    int getCurrentColumn() const;
    void moveCursorToEnd();
    void ensureVisible();
    QString removeAnsiEscapeSequences(const QString &text);

private:
    // UI组件
    QVBoxLayout *m_layout;
    QTextEdit *m_display;

    // 状态
    bool m_isConnected;
    bool m_echoEnabled;
    bool m_timestampEnabled;
    bool m_atLineStart;

    // 输入缓冲
    QString m_inputBuffer;
    QStringList m_commandHistory;
    int m_historyIndex;

    // 显示配置
    QFont m_terminalFont;
    QColor m_backgroundColor;
    QColor m_foregroundColor;
    QColor m_cursorColor;

    // 光标管理
    QTimer *m_cursorTimer;

    // 数据缓冲
    QString m_dataBuffer;
    QTimer *m_flushTimer;

    // 防抖机制
    QTimer *m_keyRepeatTimer;
    QString m_lastKeyData;
    int m_keyRepeatCount;

    // 命令提示符检测
    QString m_lastPrompt;
};

#endif // PROFESSIONALTERMINAL_H
