#include "LogManager.h"
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QMutexLocker>
#include <QDateTime>
#include <QDebug>
#include <QFile>
#include <QTextStream>
#include <QTimer>

LogManager::LogManager(QObject *parent)
    : QObject(parent), m_logFile(nullptr), m_logStream(nullptr), m_logFileEnabled(false), m_timestampEnabled(true), m_timestampFormat("yyyy-MM-dd hh:mm:ss"), m_echoEnabled(true), m_autoSaveEnabled(true), m_autoSaveInterval(30), m_currentLogLevel(Info), m_maxFileSize(10 * 1024 * 1024) // 10MB
      ,
      m_maxBackupFiles(5), m_maxBufferSize(50000), m_totalLogCount(0), m_sessionStartTime(QDateTime::currentDateTime())
{
    // 初始化配置
    initializeConfig();

    // 设置自动保存定时器
    setupAutoSave();

    // 初始化级别启用状态
    m_levelEnabled[Debug] = true;
    m_levelEnabled[Info] = true;
    m_levelEnabled[Warning] = true;
    m_levelEnabled[Error] = true;
    m_levelEnabled[System] = true;
    m_levelEnabled[Input] = true;
    m_levelEnabled[Output] = true;

    // 设置默认日志文件路径
    QString dataDir = QStandardPaths::writableLocation(QStandardPaths::AppDataLocation);
    QDir().mkpath(dataDir);
    m_logFilePath = QDir(dataDir).filePath("rf_tool.log");
}

LogManager::~LogManager()
{
    if (m_autoSaveEnabled)
    {
        flush();
    }

    if (m_logFile)
    {
        m_logFile->close();
        delete m_logFile;
    }

    if (m_logStream)
    {
        delete m_logStream;
    }
}

void LogManager::initializeConfig()
{
    // 初始化预定义时间戳格式
    m_timestampFormats["short"] = "hh:mm:ss";
    m_timestampFormats["medium"] = "MM-dd hh:mm:ss";
    m_timestampFormats["long"] = "yyyy-MM-dd hh:mm:ss";
    m_timestampFormats["full"] = "yyyy-MM-dd hh:mm:ss.zzz";
    m_timestampFormats["iso"] = "yyyy-MM-ddThh:mm:ss";

    // 设置默认配置
    m_config["timestamp_enabled"] = m_timestampEnabled;
    m_config["timestamp_format"] = m_timestampFormat;
    m_config["echo_enabled"] = m_echoEnabled;
    m_config["file_enabled"] = m_logFileEnabled;
    m_config["file_path"] = m_logFilePath;
    m_config["auto_save"] = m_autoSaveEnabled;
    m_config["auto_save_interval"] = m_autoSaveInterval;
    m_config["log_level"] = static_cast<int>(m_currentLogLevel);
    m_config["max_file_size"] = m_maxFileSize;
    m_config["max_backup_files"] = m_maxBackupFiles;
    // 新增缓冲区配置
    m_config["buffer_enabled"] = true;
    m_config["buffer_size"] = m_maxBufferSize;
}

void LogManager::setupAutoSave()
{
    m_autoSaveTimer = new QTimer(this);
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
    connect(m_autoSaveTimer, &QTimer::timeout, this, &LogManager::onAutoSaveTimer);

    if (m_autoSaveEnabled)
    {
        m_autoSaveTimer->start();
    }
}

void LogManager::log(const QString &message, LogLevel level)
{
    QMutexLocker locker(&m_mutex);

    if (!shouldLogLevel(level))
    {
        return;
    }

    QString formattedMessage = formatLogMessage(message, level);

    // 添加到缓冲区
    m_logBuffer.append(formattedMessage);
    if (m_logBuffer.size() > m_maxBufferSize)
    {
        m_logBuffer.removeFirst();
    }

    // 写入文件
    if (m_logFileEnabled)
    {
        writeToFile(formattedMessage);
    }

    // 回显到控制台
    if (m_echoEnabled)
    {
        qDebug() << formattedMessage;
    }

    m_totalLogCount++;

    emit logSaved(formattedMessage);
}

void LogManager::writeDebug(const QString &message)
{
    log(message, Debug);
}

void LogManager::writeInfo(const QString &message)
{
    log(message, Info);
}

void LogManager::writeWarning(const QString &message)
{
    log(message, Warning);
}

void LogManager::writeError(const QString &message)
{
    log(message, Error);
}

void LogManager::writeSystem(const QString &message)
{
    log(message, System);
}

void LogManager::writeInput(const QString &message)
{
    log(message, Input);
}

void LogManager::writeOutput(const QString &message)
{
    log(message, Output);
}

void LogManager::writeRawData(const QString &data)
{
    QMutexLocker locker(&m_mutex);

    // 直接写入原始数据到文件，不添加任何格式化
    if (m_logFileEnabled && m_logStream)
    {
        *m_logStream << data;
        m_logStream->flush();
        qDebug() << "LogManager::writeRawData() - 写入原始数据:" << data.left(50);
    }
}

void LogManager::setConfig(const QVariantMap &config)
{
    QMutexLocker locker(&m_mutex);

    m_config = config;

    // 应用配置
    m_timestampEnabled = config.value("timestamp_enabled", true).toBool();
    m_timestampFormat = config.value("timestamp_format", "yyyy-MM-dd hh:mm:ss").toString();
    m_echoEnabled = config.value("echo_enabled", true).toBool();
    m_logFileEnabled = config.value("file_enabled", false).toBool();
    m_autoSaveEnabled = config.value("auto_save", true).toBool();
    m_autoSaveInterval = config.value("auto_save_interval", 30).toInt();
    m_currentLogLevel = static_cast<LogLevel>(config.value("log_level", 1).toInt());
    m_maxFileSize = config.value("max_file_size", 10).toLongLong() * 1024 * 1024; // 转换为字节
    m_maxBackupFiles = config.value("max_backup_files", 5).toInt();

    // 应用缓冲区配置
    bool bufferEnabled = config.value("buffer_enabled", true).toBool();
    int newBufferSize = config.value("buffer_size", 50000).toInt();

    if (newBufferSize != m_maxBufferSize)
    {
        m_maxBufferSize = newBufferSize;
        // 如果当前缓冲区超过新的大小限制，则裁剪
        while (m_logBuffer.size() > m_maxBufferSize)
        {
            m_logBuffer.removeFirst();
        }
        qDebug() << "LogManager::setConfig() - 缓冲区大小更新为:" << m_maxBufferSize;
    }

    QString newLogPath = config.value("file_path", "").toString();
    qDebug() << "LogManager::setConfig() - 日志文件路径:" << newLogPath;
    qDebug() << "LogManager::setConfig() - 当前日志文件路径:" << m_logFilePath;
    qDebug() << "LogManager::setConfig() - 文件日志启用状态:" << m_logFileEnabled;

    // 安全的日志文件设置 - 使用内部方法避免重入锁问题
    if (!newLogPath.isEmpty() && newLogPath != m_logFilePath)
    {
        qDebug() << "LogManager::setConfig() - 开始设置新的日志文件";
        try
        {
            setLogFileInternal(newLogPath);
            qDebug() << "LogManager::setConfig() - 日志文件设置完成";
        }
        catch (const std::exception &e)
        {
            qDebug() << "LogManager::setConfig() - 日志文件设置异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "LogManager::setConfig() - 日志文件设置未知异常";
        }
    }
    else
    {
        qDebug() << "LogManager::setConfig() - 跳过日志文件设置（路径为空或相同）";
    }

    // 更新自动保存定时器
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
    if (m_autoSaveEnabled)
    {
        m_autoSaveTimer->start();
    }
    else
    {
        m_autoSaveTimer->stop();
    }

    emit configChanged("log_config", QVariant::fromValue(m_config));
}

QVariantMap LogManager::getConfig() const
{
    QMutexLocker locker(&m_mutex);
    return m_config;
}

void LogManager::loadConfig()
{
    // 从配置管理器加载配置
    // 这里可以添加从外部配置源加载的逻辑
}

void LogManager::saveConfig()
{
    // 保存配置到配置管理器
    // 这里可以添加保存到外部配置源的逻辑
}

void LogManager::setLogFileInternal(const QString &filePath)
{
    qDebug() << "LogManager::setLogFileInternal() - 开始，路径:" << filePath;
    // 内部方法，不加锁（调用者已经持有锁）

    if (m_logFilePath == filePath)
    {
        qDebug() << "LogManager::setLogFile() - 路径相同，返回";
        return;
    }

    // 关闭当前文件
    qDebug() << "LogManager::setLogFile() - 关闭当前文件";
    if (m_logFile)
    {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;
        qDebug() << "LogManager::setLogFile() - 当前文件已关闭";
    }

    if (m_logStream)
    {
        delete m_logStream;
        m_logStream = nullptr;
        qDebug() << "LogManager::setLogFile() - 当前流已删除";
    }

    m_logFilePath = filePath;
    qDebug() << "LogManager::setLogFile() - 设置新路径:" << m_logFilePath;

    // 如果启用了文件日志，打开新文件
    if (m_logFileEnabled && !m_logFilePath.isEmpty())
    {
        qDebug() << "LogManager::setLogFile() - 开始创建新日志文件";
        qDebug() << "LogManager::setLogFile() - 确保目录存在";
        ensureLogDirectory();
        qDebug() << "LogManager::setLogFile() - 目录检查完成";

        qDebug() << "LogManager::setLogFile() - 创建QFile对象";
        m_logFile = new QFile(m_logFilePath);
        qDebug() << "LogManager::setLogFile() - 尝试打开文件";
        if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append))
        {
            qDebug() << "LogManager::setLogFile() - 文件打开成功，创建流";
            m_logStream = new QTextStream(m_logFile);
            m_logStream->setCodec("UTF-8");
            qDebug() << "LogManager::setLogFile() - 日志文件设置完成";
        }
        else
        {
            qDebug() << "LogManager::setLogFile() - 文件打开失败:" << m_logFile->errorString();
            delete m_logFile;
            m_logFile = nullptr;
            emit errorOccurred("无法打开日志文件: " + m_logFilePath);
        }
    }
    else
    {
        qDebug() << "LogManager::setLogFile() - 跳过文件创建（未启用或路径为空）";
    }
}

QString LogManager::logFile() const
{
    QMutexLocker locker(&m_mutex);
    return m_logFilePath;
}

bool LogManager::isLogFileEnabled() const
{
    return m_logFileEnabled;
}

void LogManager::setLogFileEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);

    if (m_logFileEnabled == enabled)
    {
        return;
    }

    m_logFileEnabled = enabled;

    if (enabled && !m_logFilePath.isEmpty())
    {
        setLogFile(m_logFilePath); // 重新打开文件
    }
    else if (!enabled && m_logFile)
    {
        m_logFile->close();
        delete m_logFile;
        m_logFile = nullptr;

        if (m_logStream)
        {
            delete m_logStream;
            m_logStream = nullptr;
        }
    }
}

void LogManager::setTimestampEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_timestampEnabled = enabled;
    m_config["timestamp_enabled"] = enabled;
}

bool LogManager::isTimestampEnabled() const
{
    return m_timestampEnabled;
}

void LogManager::setTimestampFormat(const QString &format)
{
    QMutexLocker locker(&m_mutex);

    // 检查是否是预定义格式
    if (m_timestampFormats.contains(format))
    {
        m_timestampFormat = m_timestampFormats[format];
    }
    else
    {
        m_timestampFormat = format;
    }

    m_config["timestamp_format"] = m_timestampFormat;
}

QString LogManager::timestampFormat() const
{
    return m_timestampFormat;
}

QString LogManager::currentTimestampFormat() const
{
    return m_timestampFormat;
}

void LogManager::setEchoEnabled(bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_echoEnabled = enabled;
    m_config["echo_enabled"] = enabled;
}

bool LogManager::isEchoEnabled() const
{
    return m_echoEnabled;
}

void LogManager::setAutoSaveEnabled(bool enabled)
{
    m_autoSaveEnabled = enabled;
    m_config["auto_save"] = enabled;

    if (enabled)
    {
        m_autoSaveTimer->start();
    }
    else
    {
        m_autoSaveTimer->stop();
    }
}

bool LogManager::isAutoSaveEnabled() const
{
    return m_autoSaveEnabled;
}

void LogManager::setAutoSaveInterval(int seconds)
{
    m_autoSaveInterval = qMax(1, seconds);
    m_config["auto_save_interval"] = m_autoSaveInterval;
    m_autoSaveTimer->setInterval(m_autoSaveInterval * 1000);
}

int LogManager::autoSaveInterval() const
{
    return m_autoSaveInterval;
}

void LogManager::setLogLevel(LogLevel level)
{
    QMutexLocker locker(&m_mutex);
    m_currentLogLevel = level;
    m_config["log_level"] = static_cast<int>(level);
}

LogManager::LogLevel LogManager::logLevel() const
{
    return m_currentLogLevel;
}

void LogManager::setLevelEnabled(LogLevel level, bool enabled)
{
    QMutexLocker locker(&m_mutex);
    m_levelEnabled[level] = enabled;
}

bool LogManager::isLevelEnabled(LogLevel level) const
{
    QMutexLocker locker(&m_mutex);
    return m_levelEnabled.value(level, true);
}

void LogManager::setMaxFileSize(qint64 bytes)
{
    QMutexLocker locker(&m_mutex);
    m_maxFileSize = qMax(1024LL, bytes);                       // 最小1KB
    m_config["max_file_size"] = m_maxFileSize / (1024 * 1024); // 保存为MB
}

qint64 LogManager::maxFileSize() const
{
    return m_maxFileSize;
}

void LogManager::setMaxBackupFiles(int count)
{
    QMutexLocker locker(&m_mutex);
    m_maxBackupFiles = qMax(1, count);
    m_config["max_backup_files"] = m_maxBackupFiles;
}

int LogManager::maxBackupFiles() const
{
    return m_maxBackupFiles;
}

LogManager::LogStats LogManager::getLogStats() const
{
    QMutexLocker locker(&m_mutex);

    LogStats stats;
    stats.filePath = m_logFilePath;
    stats.fileExists = QFile::exists(m_logFilePath);

    if (stats.fileExists)
    {
        QFileInfo fileInfo(m_logFilePath);
        stats.fileSize = fileInfo.size();
        stats.lastModified = fileInfo.lastModified();
        stats.createdTime = fileInfo.birthTime();

        // 计算行数（简单估算）
        if (m_logFile && m_logFile->isOpen())
        {
            stats.lineCount = m_totalLogCount;
        }
        else
        {
            stats.lineCount = 0;
        }
    }
    else
    {
        stats.fileSize = 0;
        stats.lineCount = 0;
    }

    return stats;
}

void LogManager::clearLogs()
{
    QMutexLocker locker(&m_mutex);

    m_logBuffer.clear();
    m_totalLogCount = 0;

    if (m_logFile && m_logFile->isOpen())
    {
        m_logFile->resize(0);
    }
}

bool LogManager::exportLogs(const QString &filePath)
{
    QMutexLocker locker(&m_mutex);

    QFile exportFile(filePath);
    if (!exportFile.open(QIODevice::WriteOnly | QIODevice::Text))
    {
        return false;
    }

    QTextStream out(&exportFile);
    out.setCodec("UTF-8");

    // 写入缓冲区中的日志
    for (const QString &logLine : m_logBuffer)
    {
        out << logLine << "\n";
    }

    exportFile.close();
    return true;
}

QStringList LogManager::getRecentLogs(int count) const
{
    QMutexLocker locker(&m_mutex);

    if (count <= 0 || m_logBuffer.isEmpty())
    {
        return QStringList();
    }

    int startIndex = qMax(0, m_logBuffer.size() - count);
    return m_logBuffer.mid(startIndex);
}

QString LogManager::formatLogMessage(const QString &message, LogLevel level) const
{
    QString formatted;

    // 添加时间戳
    if (m_timestampEnabled)
    {
        formatted += QString("[%1] ").arg(generateTimestamp());
    }

    // 添加级别标识
    formatted += QString("[%1] ").arg(levelToString(level));

    // 添加消息内容
    formatted += message;

    return formatted;
}

QString LogManager::levelToString(LogLevel level) const
{
    switch (level)
    {
    case Debug:
        return "DEBUG";
    case Info:
        return "INFO";
    case Warning:
        return "WARN";
    case Error:
        return "ERROR";
    case System:
        return "SYS";
    case Input:
        return "IN";
    case Output:
        return "OUT";
    default:
        return "UNKNOWN";
    }
}

LogManager::LogLevel LogManager::stringToLevel(const QString &levelStr) const
{
    QString upper = levelStr.toUpper();

    if (upper == "DEBUG")
        return Debug;
    if (upper == "INFO")
        return Info;
    if (upper == "WARN" || upper == "WARNING")
        return Warning;
    if (upper == "ERROR")
        return Error;
    if (upper == "SYS" || upper == "SYSTEM")
        return System;
    if (upper == "IN" || upper == "INPUT")
        return Input;
    if (upper == "OUT" || upper == "OUTPUT")
        return Output;

    return Info; // 默认级别
}

void LogManager::flush()
{
    QMutexLocker locker(&m_mutex);

    if (m_logStream)
    {
        m_logStream->flush();
    }

    if (m_logFile)
    {
        m_logFile->flush();
    }
}

void LogManager::rotateLogs()
{
    QMutexLocker locker(&m_mutex);

    if (!m_logFile || !m_logFile->isOpen())
    {
        return;
    }

    rotateLogFile();
}

void LogManager::onAutoSaveTimer()
{
    flush();
}

void LogManager::checkFileSize()
{
    if (m_logFile && m_logFile->size() > m_maxFileSize)
    {
        rotateLogFile();
    }
}

void LogManager::writeToFile(const QString &formattedMessage)
{
    if (!m_logStream)
    {
        qDebug() << "LogManager::writeToFile() - 日志流为空，无法写入";
        return;
    }

    qDebug() << "LogManager::writeToFile() - 写入日志:" << formattedMessage.left(50);
    *m_logStream << formattedMessage << "\n";
    m_logStream->flush();
    qDebug() << "LogManager::writeToFile() - 写入完成";

    // 检查文件大小
    checkFileSize();
}

void LogManager::rotateLogFile()
{
    if (!m_logFile)
    {
        return;
    }

    QString baseName = QFileInfo(m_logFilePath).baseName();
    QString suffix = QFileInfo(m_logFilePath).suffix();
    QString dir = QFileInfo(m_logFilePath).absolutePath();

    // 关闭当前文件
    m_logFile->close();

    // 重命名现有备份文件
    for (int i = m_maxBackupFiles - 1; i >= 1; --i)
    {
        QString oldName = QString("%1/%2.%3.%4").arg(dir, baseName).arg(i).arg(suffix);
        QString newName = QString("%1/%2.%3.%4").arg(dir, baseName).arg(i + 1).arg(suffix);

        if (QFile::exists(oldName))
        {
            QFile::remove(newName);
            QFile::rename(oldName, newName);
        }
    }

    // 重命名当前文件为第一个备份
    QString backupName = QString("%1/%2.1.%3").arg(dir, baseName, suffix);
    QFile::remove(backupName);
    QFile::rename(m_logFilePath, backupName);

    // 重新打开日志文件
    if (m_logFile->open(QIODevice::WriteOnly | QIODevice::Append))
    {
        emit fileRotated(backupName, m_logFilePath);
    }
}

QString LogManager::generateTimestamp() const
{
    return QDateTime::currentDateTime().toString(m_timestampFormat);
}

QString LogManager::getDefaultTimestampFormat(const QString &formatName) const
{
    return m_timestampFormats.value(formatName, "yyyy-MM-dd hh:mm:ss");
}

bool LogManager::shouldLogLevel(LogLevel level) const
{
    return level >= m_currentLogLevel && m_levelEnabled.value(level, true);
}

void LogManager::ensureLogDirectory()
{
    QFileInfo fileInfo(m_logFilePath);
    QDir dir = fileInfo.absoluteDir();
    if (!dir.exists())
    {
        dir.mkpath(".");
    }
}

void LogManager::setLogFile(const QString &filePath)
{
    qDebug() << "LogManager::setLogFile() - 开始，路径:" << filePath;
    QMutexLocker locker(&m_mutex);
    qDebug() << "LogManager::setLogFile() - 获取互斥锁成功";
    setLogFileInternal(filePath);
    qDebug() << "LogManager::setLogFile() - 完成";
}
