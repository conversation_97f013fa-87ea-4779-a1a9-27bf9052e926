/****************************************************************************
** Meta object code from reading C++ file 'ConnectionManager.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ConnectionManager.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ConnectionManager.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ConnectionManager_t {
    QByteArrayData data[37];
    char stringdata0[423];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ConnectionManager_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ConnectionManager_t qt_meta_stringdata_ConnectionManager = {
    {
QT_MOC_LITERAL(0, 0, 17), // "ConnectionManager"
QT_MOC_LITERAL(1, 18, 13), // "statusChanged"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 6), // "status"
QT_MOC_LITERAL(4, 40, 7), // "details"
QT_MOC_LITERAL(5, 48, 12), // "dataReceived"
QT_MOC_LITERAL(6, 61, 4), // "data"
QT_MOC_LITERAL(7, 66, 15), // "rawDataReceived"
QT_MOC_LITERAL(8, 82, 13), // "errorOccurred"
QT_MOC_LITERAL(9, 96, 5), // "error"
QT_MOC_LITERAL(10, 102, 21), // "connectionEstablished"
QT_MOC_LITERAL(11, 124, 14), // "connectionLost"
QT_MOC_LITERAL(12, 139, 10), // "testResult"
QT_MOC_LITERAL(13, 150, 7), // "success"
QT_MOC_LITERAL(14, 158, 7), // "message"
QT_MOC_LITERAL(15, 166, 9), // "reconnect"
QT_MOC_LITERAL(16, 176, 14), // "testConnection"
QT_MOC_LITERAL(17, 191, 6), // "params"
QT_MOC_LITERAL(18, 198, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(19, 224, 14), // "onDataReceived"
QT_MOC_LITERAL(20, 239, 15), // "onErrorOccurred"
QT_MOC_LITERAL(21, 255, 16), // "onReconnectTimer"
QT_MOC_LITERAL(22, 272, 14), // "onAdbConnected"
QT_MOC_LITERAL(23, 287, 17), // "onAdbDisconnected"
QT_MOC_LITERAL(24, 305, 14), // "ConnectionType"
QT_MOC_LITERAL(25, 320, 4), // "None"
QT_MOC_LITERAL(26, 325, 6), // "Serial"
QT_MOC_LITERAL(27, 332, 3), // "TCP"
QT_MOC_LITERAL(28, 336, 3), // "ADB"
QT_MOC_LITERAL(29, 340, 7), // "Network"
QT_MOC_LITERAL(30, 348, 3), // "FTP"
QT_MOC_LITERAL(31, 352, 16), // "ConnectionStatus"
QT_MOC_LITERAL(32, 369, 12), // "Disconnected"
QT_MOC_LITERAL(33, 382, 10), // "Connecting"
QT_MOC_LITERAL(34, 393, 9), // "Connected"
QT_MOC_LITERAL(35, 403, 13), // "Disconnecting"
QT_MOC_LITERAL(36, 417, 5) // "Error"

    },
    "ConnectionManager\0statusChanged\0\0"
    "status\0details\0dataReceived\0data\0"
    "rawDataReceived\0errorOccurred\0error\0"
    "connectionEstablished\0connectionLost\0"
    "testResult\0success\0message\0reconnect\0"
    "testConnection\0params\0onConnectionStatusChanged\0"
    "onDataReceived\0onErrorOccurred\0"
    "onReconnectTimer\0onAdbConnected\0"
    "onAdbDisconnected\0ConnectionType\0None\0"
    "Serial\0TCP\0ADB\0Network\0FTP\0ConnectionStatus\0"
    "Disconnected\0Connecting\0Connected\0"
    "Disconnecting\0Error"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ConnectionManager[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      15,   14, // methods
       0,    0, // properties
       2,  124, // enums/sets
       0,    0, // constructors
       0,       // flags
       7,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    2,   89,    2, 0x06 /* Public */,
       5,    1,   94,    2, 0x06 /* Public */,
       7,    1,   97,    2, 0x06 /* Public */,
       8,    1,  100,    2, 0x06 /* Public */,
      10,    0,  103,    2, 0x06 /* Public */,
      11,    0,  104,    2, 0x06 /* Public */,
      12,    2,  105,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      15,    0,  110,    2, 0x0a /* Public */,
      16,    1,  111,    2, 0x0a /* Public */,
      18,    0,  114,    2, 0x08 /* Private */,
      19,    1,  115,    2, 0x08 /* Private */,
      20,    1,  118,    2, 0x08 /* Private */,
      21,    0,  121,    2, 0x08 /* Private */,
      22,    0,  122,    2, 0x08 /* Private */,
      23,    0,  123,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    3,    4,
    QMetaType::Void, QMetaType::QString,    6,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool, QMetaType::QString,   13,   14,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void, QMetaType::QVariantMap,   17,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QByteArray,    6,
    QMetaType::Void, QMetaType::QString,    9,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

 // enums: name, alias, flags, count, data
      24,   24, 0x0,    6,  134,
      31,   31, 0x0,    5,  146,

 // enum data: key, value
      25, uint(ConnectionManager::None),
      26, uint(ConnectionManager::Serial),
      27, uint(ConnectionManager::TCP),
      28, uint(ConnectionManager::ADB),
      29, uint(ConnectionManager::Network),
      30, uint(ConnectionManager::FTP),
      32, uint(ConnectionManager::Disconnected),
      33, uint(ConnectionManager::Connecting),
      34, uint(ConnectionManager::Connected),
      35, uint(ConnectionManager::Disconnecting),
      36, uint(ConnectionManager::Error),

       0        // eod
};

void ConnectionManager::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ConnectionManager *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->statusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 1: _t->dataReceived((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 2: _t->rawDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 3: _t->errorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 4: _t->connectionEstablished(); break;
        case 5: _t->connectionLost(); break;
        case 6: _t->testResult((*reinterpret_cast< bool(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 7: _t->reconnect(); break;
        case 8: _t->testConnection((*reinterpret_cast< const QVariantMap(*)>(_a[1]))); break;
        case 9: _t->onConnectionStatusChanged(); break;
        case 10: _t->onDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 11: _t->onErrorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 12: _t->onReconnectTimer(); break;
        case 13: _t->onAdbConnected(); break;
        case 14: _t->onAdbDisconnected(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ConnectionManager::*)(const QString & , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::statusChanged)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::dataReceived)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::rawDataReceived)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::errorOccurred)) {
                *result = 3;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::connectionEstablished)) {
                *result = 4;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)();
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::connectionLost)) {
                *result = 5;
                return;
            }
        }
        {
            using _t = void (ConnectionManager::*)(bool , const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ConnectionManager::testResult)) {
                *result = 6;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ConnectionManager::staticMetaObject = { {
    &QObject::staticMetaObject,
    qt_meta_stringdata_ConnectionManager.data,
    qt_meta_data_ConnectionManager,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ConnectionManager::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ConnectionManager::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ConnectionManager.stringdata0))
        return static_cast<void*>(this);
    return QObject::qt_metacast(_clname);
}

int ConnectionManager::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QObject::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 15)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 15;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 15)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 15;
    }
    return _id;
}

// SIGNAL 0
void ConnectionManager::statusChanged(const QString & _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ConnectionManager::dataReceived(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ConnectionManager::rawDataReceived(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ConnectionManager::errorOccurred(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}

// SIGNAL 4
void ConnectionManager::connectionEstablished()
{
    QMetaObject::activate(this, &staticMetaObject, 4, nullptr);
}

// SIGNAL 5
void ConnectionManager::connectionLost()
{
    QMetaObject::activate(this, &staticMetaObject, 5, nullptr);
}

// SIGNAL 6
void ConnectionManager::testResult(bool _t1, const QString & _t2)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)), const_cast<void*>(reinterpret_cast<const void*>(&_t2)) };
    QMetaObject::activate(this, &staticMetaObject, 6, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
