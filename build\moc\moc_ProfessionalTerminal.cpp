/****************************************************************************
** Meta object code from reading C++ file 'ProfessionalTerminal.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../ProfessionalTerminal.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'ProfessionalTerminal.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_ProfessionalTerminal_t {
    QByteArrayData data[20];
    char stringdata0[238];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_ProfessionalTerminal_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_ProfessionalTerminal_t qt_meta_stringdata_ProfessionalTerminal = {
    {
QT_MOC_LITERAL(0, 0, 20), // "ProfessionalTerminal"
QT_MOC_LITERAL(1, 21, 14), // "commandEntered"
QT_MOC_LITERAL(2, 36, 0), // ""
QT_MOC_LITERAL(3, 37, 7), // "command"
QT_MOC_LITERAL(4, 45, 10), // "dataToSend"
QT_MOC_LITERAL(5, 56, 4), // "data"
QT_MOC_LITERAL(6, 61, 21), // "timestampStateChanged"
QT_MOC_LITERAL(7, 83, 7), // "enabled"
QT_MOC_LITERAL(8, 91, 15), // "fontSizeChanged"
QT_MOC_LITERAL(9, 107, 4), // "size"
QT_MOC_LITERAL(10, 112, 23), // "onCursorPositionChanged"
QT_MOC_LITERAL(11, 136, 13), // "onTextChanged"
QT_MOC_LITERAL(12, 150, 15), // "showContextMenu"
QT_MOC_LITERAL(13, 166, 3), // "pos"
QT_MOC_LITERAL(14, 170, 13), // "clearTerminal"
QT_MOC_LITERAL(15, 184, 13), // "copySelection"
QT_MOC_LITERAL(16, 198, 9), // "pasteText"
QT_MOC_LITERAL(17, 208, 9), // "selectAll"
QT_MOC_LITERAL(18, 218, 10), // "saveToFile"
QT_MOC_LITERAL(19, 229, 8) // "findText"

    },
    "ProfessionalTerminal\0commandEntered\0"
    "\0command\0dataToSend\0data\0timestampStateChanged\0"
    "enabled\0fontSizeChanged\0size\0"
    "onCursorPositionChanged\0onTextChanged\0"
    "showContextMenu\0pos\0clearTerminal\0"
    "copySelection\0pasteText\0selectAll\0"
    "saveToFile\0findText"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_ProfessionalTerminal[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      13,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       4,       // signalCount

 // signals: name, argc, parameters, tag, flags
       1,    1,   79,    2, 0x06 /* Public */,
       4,    1,   82,    2, 0x06 /* Public */,
       6,    1,   85,    2, 0x06 /* Public */,
       8,    1,   88,    2, 0x06 /* Public */,

 // slots: name, argc, parameters, tag, flags
      10,    0,   91,    2, 0x08 /* Private */,
      11,    0,   92,    2, 0x08 /* Private */,
      12,    1,   93,    2, 0x08 /* Private */,
      14,    0,   96,    2, 0x08 /* Private */,
      15,    0,   97,    2, 0x08 /* Private */,
      16,    0,   98,    2, 0x08 /* Private */,
      17,    0,   99,    2, 0x08 /* Private */,
      18,    0,  100,    2, 0x08 /* Private */,
      19,    0,  101,    2, 0x08 /* Private */,

 // signals: parameters
    QMetaType::Void, QMetaType::QString,    3,
    QMetaType::Void, QMetaType::QByteArray,    5,
    QMetaType::Void, QMetaType::Bool,    7,
    QMetaType::Void, QMetaType::Int,    9,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QPoint,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void ProfessionalTerminal::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<ProfessionalTerminal *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->commandEntered((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 1: _t->dataToSend((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 2: _t->timestampStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 3: _t->fontSizeChanged((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 4: _t->onCursorPositionChanged(); break;
        case 5: _t->onTextChanged(); break;
        case 6: _t->showContextMenu((*reinterpret_cast< const QPoint(*)>(_a[1]))); break;
        case 7: _t->clearTerminal(); break;
        case 8: _t->copySelection(); break;
        case 9: _t->pasteText(); break;
        case 10: _t->selectAll(); break;
        case 11: _t->saveToFile(); break;
        case 12: _t->findText(); break;
        default: ;
        }
    } else if (_c == QMetaObject::IndexOfMethod) {
        int *result = reinterpret_cast<int *>(_a[0]);
        {
            using _t = void (ProfessionalTerminal::*)(const QString & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProfessionalTerminal::commandEntered)) {
                *result = 0;
                return;
            }
        }
        {
            using _t = void (ProfessionalTerminal::*)(const QByteArray & );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProfessionalTerminal::dataToSend)) {
                *result = 1;
                return;
            }
        }
        {
            using _t = void (ProfessionalTerminal::*)(bool );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProfessionalTerminal::timestampStateChanged)) {
                *result = 2;
                return;
            }
        }
        {
            using _t = void (ProfessionalTerminal::*)(int );
            if (*reinterpret_cast<_t *>(_a[1]) == static_cast<_t>(&ProfessionalTerminal::fontSizeChanged)) {
                *result = 3;
                return;
            }
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject ProfessionalTerminal::staticMetaObject = { {
    &QWidget::staticMetaObject,
    qt_meta_stringdata_ProfessionalTerminal.data,
    qt_meta_data_ProfessionalTerminal,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *ProfessionalTerminal::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *ProfessionalTerminal::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_ProfessionalTerminal.stringdata0))
        return static_cast<void*>(this);
    return QWidget::qt_metacast(_clname);
}

int ProfessionalTerminal::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QWidget::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 13)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 13;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 13)
            *reinterpret_cast<int*>(_a[0]) = -1;
        _id -= 13;
    }
    return _id;
}

// SIGNAL 0
void ProfessionalTerminal::commandEntered(const QString & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 0, _a);
}

// SIGNAL 1
void ProfessionalTerminal::dataToSend(const QByteArray & _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 1, _a);
}

// SIGNAL 2
void ProfessionalTerminal::timestampStateChanged(bool _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 2, _a);
}

// SIGNAL 3
void ProfessionalTerminal::fontSizeChanged(int _t1)
{
    void *_a[] = { nullptr, const_cast<void*>(reinterpret_cast<const void*>(&_t1)) };
    QMetaObject::activate(this, &staticMetaObject, 3, _a);
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
