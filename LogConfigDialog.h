#ifndef LOGCONFIGDIALOG_H
#define LOGCONFIGDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QComboBox>
#include <QLineEdit>
#include <QPushButton>
#include <QSpinBox>
#include <QLabel>
#include <QFileDialog>
#include <QVariantMap>
#include <QScrollArea>

class LogConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit LogConfigDialog(QWidget *parent = nullptr);
    ~LogConfigDialog();

    void setConfig(const QVariantMap &config);
    QVariantMap getConfig() const;

public slots:
    void accept() override;
    void reject() override;

signals:
    void configChanged(const QString &key, const QVariant &value);

private slots:
    void onBrowseLogFile();
    void onResetToDefaults();
    void onTestLogFile();

private:
    void setupUI();
    void loadConfig();
    void saveConfig();
    void updateUI();

private:
    // 主布局
    QScrollArea *m_scrollArea;
    QWidget *m_scrollWidget;

    // 时间戳配置
    QGroupBox *m_timestampGroup;
    QCheckBox *m_timestampEnabledCheck;
    QComboBox *m_timestampFormatCombo;
    QLineEdit *m_customFormatEdit;

    // 文件配置
    QGroupBox *m_fileGroup;
    QCheckBox *m_fileEnabledCheck;
    QLineEdit *m_filePathEdit;
    QPushButton *m_browseFileBtn;
    QCheckBox *m_autoSaveCheck;
    QSpinBox *m_autoSaveIntervalSpin;

    // 显示配置
    QGroupBox *m_displayGroup;
    QCheckBox *m_echoEnabledCheck;
    QComboBox *m_logLevelCombo;
    QSpinBox *m_maxLinesSpin;

    // 文件轮转配置
    QGroupBox *m_rotationGroup;
    QCheckBox *m_rotationEnabledCheck;
    QSpinBox *m_maxFileSizeSpin;
    QSpinBox *m_maxBackupFilesSpin;

    // 缓冲区配置
    QGroupBox *m_bufferGroup;
    QCheckBox *m_bufferEnabledCheck;
    QSpinBox *m_bufferSizeSpin;

    // 日志字体配置
    QGroupBox *m_logFontGroup;
    QComboBox *m_logFontFamilyCombo;
    QSpinBox *m_logFontSizeSpin;
    QComboBox *m_logFontWeightCombo;

    // 按钮
    QPushButton *m_okBtn;
    QPushButton *m_cancelBtn;
    QPushButton *m_resetBtn;
    QPushButton *m_testBtn;

    // 配置数据
    QVariantMap m_config;
};

#endif // LOGCONFIGDIALOG_H
