#include "MainWindow.h"
#include "ConnectionManager.h"
#include "ConfigManager.h"
#include "LogManager.h"
#include "CommandHistory.h"
#include "InteractiveTerminal.h"
#include "ProfessionalTerminal.h"
#include "CommandListWidget.h"
#include "ConnectionDialog.h"
#include "LogConfigDialog.h"
#include "BackgroundConfigDialog.h"
#include "TerminalConfigDialog.h"
#include "ConnectionConfigDialog.h"

#include <QApplication>
#include <QMessageBox>
#include <QFileDialog>
#include <QInputDialog>
#include <QSplitter>
#include <QGroupBox>
#include <QFormLayout>
#include <QSpacerItem>
#include <QDateTime>
#include <QStandardPaths>
#include <QDir>
#include <QJsonDocument>
#include <QJsonObject>
#include <QDesktopServices>
#include <QUrl>
#include <QSettings>
#include <QTimer>
#include <QMenu>
#include <QAction>
#include <QProcess>
#include <QTextCodec>
#include <QDebug>
#include <QInputDialog>
#include <QMessageBox>
#include <QGroupBox>
#include <QRadioButton>
#include <QShortcut>
#include <QKeySequence>
#include <QTextEdit>
#include <QTcpServer>
#include <QTcpSocket>
#include <QNetworkInterface>
#include <QHostAddress>
#include <QJsonDocument>
#include <QJsonObject>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent), m_connectionManager(nullptr), m_configManager(nullptr), m_logManager(nullptr), m_commandHistory(nullptr), m_centralWidget(nullptr), m_mainSplitter(nullptr), m_historyFrame(nullptr), m_mainWorkFrame(nullptr), m_commandsFrame(nullptr), m_menuBar(nullptr), m_toolBar(nullptr), m_statusBar(nullptr), m_topMenuFrame(nullptr), m_menuTitle(nullptr), m_configBtn(nullptr), m_importExportBtn(nullptr), m_helpBtn(nullptr), m_quickCommandFrame(nullptr), m_cmdBtn1(nullptr), m_cmdBtn2(nullptr), m_cmdBtn3(nullptr), m_timestampBtn(nullptr), m_quickCmdInput(nullptr), m_quickConnectBtn(nullptr), m_quickDisconnectBtn(nullptr), m_quickClearBtn(nullptr), m_quickPauseBtn(nullptr), m_findBtn(nullptr), m_highlightBtn(nullptr), m_remoteBtn(nullptr), m_logFrame(nullptr), m_terminal(nullptr), m_professionalTerminal(nullptr), m_commandInputFrame(nullptr), m_statusLabel(nullptr), m_commandInput(nullptr), m_sendBtn(nullptr), m_connectionTypeLabel(nullptr), m_connectionBtn(nullptr), m_commandsTitle(nullptr), m_toggleCommandsBtn(nullptr), m_commandsList(nullptr), m_addCommandBtn(nullptr), m_statusInfo(nullptr), m_connectionDetailsLabel(nullptr), m_connectionTimeLabel(nullptr), m_versionLabel(nullptr), m_connectionDialog(nullptr), m_logConfigDialog(nullptr), m_backgroundConfigDialog(nullptr), m_terminalConfigDialog(nullptr), m_commandsPanelVisible(true), m_isConnected(false), m_timestampEnabled(false), m_processingRawData(false), m_connectionType("未连接"), m_currentConnectionItem(nullptr), m_uiUpdatePending(false), m_remoteServer(nullptr), m_remoteClient(nullptr), m_remoteServerEnabled(false), m_remoteServerPort(8080), m_autoSaveTimer(nullptr), m_statusUpdateTimer(nullptr), m_connectionTimeUpdateTimer(nullptr), m_uiUpdateTimer(nullptr), m_settings(nullptr)
{
    qDebug() << "MainWindow constructor started";

    try
    {

        // 初始化核心组件
        qDebug() << "Creating core components...";
        m_configManager = new ConfigManager(this);
        m_logManager = new LogManager(this);
        m_commandHistory = new CommandHistory(this);
        m_connectionManager = new ConnectionManager(this);

        // 初始化设置
        m_settings = new QSettings(this);

        // 设置窗口属性
        qDebug() << "Setting window properties...";
        setWindowTitle("RF调试工具 v2.0 - Qt版本");
        setMinimumSize(1200, 800);

        // 加载窗口配置
        qDebug() << "Loading window config...";
        loadWindowConfig();

        // 设置UI
        qDebug() << "Setting up UI...";
        setupUI();
        qDebug() << "Setting up connections...";
        setupConnections();
        qDebug() << "Connections setup completed";

        // 在UI创建完成后，应用窗口配置中的面板状态
        qDebug() << "Applying panel states...";
        applyPanelStates();

        // 添加调试快捷键
        QShortcut *forceShowShortcut = new QShortcut(QKeySequence("Ctrl+F12"), this);
        connect(forceShowShortcut, &QShortcut::activated, this, &MainWindow::forceShowCommandButton);

        // 应用配置
        qDebug() << "Applying config...";
        try
        {
            applyConfig();
            qDebug() << "Config applied successfully";
        }
        catch (const std::exception &e)
        {
            qDebug() << "Exception in applyConfig:" << e.what();
        }
        catch (...)
        {
            qDebug() << "Unknown exception in applyConfig";
        }
        qDebug() << "Applying Mac style...";
        applyMacStyle();
        qDebug() << "Mac style applied";

        // 加载常用命令
        qDebug() << "Loading common commands...";
        try
        {
            loadCommonCommands();
            qDebug() << "Common commands loaded";
        }
        catch (const std::exception &e)
        {
            qDebug() << "Exception in loadCommonCommands:" << e.what();
        }
        catch (...)
        {
            qDebug() << "Unknown exception in loadCommonCommands";
        }

        // 设置定时器 - 优化自动保存频率
        qDebug() << "Setting up auto-save timer...";
        m_autoSaveTimer = new QTimer(this);
        m_autoSaveTimer->setInterval(60000); // 60秒自动保存，减少IO频率
        m_autoSaveTimer->setSingleShot(false);
        connect(m_autoSaveTimer, &QTimer::timeout, this, &MainWindow::autoSave);
        m_autoSaveTimer->start();
        qDebug() << "Auto-save timer started";

        // 设置连接时间更新定时器 - 适中的更新频率
        qDebug() << "Setting up connection time update timer...";
        m_connectionTimeUpdateTimer = new QTimer(this);
        m_connectionTimeUpdateTimer->setInterval(2000); // 每2秒更新一次，平衡性能和实时性
        m_connectionTimeUpdateTimer->setSingleShot(false);
        connect(m_connectionTimeUpdateTimer, &QTimer::timeout, this, [this]()
                {
                    if (m_isConnected)
                    {
                        updateConnectionDetails();
                    } });
        qDebug() << "Connection time update timer created";

        qDebug() << "Setting up status update timer...";
        m_statusUpdateTimer = new QTimer(this);
        m_statusUpdateTimer->setInterval(1000); // 1秒更新状态
        connect(m_statusUpdateTimer, &QTimer::timeout, this, &MainWindow::updateConnectionStatus);
        m_statusUpdateTimer->start();
        qDebug() << "Status update timer started";

        // 应用配置
        qDebug() << "Applying initial configuration...";
        applyConfig();
        qDebug() << "Initial configuration applied";

        // 设置初始状态
        qDebug() << "Updating connection status...";
        updateConnectionStatus();
        qDebug() << "Updating command buttons...";
        updateCommandButtons();
        qDebug() << "Initial status updated";

        // 显示欢迎信息
        qDebug() << "Adding welcome messages to terminal...";
        if (m_terminal)
        {
            m_terminal->appendMessage("=== RF调试工具 Qt版本 ===", LogManager::Info);
            m_terminal->appendMessage("", LogManager::Info);
            m_terminal->appendMessage("✅ 功能特性：", LogManager::Info);
            m_terminal->appendMessage("• 多种连接方式：ADB、串口、SSH、网络、FTP", LogManager::Info);
            m_terminal->appendMessage("• 串口高波特率支持：9600-3,000,000", LogManager::Info);
            m_terminal->appendMessage("• 智能日志管理：时间戳、存储、回显配置", LogManager::Info);
            m_terminal->appendMessage("• 背景配置：颜色、图片、透明度（精度0.01）", LogManager::Info);
            m_terminal->appendMessage("• 命令管理：右键菜单、启用/禁用、面板开关", LogManager::Info);
            m_terminal->appendMessage("• Mac风格界面：毛玻璃效果、现代化设计", LogManager::Info);
            m_terminal->appendMessage("", LogManager::Info);
            m_terminal->appendMessage("🚀 开始使用：点击'连接设备'按钮配置连接", LogManager::Info);
            m_terminal->appendMessage("", LogManager::Info);
        }
        else
        {
            qDebug() << "ERROR: m_terminal is null!";
        }

        qDebug() << "MainWindow constructor completed successfully";
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception in MainWindow constructor:" << e.what();
        throw;
    }
    catch (...)
    {
        qDebug() << "Unknown exception in MainWindow constructor";
        throw;
    }
}

MainWindow::~MainWindow()
{
    qDebug() << "MainWindow::~MainWindow() - 开始析构";

    // 先断开连接（非阻塞方式）
    if (m_isConnected && m_connectionManager)
    {
        qDebug() << "MainWindow::~MainWindow() - 断开连接";
        m_connectionManager->disconnectFromDevice();
    }

    // 保存配置
    qDebug() << "MainWindow::~MainWindow() - 保存配置";
    saveWindowConfig();
    saveCommonCommands();
    saveConnectionHistory();

    qDebug() << "MainWindow::~MainWindow() - 析构完成";
}

void MainWindow::setupUI()
{
    // 创建中央部件
    m_centralWidget = new QWidget;
    setCentralWidget(m_centralWidget);

    // 创建主布局
    QVBoxLayout *mainLayout = new QVBoxLayout(m_centralWidget);
    mainLayout->setContentsMargins(15, 15, 15, 15); // 减少主布局边距
    mainLayout->setSpacing(10);                     // 减少主布局间距

    // 创建各个区域
    createTopMenuArea();
    createMainContentArea();
    createBottomStatusArea();

    // 添加到主布局
    mainLayout->addWidget(m_topMenuFrame);
    mainLayout->addWidget(m_mainSplitter, 1);

    // 设置状态栏
    setupStatusBar();
}

void MainWindow::createTopMenuArea()
{
    m_topMenuFrame = new QFrame;
    m_topMenuFrame->setObjectName("menuFrame");
    m_topMenuFrame->setFixedHeight(50); // 减少高度，给日志区域更多空间

    QHBoxLayout *menuLayout = new QHBoxLayout(m_topMenuFrame);
    menuLayout->setContentsMargins(15, 8, 15, 8); // 减少边距

    // 菜单标题
    m_menuTitle = new QLabel("RF调试工具");
    m_menuTitle->setObjectName("menuTitle");
    m_menuTitle->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);

    // 菜单按钮
    m_configBtn = new QPushButton("配置");
    m_configBtn->setObjectName("menuButton");

    m_importExportBtn = new QPushButton("导入/导出");
    m_importExportBtn->setObjectName("menuButton");

    m_helpBtn = new QPushButton("帮助");
    m_helpBtn->setObjectName("menuButton");

    menuLayout->addWidget(m_menuTitle);
    menuLayout->addStretch();
    menuLayout->addWidget(m_configBtn);
    menuLayout->addWidget(m_importExportBtn);
    menuLayout->addWidget(m_helpBtn);
}

void MainWindow::createMainContentArea()
{
    m_mainSplitter = new QSplitter(Qt::Horizontal);

    // 创建历史连接面板
    createHistoryPanel();

    // 创建主工作区域
    createMainWorkArea();

    // 创建常用命令面板
    createCommandsPanel();

    // 添加到分割器
    m_mainSplitter->addWidget(m_historyFrame);
    m_mainSplitter->addWidget(m_mainWorkFrame);
    m_mainSplitter->addWidget(m_commandsFrame);

    // 设置分割器比例
    m_mainSplitter->setStretchFactor(0, 0); // 历史面板固定宽度
    m_mainSplitter->setStretchFactor(1, 1); // 主工作区域可伸缩
    m_mainSplitter->setStretchFactor(2, 0); // 命令面板固定宽度

    // 设置初始大小
    m_mainSplitter->setSizes({200, 800, 150});
}

void MainWindow::createHistoryPanel()
{
    m_historyFrame = new QFrame;
    m_historyFrame->setObjectName("historyFrame");
    m_historyFrame->setFixedWidth(220); // 增加宽度以显示更多信息

    // 设置历史面板整体样式 - 参考您提供的样式
    m_historyFrame->setStyleSheet(
        "#historyFrame {"
        "    background: #f8f9fa;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 4px;"
        "    margin: 2px;"
        "}");

    QVBoxLayout *historyLayout = new QVBoxLayout(m_historyFrame);
    historyLayout->setContentsMargins(12, 12, 12, 12);
    historyLayout->setSpacing(8);

    // 标题和隐藏按钮
    QHBoxLayout *titleLayout = new QHBoxLayout;
    titleLayout->setContentsMargins(0, 0, 0, 0);

    QLabel *historyTitle = new QLabel("连接管理");
    historyTitle->setObjectName("historyTitle");
    historyTitle->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    historyTitle->setStyleSheet(
        "#historyTitle {"
        "    font-size: 13px;"
        "    font-weight: bold;"
        "    color: #333333;"
        "    padding: 4px 0px;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "}");

    m_hideHistoryBtn = new QPushButton("✕");
    m_hideHistoryBtn->setObjectName("hideHistoryBtn");
    m_hideHistoryBtn->setFixedSize(24, 24);
    m_hideHistoryBtn->setToolTip("隐藏历史连接面板");

    // 设置隐藏按钮样式 - 简洁设计
    m_hideHistoryBtn->setStyleSheet(
        "QPushButton {"
        "    background: #ffffff;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 12px;"
        "    font-size: 11px;"
        "    font-weight: bold;"
        "    color: #666666;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "}"
        "QPushButton:hover {"
        "    background: #f0f0f0;"
        "    border: 1px solid #4a90e2;"
        "    color: #4a90e2;"
        "}"
        "QPushButton:pressed {"
        "    background: #e0e0e0;"
        "    color: #333333;"
        "}");

    titleLayout->addWidget(historyTitle);
    titleLayout->addStretch(); // 添加弹性空间
    titleLayout->addWidget(m_hideHistoryBtn);

    // 历史连接列表
    m_historyList = new QListWidget;
    m_historyList->setObjectName("historyList");
    m_historyList->setMaximumHeight(350);
    m_historyList->setMinimumHeight(200);
    m_historyList->setContextMenuPolicy(Qt::CustomContextMenu);

    // 设置历史列表样式 - 恢复正常样式应用
    m_historyList->setStyleSheet(
        "QListWidget {"
        "    background: #ffffff;"
        "    border: 1px solid #d0d0d0;"
        "    border-radius: 4px;"
        "    outline: none;"
        "    padding: 2px;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "}"
        "QListWidget::item {"
        "    background: #ffffff;"
        "    border: 1px solid #e0e0e0;"
        "    border-radius: 3px;"
        "    padding: 8px 12px;"
        "    margin: 1px;"
        "    min-height: 40px;"
        "    font-size: 12px;"
        "    color: #333333;"
        "}"
        "QListWidget::item:hover {"
        "    background: #f0f8ff;"
        "    border: 1px solid #4a90e2;"
        "    color: #2c5aa0;"
        "}"
        "QListWidget::item:selected {"
        "    background: #e6f3ff;"
        "    border: 1px solid #4a90e2;"
        "    color: #2c5aa0;"
        "}");

    // 显示历史面板按钮（初始隐藏）
    m_showHistoryBtn = new QPushButton("连接管理");
    m_showHistoryBtn->setObjectName("showHistoryBtn");
    m_showHistoryBtn->setFixedSize(80, 32);
    m_showHistoryBtn->setToolTip("显示连接管理面板");
    m_showHistoryBtn->setVisible(false); // 初始隐藏

    // 设置显示按钮样式 - 简洁设计
    m_showHistoryBtn->setStyleSheet(
        "QPushButton {"
        "    background: #4a90e2;"
        "    color: white;"
        "    border: 1px solid #4a90e2;"
        "    border-radius: 16px;"
        "    font-size: 11px;"
        "    font-weight: bold;"
        "    padding: 4px 8px;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "}"
        "QPushButton:hover {"
        "    background: #357abd;"
        "    border: 1px solid #357abd;"
        "}"
        "QPushButton:pressed {"
        "    background: #2c5aa0;"
        "    border: 1px solid #2c5aa0;"
        "}");

    historyLayout->addLayout(titleLayout);
    historyLayout->addWidget(m_historyList);
    historyLayout->addStretch();

    // 加载历史连接记录
    loadConnectionHistory();
}

void MainWindow::createMainWorkArea()
{
    m_mainWorkFrame = new QFrame;
    m_mainWorkFrame->setObjectName("mainWorkArea");

    QVBoxLayout *mainAreaLayout = new QVBoxLayout(m_mainWorkFrame);
    mainAreaLayout->setContentsMargins(15, 8, 15, 15); // 恢复合理的上边距
    mainAreaLayout->setSpacing(8);                     // 恢复合理的间距

    // 创建显示命令栏按钮（参考历史面板的实现方式）
    m_showCommandsBtn = new QPushButton("📋 命令");
    m_showCommandsBtn->setObjectName("showCommandsBtn");
    m_showCommandsBtn->setFixedSize(80, 32);
    m_showCommandsBtn->setToolTip("显示常用命令栏");
    m_showCommandsBtn->setVisible(false); // 初始隐藏，和历史面板一样

    // 设置显示按钮样式 - 参考历史面板的样式
    m_showCommandsBtn->setStyleSheet(
        "QPushButton {"
        "    background: #4a90e2;"
        "    color: white;"
        "    border: 1px solid #4a90e2;"
        "    border-radius: 16px;"
        "    font-size: 11px;"
        "    font-weight: bold;"
        "    padding: 4px 8px;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "}"
        "QPushButton:hover {"
        "    background: #357abd;"
        "    border: 1px solid #357abd;"
        "}"
        "QPushButton:pressed {"
        "    background: #2c5aa0;"
        "    border: 1px solid #2c5aa0;"
        "}");

    // 设置按钮样式，与历史面板按钮保持一致
    m_showCommandsBtn->setStyleSheet(
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: 2px solid #1565c0;"
        "    border-radius: 16px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    padding: 4px 8px;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "    border: 2px solid #0d47a1;"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1565c0, stop:1 #0d47a1);"
        "    border: 2px solid #01579b;"
        "}");

    // 创建各个子区域
    createQuickCommandsArea();
    createLogDisplayArea();
    createCommandInputArea();

    // 创建顶部布局，包含显示按钮 - 使用固定宽度的spacer
    QHBoxLayout *topLayout = new QHBoxLayout;
    topLayout->setContentsMargins(0, 0, 0, 0);

    // 设置按钮的大小策略，防止被拉伸
    if (m_showHistoryBtn)
    {
        m_showHistoryBtn->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    }
    if (m_showCommandsBtn)
    {
        m_showCommandsBtn->setSizePolicy(QSizePolicy::Fixed, QSizePolicy::Fixed);
    }

    topLayout->addWidget(m_showHistoryBtn);  // 历史按钮放在左边
    topLayout->addStretch();                 // 中间留空
    topLayout->addWidget(m_showCommandsBtn); // 命令按钮放在右边，和历史面板一样的方式

    // 设置布局的最大宽度，确保不会超出父控件
    topLayout->setSizeConstraint(QLayout::SetDefaultConstraint);

    // 添加到布局 - 将快捷命令栏提到最上方，给日志框更多空间
    mainAreaLayout->addLayout(topLayout);           // 按钮区域在最上方
    mainAreaLayout->addWidget(m_quickCommandFrame); // 快捷命令栏在按钮区域下方
    mainAreaLayout->addWidget(m_logFrame, 1);       // 日志框获得剩余空间
    mainAreaLayout->addWidget(m_commandInputFrame);
}

void MainWindow::createQuickCommandsArea()
{
    m_quickCommandFrame = new QFrame;
    m_quickCommandFrame->setObjectName("quickCommandFrame");
    m_quickCommandFrame->setFixedHeight(95); // 增加高度以适应更大的按钮

    QVBoxLayout *mainLayout = new QVBoxLayout(m_quickCommandFrame); // 改回垂直布局
    mainLayout->setContentsMargins(15, 10, 15, 10);                 // 增加边距
    mainLayout->setSpacing(10);                                     // 增加间距

    // 第一行：连接控制和基础功能按钮
    QHBoxLayout *firstRowLayout = new QHBoxLayout;
    firstRowLayout->setSpacing(12);

    // 连接控制按钮
    m_quickConnectBtn = new QPushButton("连接");
    m_quickConnectBtn->setObjectName("quickActionBtn");
    m_quickConnectBtn->setFixedSize(70, 35);
    m_quickConnectBtn->setToolTip("快速打开连接对话框");

    m_quickDisconnectBtn = new QPushButton("断开");
    m_quickDisconnectBtn->setObjectName("quickActionBtn");
    m_quickDisconnectBtn->setFixedSize(70, 35);
    m_quickDisconnectBtn->setToolTip("快速断开当前连接");
    m_quickDisconnectBtn->setEnabled(false);

    m_quickClearBtn = new QPushButton("清空");
    m_quickClearBtn->setObjectName("quickActionBtn");
    m_quickClearBtn->setFixedSize(70, 35);
    m_quickClearBtn->setToolTip("清空终端显示内容");

    m_quickPauseBtn = new QPushButton("暂停");
    m_quickPauseBtn->setObjectName("quickActionBtn");
    m_quickPauseBtn->setFixedSize(70, 35);
    m_quickPauseBtn->setToolTip("暂停/恢复数据接收显示");
    m_quickPauseBtn->setCheckable(true);

    // 快捷命令按钮
    m_cmdBtn1 = new QPushButton("保存日志");
    m_cmdBtn1->setObjectName("quickCommandBtn");
    m_cmdBtn1->setFixedSize(85, 35);
    m_cmdBtn1->setToolTip("点击保存当前终端内容到文件");

    m_timestampBtn = new QPushButton("时间戳");
    m_timestampBtn->setObjectName("timestampBtn");
    m_timestampBtn->setFixedSize(90, 35); // 增加宽度以显示完整文字
    m_timestampBtn->setCheckable(true);
    m_timestampBtn->setToolTip("点击启用/禁用时间戳显示");

    firstRowLayout->addWidget(m_quickConnectBtn);
    firstRowLayout->addWidget(m_quickDisconnectBtn);
    firstRowLayout->addWidget(m_quickClearBtn);
    firstRowLayout->addWidget(m_quickPauseBtn);
    firstRowLayout->addWidget(m_cmdBtn1);
    firstRowLayout->addWidget(m_timestampBtn);
    firstRowLayout->addStretch(); // 添加弹性空间

    // 第二行：快捷命令和功能按钮
    QHBoxLayout *secondRowLayout = new QHBoxLayout;
    secondRowLayout->setSpacing(12);

    m_cmdBtn2 = new QPushButton("路径");
    m_cmdBtn2->setObjectName("quickCommandBtn");
    m_cmdBtn2->setFixedSize(70, 35);
    m_cmdBtn2->setEnabled(false);

    m_cmdBtn3 = new QPushButton("用户");
    m_cmdBtn3->setObjectName("quickCommandBtn");
    m_cmdBtn3->setFixedSize(70, 35);
    m_cmdBtn3->setEnabled(false);

    // 新增功能按钮
    m_findBtn = new QPushButton("查找");
    m_findBtn->setObjectName("quickCommandBtn");
    m_findBtn->setFixedSize(70, 35);
    m_findBtn->setToolTip("在日志中查找内容");

    m_highlightBtn = new QPushButton("高亮");
    m_highlightBtn->setObjectName("quickCommandBtn");
    m_highlightBtn->setFixedSize(70, 35);
    m_highlightBtn->setCheckable(true);
    m_highlightBtn->setToolTip("高亮显示指定内容");

    m_remoteBtn = new QPushButton("远程");
    m_remoteBtn->setObjectName("quickCommandBtn");
    m_remoteBtn->setFixedSize(70, 35);
    m_remoteBtn->setToolTip("远程连接到其他RF调试工具");

    // 快捷命令输入框
    m_quickCmdInput = new QLineEdit;
    m_quickCmdInput->setPlaceholderText("快捷命令框");
    m_quickCmdInput->setObjectName("quickCommandInput");
    m_quickCmdInput->setFixedSize(180, 35);

    secondRowLayout->addWidget(m_cmdBtn2);
    secondRowLayout->addWidget(m_cmdBtn3);
    secondRowLayout->addWidget(m_findBtn);
    secondRowLayout->addWidget(m_highlightBtn);
    secondRowLayout->addWidget(m_remoteBtn);
    secondRowLayout->addStretch(); // 添加弹性空间
    secondRowLayout->addWidget(m_quickCmdInput);

    // 将两行添加到主布局
    mainLayout->addLayout(firstRowLayout);
    mainLayout->addLayout(secondRowLayout);

    // 设置清晰字体的快捷功能按钮样式 - 参考右边按钮的样式
    QString quickActionBtnStyle =
        "QPushButton#quickActionBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: 2px solid #1565c0;"
        "    border-radius: 16px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "    padding: 4px 8px;"
        "    text-align: center;"
        "}"
        "QPushButton#quickActionBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "    border: 2px solid #0d47a1;"
        "    color: white;"
        "}"
        "QPushButton#quickActionBtn:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1565c0, stop:1 #0d47a1);"
        "    border: 2px solid #01579b;"
        "    color: white;"
        "}"
        "QPushButton#quickActionBtn:disabled {"
        "    background: #f5f5f5;"
        "    color: #999999;"
        "    border: 2px solid #dddddd;"
        "}";

    // 应用样式到所有快捷功能按钮
    m_quickConnectBtn->setStyleSheet(quickActionBtnStyle);
    m_quickDisconnectBtn->setStyleSheet(quickActionBtnStyle);
    m_quickClearBtn->setStyleSheet(quickActionBtnStyle);
    m_quickPauseBtn->setStyleSheet(quickActionBtnStyle);

    // 设置清晰字体的快捷命令按钮样式 - 参考右边按钮的样式
    QString quickCommandBtnStyle =
        "QPushButton#quickCommandBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: 2px solid #1565c0;"
        "    border-radius: 16px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "    padding: 4px 8px;"
        "    text-align: center;"
        "}"
        "QPushButton#quickCommandBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "    border: 2px solid #0d47a1;"
        "    color: white;"
        "}"
        "QPushButton#quickCommandBtn:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1565c0, stop:1 #0d47a1);"
        "    border: 2px solid #01579b;"
        "    color: white;"
        "}"
        "QPushButton#quickCommandBtn:disabled {"
        "    background: #f5f5f5;"
        "    color: #999999;"
        "    border: 2px solid #dddddd;"
        "}";

    // 应用样式到所有快捷命令按钮
    m_cmdBtn1->setStyleSheet(quickCommandBtnStyle);
    m_cmdBtn2->setStyleSheet(quickCommandBtnStyle);
    m_cmdBtn3->setStyleSheet(quickCommandBtnStyle);
    m_findBtn->setStyleSheet(quickCommandBtnStyle);
    m_highlightBtn->setStyleSheet(quickCommandBtnStyle);
    m_remoteBtn->setStyleSheet(quickCommandBtnStyle);

    // 设置清晰字体的时间戳按钮样式 - 参考右边按钮的样式
    QString timestampBtnStyle =
        "QPushButton#timestampBtn {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "    border: 2px solid #1565c0;"
        "    border-radius: 16px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "    padding: 4px 8px;"
        "    text-align: center;"
        "}"
        "QPushButton#timestampBtn:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1976d2, stop:1 #1565c0);"
        "    border: 2px solid #0d47a1;"
        "    color: white;"
        "}"
        "QPushButton#timestampBtn:checked {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #4caf50, stop:1 #388e3c);"
        "    border: 2px solid #2e7d32;"
        "    color: white;"
        "    font-weight: bold;"
        "}"
        "QPushButton#timestampBtn:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #1565c0, stop:1 #0d47a1);"
        "    border: 2px solid #01579b;"
        "    color: white;"
        "}";

    // 应用样式到时间戳按钮
    m_timestampBtn->setStyleSheet(timestampBtnStyle);

    // 设置清晰字体的快捷命令输入框样式
    QString quickCmdInputStyle =
        "QLineEdit#quickCommandInput {"
        "    background: #ffffff;"
        "    border: 2px solid #dee2e6;"
        "    border-radius: 8px;"
        "    padding: 6px 12px;"
        "    font-size: 12px;"
        "    color: #333333;"
        "    font-weight: normal;"
        "    font-family: 'Microsoft YaHei', 'SimHei', sans-serif;"
        "    selection-background-color: #2196f3;"
        "    selection-color: #ffffff;"
        "}"
        "QLineEdit#quickCommandInput:focus {"
        "    border: 2px solid #2196f3;"
        "    background: #ffffff;"
        "    outline: none;"
        "}"
        "QLineEdit#quickCommandInput::placeholder {"
        "    color: #999999;"
        "    font-weight: normal;"
        "}";

    // 应用样式到快捷命令输入框
    m_quickCmdInput->setStyleSheet(quickCmdInputStyle);
}

void MainWindow::createLogDisplayArea()
{
    qDebug() << "Creating log display area...";

    m_logFrame = new QFrame;
    m_logFrame->setObjectName("logFrame");

    QVBoxLayout *logLayout = new QVBoxLayout(m_logFrame);
    logLayout->setContentsMargins(15, 10, 15, 15); // 减少边距，给日志区域更多空间

    // 创建专业终端
    qDebug() << "Creating ProfessionalTerminal...";
    m_professionalTerminal = new ProfessionalTerminal;
    m_professionalTerminal->setObjectName("professionalTerminal");

    // 保持旧终端的兼容性（暂时）
    m_terminal = new InteractiveTerminal;
    m_terminal->setObjectName("logDisplay");
    m_terminal->setVisible(false); // 隐藏旧终端

    logLayout->addWidget(m_professionalTerminal);
    logLayout->addWidget(m_terminal);

    qDebug() << "Log display area created successfully, using ProfessionalTerminal";
}

void MainWindow::createCommandInputArea()
{
    m_commandInputFrame = new QFrame;
    m_commandInputFrame->setObjectName("commandInputFrame");
    m_commandInputFrame->setFixedHeight(50); // 减少高度，给日志区域更多空间

    QHBoxLayout *inputLayout = new QHBoxLayout(m_commandInputFrame);
    inputLayout->setContentsMargins(15, 8, 15, 8); // 减少边距
    inputLayout->setSpacing(12);                   // 减少间距

    // 状态标签
    m_statusLabel = new QLabel("状态栏");
    m_statusLabel->setObjectName("statusLabel");

    // 命令输入框
    m_commandInput = new QLineEdit;
    m_commandInput->setPlaceholderText("用户命令发送框");
    m_commandInput->setObjectName("commandInput");
    m_commandInput->setFixedHeight(30); // 减少高度

    // 发送按钮
    m_sendBtn = new QPushButton("发送");
    m_sendBtn->setObjectName("sendButton");
    m_sendBtn->setFixedSize(55, 30); // 减少尺寸

    // 连接状态标签
    m_connectionTypeLabel = new QLabel("未连接");
    m_connectionTypeLabel->setObjectName("connectionTypeLabel");
    m_connectionTypeLabel->setFixedHeight(30); // 减少高度

    // 连接按钮
    m_connectionBtn = new QPushButton("连接设备");
    m_connectionBtn->setObjectName("connectionButton");
    m_connectionBtn->setFixedSize(90, 30); // 减少尺寸

    inputLayout->addWidget(m_statusLabel);
    inputLayout->addWidget(m_commandInput);
    inputLayout->addWidget(m_sendBtn);
    inputLayout->addWidget(m_connectionTypeLabel);
    inputLayout->addWidget(m_connectionBtn);
}

void MainWindow::createCommandsPanel()
{
    m_commandsFrame = new QFrame;
    m_commandsFrame->setObjectName("commandsFrame");
    m_commandsFrame->setFixedWidth(160); // 稍微增加宽度

    // 设置命令面板整体样式
    m_commandsFrame->setStyleSheet(
        "#commandsFrame {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #ffffff, stop:1 #f8f9fa);"
        "    border: 1px solid #e9ecef;"
        "    border-radius: 8px;"
        "    margin: 2px;"
        "}");

    QVBoxLayout *commandsLayout = new QVBoxLayout(m_commandsFrame);
    commandsLayout->setContentsMargins(12, 12, 12, 12);
    commandsLayout->setSpacing(8);

    // 标题和控制按钮
    QHBoxLayout *titleLayout = new QHBoxLayout;
    titleLayout->setContentsMargins(0, 0, 0, 0);

    m_commandsTitle = new QLabel("⚡ 常用命令");
    m_commandsTitle->setObjectName("commandsTitle");
    m_commandsTitle->setAlignment(Qt::AlignLeft | Qt::AlignVCenter);
    m_commandsTitle->setStyleSheet(
        "#commandsTitle {"
        "    font-size: 14px;"
        "    font-weight: bold;"
        "    color: #495057;"
        "    padding: 4px 0px;"
        "}");

    m_toggleCommandsBtn = new QPushButton("✕");
    m_toggleCommandsBtn->setObjectName("toggleButton");
    m_toggleCommandsBtn->setFixedSize(24, 24);
    m_toggleCommandsBtn->setToolTip("隐藏常用命令面板");

    // 设置隐藏按钮样式 - 与历史面板保持一致
    m_toggleCommandsBtn->setStyleSheet(
        "QPushButton {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #ffffff, stop:1 #f8f9fa);"
        "    border: 2px solid #dee2e6;"
        "    border-radius: 12px;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    color: #6c757d;"
        "}"
        "QPushButton:hover {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #007bff, stop:1 #0056b3);"
        "    border: 2px solid #007bff;"
        "    color: white;"
        "}"
        "QPushButton:pressed {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #0056b3, stop:1 #004085);"
        "    border: 2px solid #004085;"
        "}");

    titleLayout->addWidget(m_commandsTitle);
    titleLayout->addStretch(); // 添加弹性空间
    titleLayout->addWidget(m_toggleCommandsBtn);

    // 命令列表
    m_commandsList = new CommandListWidget;
    m_commandsList->setObjectName("commandsList");

    // 设置命令列表的最小高度，确保有足够空间显示
    m_commandsList->setMinimumHeight(200);

    // 添加命令按钮
    m_addCommandBtn = new QPushButton("+ 添加命令");
    m_addCommandBtn->setObjectName("addCommandBtn");

    commandsLayout->addLayout(titleLayout);
    commandsLayout->addWidget(m_commandsList);
    commandsLayout->addWidget(m_addCommandBtn);
}

void MainWindow::createBottomStatusArea()
{
    // 状态栏在setupStatusBar中创建
}

void MainWindow::setupStatusBar()
{
    m_statusBar = statusBar();

    // 连接状态信息
    m_statusInfo = new QLabel("就绪");
    m_statusInfo->setObjectName("statusInfo");
    m_statusInfo->setMinimumWidth(100);

    // 设备信息标签 - 显示COM口、IP地址、ADB设备等
    m_connectionDetailsLabel = new QLabel("未连接");
    m_connectionDetailsLabel->setObjectName("deviceInfo");
    m_connectionDetailsLabel->setMinimumWidth(150);
    m_connectionDetailsLabel->setStyleSheet(
        "#deviceInfo {"
        "    color: #2c5aa0;"
        "    font-size: 12px;"
        "    font-weight: bold;"
        "    padding: 2px 8px;"
        "    border-left: 1px solid #e0e0e0;"
        "    margin-left: 10px;"
        "}");

    // 配置信息标签 - 显示波特率、编码格式等
    m_connectionTimeLabel = new QLabel("");
    m_connectionTimeLabel->setObjectName("configInfo");
    m_connectionTimeLabel->setMinimumWidth(180);
    m_connectionTimeLabel->setStyleSheet(
        "#configInfo {"
        "    color: #666666;"
        "    font-size: 11px;"
        "    padding: 2px 8px;"
        "    border-left: 1px solid #e0e0e0;"
        "    margin-left: 5px;"
        "}");

    // 版本信息
    m_versionLabel = new QLabel("RF调试工具 v2.0 - Qt版本 | 作者: flex | 联系: <EMAIL>");
    m_versionLabel->setObjectName("versionLabel");
    m_versionLabel->setToolTip("RF调试工具 Qt版本\n版本: 2.0\n作者: flex\n联系方式: <EMAIL>");

    // 添加到状态栏
    m_statusBar->addWidget(m_statusInfo);
    m_statusBar->addWidget(m_connectionDetailsLabel);
    m_statusBar->addWidget(m_connectionTimeLabel);
    m_statusBar->addPermanentWidget(m_versionLabel);

    // 设置状态栏样式
    m_statusBar->setStyleSheet(
        "QStatusBar {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #f8f9fa, stop:1 #e9ecef);"
        "    border-top: 1px solid #dee2e6;"
        "    font-size: 11px;"
        "}"
        "QStatusBar::item {"
        "    border: none;"
        "}");
}

void MainWindow::setupConnections()
{
    qDebug() << "Setting up connections - starting...";

    // 菜单按钮连接
    qDebug() << "Connecting menu buttons...";
    connect(m_configBtn, &QPushButton::clicked, this, &MainWindow::showConfigMenu);
    connect(m_importExportBtn, &QPushButton::clicked, this, &MainWindow::showImportExportMenu);
    connect(m_helpBtn, &QPushButton::clicked, this, &MainWindow::showHelp);

    // 快捷命令按钮连接
    qDebug() << "Connecting quick command buttons...";
    connect(m_cmdBtn1, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn2, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_cmdBtn3, &QPushButton::clicked, this, &MainWindow::executeQuickCommand);
    connect(m_timestampBtn, &QPushButton::clicked, this, &MainWindow::toggleTimestamp);

    // 新增快捷功能按钮连接
    qDebug() << "Connecting quick action buttons...";
    connect(m_quickConnectBtn, &QPushButton::clicked, this, &MainWindow::quickConnect);
    connect(m_quickDisconnectBtn, &QPushButton::clicked, this, &MainWindow::quickDisconnect);
    connect(m_quickClearBtn, &QPushButton::clicked, this, &MainWindow::quickClear);
    connect(m_quickPauseBtn, &QPushButton::clicked, this, &MainWindow::quickPause);

    // 新增功能按钮连接
    connect(m_findBtn, &QPushButton::clicked, this, &MainWindow::showFindDialog);
    connect(m_highlightBtn, &QPushButton::clicked, this, &MainWindow::toggleHighlight);
    connect(m_remoteBtn, &QPushButton::clicked, this, &MainWindow::showRemoteDialog);

    // 命令输入连接
    qDebug() << "Connecting command input...";
    connect(m_commandInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_quickCmdInput, &QLineEdit::returnPressed, this, &MainWindow::sendCommand);
    connect(m_sendBtn, &QPushButton::clicked, this, &MainWindow::sendCommand);

    // 连接按钮
    qDebug() << "Connecting connection button...";
    connect(m_connectionBtn, &QPushButton::clicked, this, &MainWindow::onConnectionButtonClicked);

    // 历史连接面板
    qDebug() << "Connecting history panel...";
    connect(m_hideHistoryBtn, &QPushButton::clicked, this, &MainWindow::toggleHistoryPanel);
    connect(m_showHistoryBtn, &QPushButton::clicked, this, &MainWindow::toggleHistoryPanel);
    connect(m_historyList, &QListWidget::itemClicked, this, &MainWindow::onHistoryItemClicked);
    connect(m_historyList, &QListWidget::itemDoubleClicked, this, &MainWindow::onHistoryItemDoubleClicked);
    connect(m_historyList, &QListWidget::customContextMenuRequested, this, &MainWindow::onHistoryContextMenu);

    // 命令面板
    qDebug() << "Connecting command panel...";
    connect(m_toggleCommandsBtn, &QPushButton::clicked, this, &MainWindow::toggleCommandsPanel);
    connect(m_addCommandBtn, &QPushButton::clicked, this, &MainWindow::addNewCommand);
    connect(m_showCommandsBtn, &QPushButton::clicked, this, &MainWindow::toggleCommandsPanel);

    // 连接管理器信号
    qDebug() << "Connecting connection manager signals...";
    if (m_connectionManager)
    {
        connect(m_connectionManager, &ConnectionManager::statusChanged,
                this, &MainWindow::onConnectionStatusChanged);
        connect(m_connectionManager, &ConnectionManager::dataReceived,
                this, &MainWindow::onDataReceived);
        connect(m_connectionManager, &ConnectionManager::rawDataReceived,
                this, &MainWindow::onRawDataReceived);
        connect(m_connectionManager, &ConnectionManager::errorOccurred,
                this, &MainWindow::onErrorOccurred);
        connect(m_connectionManager, &ConnectionManager::connectionEstablished,
                this, &MainWindow::onConnectionEstablished);
        connect(m_connectionManager, &ConnectionManager::connectionLost,
                this, &MainWindow::onConnectionLost);
    }

    // 配置管理器信号
    qDebug() << "Connecting config manager signals...";
    if (m_configManager)
    {
        connect(m_configManager, &ConfigManager::configChanged,
                this, &MainWindow::onConfigChanged);
    }

    // 命令列表信号
    qDebug() << "Connecting command list signals...";
    if (m_commandsList)
    {
        connect(m_commandsList, &CommandListWidget::executeCommand,
                this, &MainWindow::executeCommonCommand);
        connect(m_commandsList, &CommandListWidget::commandEdited,
                this, &MainWindow::onCommandEdited);
        connect(m_commandsList, &CommandListWidget::commandDeleted,
                this, &MainWindow::onCommandDeleted);
        connect(m_commandsList, &CommandListWidget::commandAdded,
                this, &MainWindow::onCommandAdded);
    }

    // 专业终端信号
    qDebug() << "Connecting professional terminal signals...";
    if (m_professionalTerminal)
    {
        qDebug() << "ProfessionalTerminal exists, connecting signals...";

        // 连接数据发送信号
        connect(m_professionalTerminal, &ProfessionalTerminal::dataToSend,
                this, [this](const QByteArray &data)
                {
                    qDebug() << "MainWindow - 专业终端数据发送:" << data.left(50);

                    // 记录发送的数据到日志（原始数据，不添加格式化）
                    if (m_logManager)
                    {
                        m_logManager->writeRawData(QString::fromUtf8(data));
                    }

                    if (m_connectionManager && m_isConnected) {
                        // 直接发送原始数据到串口
                        bool success = m_connectionManager->sendData(data);
                        qDebug() << "MainWindow - 数据发送结果:" << success;
                    } else {
                        qDebug() << "MainWindow - 设备未连接，无法发送数据";
                        m_professionalTerminal->appendMessage("警告: 设备未连接\n");
                    } });

        // 连接时间戳状态变化信号
        connect(m_professionalTerminal, &ProfessionalTerminal::timestampStateChanged,
                this, &MainWindow::onTerminalTimestampStateChanged);

        // 连接字体大小变化信号
        connect(m_professionalTerminal, &ProfessionalTerminal::fontSizeChanged,
                this, [this](int size)
                {
                    qDebug() << "日志字体大小变化:" << size;
                    if (m_configManager) {
                        m_configManager->setValue("log/font_size", size);
                    } });

        qDebug() << "ProfessionalTerminal signals connected successfully";
    }

    // 保持旧终端的兼容性
    if (m_terminal)
    {
        connect(m_terminal, &InteractiveTerminal::commandEntered,
                this, [this](const QString & /*command*/)
                {
                    // 旧终端的处理逻辑（备用）
                });
    }

    qDebug() << "All connections setup completed";
}

// 连接相关槽函数
void MainWindow::showConnectionDialog()
{
    if (!m_connectionDialog)
    {
        m_connectionDialog = new ConnectionDialog(this);
        connect(m_connectionDialog, &ConnectionDialog::connectionRequested,
                this, &MainWindow::connectDevice);
        connect(m_connectionDialog, &ConnectionDialog::testConnectionRequested,
                m_connectionManager, &ConnectionManager::testConnection);
    }

    m_connectionDialog->show();
    m_connectionDialog->raise();
    m_connectionDialog->activateWindow();
}

void MainWindow::connectDevice()
{
    if (m_connectionDialog)
    {
        QVariantMap params = m_connectionDialog->getConnectionParams();
        if (m_connectionManager)
        {
            // 保存连接参数，用于连接成功后的处理
            m_connectionParams = params;
            m_connectionType = params.value("type", "未知").toString();

            // 显示连接中状态
            m_terminal->appendMessage("🔄 正在连接设备...", LogManager::Info);

            // 异步启动连接，结果通过信号处理
            m_connectionManager->connectToDevice(params);

            // 暂时关闭对话框，连接结果通过信号处理
            m_connectionDialog->accept();
        }
    }
}

void MainWindow::disconnectDevice()
{
    qDebug() << "MainWindow::disconnectDevice() - 开始断开连接";

    if (m_connectionManager && m_isConnected)
    {
        qDebug() << "MainWindow::disconnectDevice() - 调用ConnectionManager断开连接";

        // 先更新UI状态，避免用户重复点击
        m_connectionBtn->setEnabled(false);
        m_connectionBtn->setText("断开中...");

        // 异步断开连接
        QTimer::singleShot(0, [this]()
                           { m_connectionManager->disconnectFromDevice(); });

        // 立即更新本地状态
        m_isConnected = false;
        m_connectionType = "未连接";
        m_connectionParams.clear();

        if (m_terminal)
        {
            m_terminal->appendMessage("🔌 正在断开连接...", LogManager::Info);
            m_terminal->setConnected(false); // 禁用交互式模式
        }

        // 停止连接时间更新定时器
        if (m_connectionTimeUpdateTimer)
        {
            m_connectionTimeUpdateTimer->stop();
            qDebug() << "MainWindow::disconnectDevice() - 连接时间更新定时器已停止";
        }

        // 优化：减少延迟，快速响应
        QTimer::singleShot(200, [this]()
                           {
            updateConnectionStatus();
            updateCommandButtons();
            // 清除历史连接高亮状态
            updateHistoryConnectionStatus();
            m_connectionBtn->setEnabled(true); });
    }

    qDebug() << "MainWindow::disconnectDevice() - 完成";
}

void MainWindow::onConnectionButtonClicked()
{
    qDebug() << "MainWindow::onConnectionButtonClicked() - 按钮点击，当前连接状态:" << m_isConnected;

    if (m_isConnected)
    {
        qDebug() << "MainWindow::onConnectionButtonClicked() - 断开连接";
        disconnectDevice();
    }
    else
    {
        qDebug() << "MainWindow::onConnectionButtonClicked() - 显示连接对话框";
        showConnectionDialog();
    }
}

void MainWindow::onConnectionStatusChanged(const QString &status, const QString &details)
{
    qDebug() << "MainWindow::onConnectionStatusChanged() - 开始处理状态变化:" << status;

    qDebug() << "MainWindow::onConnectionStatusChanged() - 设置状态标签";
    m_statusLabel->setText(status);

    if (!details.isEmpty())
    {
        qDebug() << "MainWindow::onConnectionStatusChanged() - 添加终端消息:" << details;
        m_terminal->appendMessage(details, LogManager::System);
    }

    qDebug() << "MainWindow::onConnectionStatusChanged() - 更新连接状态";
    // 更新连接状态
    if (status.contains("已连接"))
    {
        m_isConnected = true;
    }
    else if (status.contains("断开") || status.contains("失败"))
    {
        m_isConnected = false;
    }

    qDebug() << "MainWindow::onConnectionStatusChanged() - 调用updateConnectionStatus";
    updateConnectionStatus();
    qDebug() << "MainWindow::onConnectionStatusChanged() - 调用updateCommandButtons";
    updateCommandButtons();
    qDebug() << "MainWindow::onConnectionStatusChanged() - 完成";
}

void MainWindow::onDataReceived(const QString & /*data*/)
{
    // 完全禁用字符串数据处理，只使用原始数据处理
    qDebug() << "MainWindow::onDataReceived() - 字符串数据处理已禁用，使用原始数据处理";
    return;
}

void MainWindow::onRawDataReceived(const QByteArray &data)
{
    try
    {
        qDebug() << "MainWindow::onRawDataReceived() - 收到原始数据:" << data.left(50).toHex();
        qDebug() << "MainWindow::onRawDataReceived() - 数据长度:" << data.length();

        // 智能编码检测和转换
        QString dataStr;

        // 检查数据是否主要是可打印ASCII字符
        int printableCount = 0;
        int totalCount = data.size();

        for (int i = 0; i < data.size(); ++i)
        {
            unsigned char byte = static_cast<unsigned char>(data[i]);
            // 可打印ASCII字符范围：32-126，加上常见控制字符：7(bel), 8(bs), 9(tab), 10(lf), 13(cr), 27(esc)
            // 同时包含扩展ASCII字符范围：128-255（用于支持更多字符集）
            if ((byte >= 32 && byte <= 126) || byte == 7 || byte == 8 || byte == 9 || byte == 10 || byte == 13 || byte == 27 || byte >= 128)
            {
                printableCount++;
            }
        }

        // 对于串口通信，优先按文本数据处理，除非明确是二进制数据
        // 只有当数据中包含大量null字节或其他明显的二进制标识时才按二进制处理
        int nullCount = 0;
        int controlCount = 0;
        for (int i = 0; i < data.size(); ++i)
        {
            unsigned char byte = static_cast<unsigned char>(data[i]);
            if (byte == 0)
                nullCount++;
            if (byte < 32 && byte != 7 && byte != 8 && byte != 9 && byte != 10 && byte != 13 && byte != 27)
                controlCount++;
        }

        // 如果包含大量null字节或控制字符，才认为是二进制数据
        bool isBinaryData = (totalCount > 0) && ((nullCount > totalCount * 0.2) || (controlCount > totalCount * 0.3));
        bool isTextData = !isBinaryData;

        qDebug() << "MainWindow::onRawDataReceived() - 数据分析: 总字节数=" << totalCount
                 << "可打印字节数=" << printableCount << "null字节数=" << nullCount
                 << "控制字符数=" << controlCount << "文本数据=" << isTextData;

        if (isTextData)
        {
            // 对于文本数据，优先使用Latin-1编码（适合串口通信）
            dataStr = QString::fromLatin1(data);
            qDebug() << "MainWindow::onRawDataReceived() - 使用Latin-1编码处理文本数据";
        }
        else
        {
            // 对于二进制数据，使用Latin-1保持字节完整性，并显示为十六进制
            dataStr = QString::fromLatin1(data);

            // 为二进制数据添加十六进制显示
            QString hexStr = data.toHex(' ').toUpper();
            dataStr = QString("[HEX: %1] %2").arg(hexStr, dataStr);
            qDebug() << "MainWindow::onRawDataReceived() - 检测到二进制数据，使用十六进制显示";
        }

        qDebug() << "MainWindow::onRawDataReceived() - 转换后数据:" << dataStr.left(50);

        // 记录到日志管理器
        if (m_logManager)
        {
            m_logManager->writeRawData(dataStr);
        }

        // 发送到终端显示
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendData(dataStr);
        }
        else if (m_terminal)
        {
            m_terminal->appendSystemOutput(dataStr);
        }

        // 广播数据到远程客户端
        if (!m_remoteClients.isEmpty())
        {
            broadcastToRemoteClients(dataStr);
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::onRawDataReceived() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::onRawDataReceived() - 未知异常";
    }
}

void MainWindow::onTerminalTimestampStateChanged(bool enabled)
{
    qDebug() << "MainWindow::onTerminalTimestampStateChanged() - 终端时间戳状态变化:" << enabled;

    // 更新内部状态
    m_timestampEnabled = enabled;

    // 更新按钮状态和文本
    if (m_timestampBtn)
    {
        m_timestampBtn->setChecked(enabled);
        if (enabled)
        {
            m_timestampBtn->setText("🕐 时间戳✓");
            m_timestampBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
        }
        else
        {
            m_timestampBtn->setText("🕐 时间戳");
            m_timestampBtn->setStyleSheet("");
        }
    }

    // 保存到配置
    if (m_configManager)
    {
        m_configManager->setValue("timestamp_enabled", enabled);
    }

    qDebug() << "MainWindow::onTerminalTimestampStateChanged() - 按钮状态已同步";
}

void MainWindow::onErrorOccurred(const QString &error)
{
    if (m_terminal)
    {
        m_terminal->appendError(error);
    }

    m_statusLabel->setText("错误: " + error);
}

void MainWindow::onConnectionEstablished()
{
    qDebug() << "MainWindow::onConnectionEstablished() - 开始处理连接建立";

    m_isConnected = true;
    m_connectionStartTime = QDateTime::currentDateTime(); // 记录连接开始时间

    // 如果是ADB连接，获取设备序列号并更新显示
    if (m_connectionType.toLower() == "adb" && m_connectionManager)
    {
        // 获取ADB设备信息
        QString deviceId = m_connectionParams.value("device", "").toString();
        if (!deviceId.isEmpty())
        {
            // 更新连接类型显示为包含设备序列号的格式
            m_connectionType = QString("ADB (%1)").arg(deviceId);
            qDebug() << "MainWindow::onConnectionEstablished() - ADB设备序列号:" << deviceId;
        }
        else
        {
            // 如果没有指定设备ID，尝试获取当前连接的设备
            QProcess adbCheck;
            adbCheck.start("adb", QStringList() << "devices");
            if (adbCheck.waitForFinished(2000))
            {
                QString output = adbCheck.readAllStandardOutput();
                QStringList lines = output.split('\n', QString::SkipEmptyParts);

                for (const QString &line : lines)
                {
                    if (line.contains('\t') && !line.startsWith("List of devices"))
                    {
                        QStringList parts = line.split('\t');
                        if (parts.size() >= 2 && parts[1].trimmed() == "device")
                        {
                            QString serialNumber = parts[0].trimmed();
                            m_connectionType = QString("ADB (%1)").arg(serialNumber);
                            qDebug() << "MainWindow::onConnectionEstablished() - 检测到ADB设备:" << serialNumber;
                            break;
                        }
                    }
                }
            }
        }
    }

    updateConnectionStatus();
    updateCommandButtons();

    // 启动连接时间更新定时器
    if (m_connectionTimeUpdateTimer)
    {
        m_connectionTimeUpdateTimer->start();
        qDebug() << "MainWindow::onConnectionEstablished() - 连接时间更新定时器已启动";
    }

    // 使用专业终端
    if (m_professionalTerminal)
    {
        m_professionalTerminal->setConnected(true);
        m_professionalTerminal->appendMessage("✅ 设备连接成功 - 可以开始输入命令\n");
    }

    // 保持旧终端兼容性
    if (m_terminal)
    {
        m_terminal->appendMessage("✅ 设备连接成功", LogManager::Info);
        m_terminal->setConnected(true);
    }

    // 优化：减少延迟，使用异步处理
    QTimer::singleShot(100, this, [this]()
                       {
                           try {
                               qDebug() << "MainWindow::onConnectionEstablished() - 异步保存历史记录";
                               saveCurrentConnectionToHistory();
                               // 查找并高亮当前连接项
                               findAndHighlightCurrentConnection();
                               qDebug() << "MainWindow::onConnectionEstablished() - 历史记录保存完成";
                           } catch (...) {
                               qDebug() << "MainWindow::onConnectionEstablished() - 保存历史记录时发生异常";
                           } });

    // 保持旧的连接历史兼容性
    if (m_connectionDialog && !m_connectionParams.isEmpty())
    {
        try
        {
            m_connectionDialog->addToHistory(m_connectionParams);
        }
        catch (...)
        {
            qDebug() << "MainWindow::onConnectionEstablished() - 添加到连接对话框历史时发生异常";
        }
    }

    qDebug() << "MainWindow::onConnectionEstablished() - 处理完成";
}

void MainWindow::onConnectionLost()
{
    m_isConnected = false;
    m_connectionType = "未连接";
    m_connectionParams.clear();
    updateConnectionStatus();
    updateCommandButtons();

    // 使用专业终端
    if (m_professionalTerminal)
    {
        m_professionalTerminal->setConnected(false);
        m_professionalTerminal->appendMessage("🔌 设备连接已断开\n");
    }

    // 保持旧终端兼容性
    if (m_terminal)
    {
        m_terminal->appendMessage("🔌 设备连接已断开", LogManager::Warning);
        m_terminal->setConnected(false);
    }
}

// 命令相关槽函数
void MainWindow::sendCommand()
{
    QString command;

    // 确定命令来源
    QObject *sender = this->sender();
    if (sender == m_commandInput)
    {
        command = m_commandInput->text().trimmed();
        m_commandInput->clear();
    }
    else if (sender == m_quickCmdInput)
    {
        command = m_quickCmdInput->text().trimmed();
        m_quickCmdInput->clear();
    }
    else if (sender == m_sendBtn)
    {
        command = m_commandInput->text().trimmed();
        m_commandInput->clear();
    }

    if (command.isEmpty())
    {
        return;
    }

    if (!m_isConnected || !m_connectionManager)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("警告: 设备未连接\n");
        }
        else if (m_terminal)
        {
            m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
        }
        return;
    }

    // 记录发送的命令到日志（原始数据，不添加格式化）
    if (m_logManager)
    {
        m_logManager->writeRawData(command + "\n");
    }

    // 发送命令
    bool success = m_connectionManager->sendCommand(command);
    if (success)
    {
        if (m_terminal)
        {
            m_terminal->appendUserInput(command);
        }
        if (m_commandHistory)
        {
            m_commandHistory->addCommand(command);
        }
    }
    else
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("命令发送失败\n");
        }
        else if (m_terminal)
        {
            m_terminal->appendError("命令发送失败");
        }
    }
}

void MainWindow::executeQuickCommand()
{
    try
    {
        QPushButton *button = qobject_cast<QPushButton *>(sender());
        if (!button)
        {
            return;
        }

        // 第一个按钮是日志保存功能
        if (button == m_cmdBtn1)
        {
            // 执行日志保存功能
            saveCurrentTerminalContent();
            return; // 直接返回，不执行后续的命令发送逻辑
        }

        QString command;
        if (button == m_cmdBtn2 && m_configManager)
        {
            command = m_configManager->getValue("quick_command_2", "pwd").toString();
        }
        else if (button == m_cmdBtn3 && m_configManager)
        {
            command = m_configManager->getValue("quick_command_3", "whoami").toString();
        }

        if (!command.isEmpty())
        {
            if (m_isConnected && m_connectionManager)
            {
                // 记录快速命令到日志（原始数据，不添加格式化）
                if (m_logManager)
                {
                    m_logManager->writeRawData(command + "\n");
                }

                bool success = m_connectionManager->sendCommand(command);
                if (success)
                {
                    if (m_terminal)
                    {
                        m_terminal->appendUserInput(command);
                    }
                    if (m_commandHistory)
                    {
                        m_commandHistory->addCommand(command);
                    }
                }
                else
                {
                    if (m_professionalTerminal)
                    {
                        m_professionalTerminal->appendMessage("命令发送失败\n");
                    }
                    else if (m_terminal)
                    {
                        m_terminal->appendError("命令发送失败");
                    }
                }
            }
            else
            {
                if (m_professionalTerminal)
                {
                    m_professionalTerminal->appendMessage("警告: 设备未连接\n");
                }
                else if (m_terminal)
                {
                    m_terminal->appendMessage("警告: 设备未连接", LogManager::Warning);
                }
            }
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::executeQuickCommand() - 异常:" << e.what();
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("执行快捷命令时发生错误\n");
        }
    }
    catch (...)
    {
        qDebug() << "MainWindow::executeQuickCommand() - 未知异常";
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("执行快捷命令时发生未知错误\n");
        }
    }
}

void MainWindow::toggleTimestamp()
{
    qDebug() << "MainWindow::toggleTimestamp() - 切换时间戳状态";

    try
    {
        m_timestampEnabled = !m_timestampEnabled;

        // 更新按钮状态和文本
        if (m_timestampBtn)
        {
            m_timestampBtn->setChecked(m_timestampEnabled);
            if (m_timestampEnabled)
            {
                m_timestampBtn->setText("🕐 时间戳✓");
                m_timestampBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
            }
            else
            {
                m_timestampBtn->setText("🕐 时间戳");
                m_timestampBtn->setStyleSheet("");
            }
        }

        // 通知终端时间戳状态变化
        if (m_professionalTerminal)
        {
            m_professionalTerminal->setTimestampEnabled(m_timestampEnabled);

            // 在终端显示状态变化消息
            QString statusMsg = m_timestampEnabled ? "✅ 时间戳已启用 - 所有消息将显示时间戳\n" : "❌ 时间戳已禁用\n";
            m_professionalTerminal->appendMessage(statusMsg);
        }

        // 保存时间戳设置到配置
        if (m_configManager)
        {
            m_configManager->setValue("timestamp_enabled", m_timestampEnabled);
        }

        qDebug() << "MainWindow::toggleTimestamp() - 时间戳状态:" << (m_timestampEnabled ? "启用" : "禁用");
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::toggleTimestamp() - 异常:" << e.what();
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("切换时间戳状态时发生错误\n");
        }
    }
    catch (...)
    {
        qDebug() << "MainWindow::toggleTimestamp() - 未知异常";
    }
}

void MainWindow::executeCommonCommand(const QString &name, const QString &content)
{
    try
    {
        if (content.isEmpty())
        {
            return;
        }

        // 显示命令执行信息
        QString message = QString("执行常用命令: %1").arg(name);
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(message + "\n");
        }
        else if (m_terminal)
        {
            m_terminal->appendMessage(message, LogManager::Info);
        }

        // 检查是否需要设备连接
        if (!m_isConnected || !m_connectionManager)
        {
            // 显示命令内容，即使未连接也让用户看到
            QString displayMessage = QString("命令内容: %1").arg(content);
            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage(displayMessage + "\n");
                m_professionalTerminal->appendMessage("注意: 设备未连接，命令未发送到设备\n");
            }
            else if (m_terminal)
            {
                m_terminal->appendMessage(displayMessage, LogManager::Info);
                m_terminal->appendMessage("注意: 设备未连接，命令未发送到设备", LogManager::Warning);
            }

            // 仍然添加到历史记录，方便用户查看
            if (m_commandHistory)
            {
                m_commandHistory->addCommand(content);
            }
            return;
        }

        // 记录常用命令到日志（原始数据，不添加格式化）
        if (m_logManager)
        {
            m_logManager->writeRawData(content + "\n");
        }

        // 设备已连接，发送命令
        bool success = m_connectionManager->sendCommand(content);
        if (success)
        {
            if (m_terminal)
            {
                m_terminal->appendUserInput(content);
            }
            if (m_commandHistory)
            {
                m_commandHistory->addCommand(content);
            }
        }
        else
        {
            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage("命令发送失败\n");
            }
            else if (m_terminal)
            {
                m_terminal->appendError("命令发送失败");
            }
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::executeCommonCommand() - 异常:" << e.what();
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("执行常用命令时发生错误\n");
        }
    }
    catch (...)
    {
        qDebug() << "MainWindow::executeCommonCommand() - 未知异常";
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("执行常用命令时发生未知错误\n");
        }
    }
}

void MainWindow::onCommandEdited(const QString &oldName, const QString &newName, const QString &newContent)
{
    // 更新配置中的命令
    if (m_configManager)
    {
        QVariantList commands = m_configManager->getCommands();
        for (int i = 0; i < commands.size(); ++i)
        {
            QVariantMap command = commands[i].toMap();
            if (command["name"].toString() == oldName)
            {
                command["name"] = newName;
                command["content"] = newContent;
                commands[i] = command;
                break;
            }
        }
        m_configManager->setCommands(commands);
    }

    m_terminal->appendMessage(QString("命令已更新: %1 -> %2").arg(oldName, newName), LogManager::Info);
}

void MainWindow::onCommandDeleted(const QString &name)
{
    // 从配置中删除命令
    if (m_configManager)
    {
        QVariantList commands = m_configManager->getCommands();
        for (int i = 0; i < commands.size(); ++i)
        {
            QVariantMap command = commands[i].toMap();
            if (command["name"].toString() == name)
            {
                commands.removeAt(i);
                break;
            }
        }
        m_configManager->setCommands(commands);
    }

    m_terminal->appendMessage(QString("命令已删除: %1").arg(name), LogManager::Info);
}

void MainWindow::onCommandAdded(const QString &name, const QString &content)
{
    qDebug() << "onCommandAdded() called with name:" << name;

    // 添加到配置中
    if (m_configManager)
    {
        qDebug() << "Creating command map...";
        QVariantMap command;
        command["name"] = name;
        command["content"] = content;
        command["enabled"] = true;
        command["created"] = QDateTime::currentDateTime();

        qDebug() << "Calling m_configManager->addCommand()...";
        m_configManager->addCommand(command);
        qDebug() << "m_configManager->addCommand() completed";
    }
    else
    {
        qDebug() << "m_configManager is null";
    }

    qDebug() << "Adding message to terminal...";
    m_terminal->appendMessage(QString("命令已添加: %1").arg(name), LogManager::Info);
    qDebug() << "onCommandAdded() completed";
}

void MainWindow::addNewCommand()
{
    bool ok;
    QString name = QInputDialog::getText(this, "添加命令", "命令名称:", QLineEdit::Normal, "", &ok);
    if (!ok || name.trimmed().isEmpty())
    {
        return;
    }

    QString content = QInputDialog::getMultiLineText(this, "添加命令", "命令内容:", "", &ok);
    if (!ok || content.trimmed().isEmpty())
    {
        return;
    }

    QString description = QInputDialog::getText(this, "添加命令", "命令描述 (可选):", QLineEdit::Normal, "", &ok);
    if (!ok)
    {
        description = "";
    }

    if (m_commandsList)
    {
        m_commandsList->addCommand(name.trimmed(), content.trimmed(), description.trimmed());
    }
}

// 配置相关槽函数
void MainWindow::showConfigMenu()
{
    QMenu menu(this);
    menu.addAction("日志配置", this, &MainWindow::showLogConfig);
    menu.addAction("背景配置", this, &MainWindow::showBackgroundConfig);
    menu.addAction("界面配置", this, &MainWindow::showUIConfig);
    menu.addAction("终端配置", this, &MainWindow::showTerminalConfig);
    menu.addSeparator();
    menu.addAction("重置配置", this, &MainWindow::resetConfig);

    menu.exec(m_configBtn->mapToGlobal(QPoint(0, m_configBtn->height())));
}

void MainWindow::showLogConfig()
{
    if (!m_logConfigDialog)
    {
        m_logConfigDialog = new LogConfigDialog(this);
        connect(m_logConfigDialog, &LogConfigDialog::configChanged,
                this, [this](const QString &key, const QVariant &value)
                {
                    qDebug() << "MainWindow - Log config changed:" << key << value;
                    if (m_configManager) {
                        m_configManager->setValue(key, value);
                        qDebug() << "MainWindow - Config saved to manager";

                        // 立即应用字体配置
                        if (key.startsWith("terminal/font_") || key.startsWith("log/font_")) {
                            // 强制保存配置到文件
                            m_configManager->save();
                            qDebug() << "MainWindow - Font config saved to file";

                            QTimer::singleShot(100, this, [this]() {
                                qDebug() << "MainWindow - Applying font config immediately";
                                applyFontConfig();
                            });
                        }
                    } else {
                        qDebug() << "MainWindow - ERROR: ConfigManager is null!";
                    } });
    }

    // 设置当前配置
    if (m_configManager)
    {
        QVariantMap logConfig;
        // 修复键名不一致问题：使用正确的键名映射
        logConfig["log_font_family"] = m_configManager->getValue("log/font_family", "Consolas");
        logConfig["log_font_size"] = m_configManager->getValue("log/font_size", 10);
        logConfig["log_font_weight"] = m_configManager->getValue("log/font_weight", "normal");
        logConfig["log_buffer_enabled"] = m_configManager->getValue("log/buffer_enabled", true);
        logConfig["log_buffer_size"] = m_configManager->getValue("log/buffer_size", 50000);

        qDebug() << "MainWindow::showLogConfig() - Loading config values:";
        qDebug() << "  log_font_family:" << logConfig["log_font_family"];
        qDebug() << "  log_font_size:" << logConfig["log_font_size"];
        qDebug() << "  log_font_weight:" << logConfig["log_font_weight"];

        m_logConfigDialog->setConfig(logConfig);
    }

    m_logConfigDialog->show();
    m_logConfigDialog->raise();
    m_logConfigDialog->activateWindow();
}

void MainWindow::showBackgroundConfig()
{
    if (!m_backgroundConfigDialog)
    {
        m_backgroundConfigDialog = new BackgroundConfigDialog(this);
        connect(m_backgroundConfigDialog, &BackgroundConfigDialog::configChanged,
                this, [this](const QString &key, const QVariant &value)
                {
                    qDebug() << "MainWindow - 收到背景配置变化:" << key << value;
                    if (m_configManager) {
                        m_configManager->setValue(key, value);
                    } });
    }

    // 传递当前配置到对话框
    if (m_configManager)
    {
        QVariantMap currentConfig;
        currentConfig["type"] = m_configManager->getValue("background_type", "color");
        currentConfig["color"] = m_configManager->getValue("background_color", "#000000");
        currentConfig["color2"] = m_configManager->getValue("background_color2", "#333333");
        currentConfig["image"] = m_configManager->getValue("background_image", "");
        currentConfig["image_mode"] = m_configManager->getValue("background_image_mode", "center");
        currentConfig["opacity"] = m_configManager->getValue("background_opacity", 1.0);

        qDebug() << "MainWindow::showBackgroundConfig() - 传递配置:" << currentConfig;
        m_backgroundConfigDialog->setConfig(currentConfig);
    }

    m_backgroundConfigDialog->show();
    m_backgroundConfigDialog->raise();
    m_backgroundConfigDialog->activateWindow();

    // 测试：立即应用当前配置
    qDebug() << "MainWindow::showBackgroundConfig() - 测试立即应用配置";
    QTimer::singleShot(200, this, [this]()
                       { applyBackgroundConfig(); });
}

void MainWindow::showUIConfig()
{
    QMessageBox::information(this, "界面配置", "界面配置功能正在开发中...");
}

void MainWindow::showTerminalConfig()
{
    qDebug() << "MainWindow::showTerminalConfig() - 显示终端配置对话框";

    if (!m_terminalConfigDialog)
    {
        m_terminalConfigDialog = new TerminalConfigDialog(this);

        // 连接配置变化信号
        connect(m_terminalConfigDialog, &TerminalConfigDialog::configChanged,
                this, [this](const QString &key, const QVariant &value)
                {
                    qDebug() << "MainWindow - Terminal config changed:" << key << value;
                    if (m_configManager) {
                        m_configManager->setValue(key, value);
                        qDebug() << "MainWindow - Config saved to manager";

                        // 验证配置是否真的被保存了
                        QVariant savedValue = m_configManager->getValue(key);
                        qDebug() << "MainWindow - Verification: saved value is:" << savedValue;

                        // 立即应用字体配置
                        if (key.startsWith("terminal/font_") || key.startsWith("log/font_")) {
                            // 强制保存配置到文件
                            m_configManager->save();
                            qDebug() << "MainWindow - Font config saved to file";

                            QTimer::singleShot(100, this, [this]() {
                                qDebug() << "MainWindow - Applying font config immediately";
                                applyFontConfig();
                            });
                        }
                    } else {
                        qDebug() << "MainWindow - ERROR: ConfigManager is null!";
                    } });
    }

    // 设置当前配置
    if (m_configManager)
    {
        QVariantMap terminalConfig;
        terminalConfig["font_family"] = m_configManager->getValue("terminal/font_family", "Consolas");
        terminalConfig["font_size"] = m_configManager->getValue("terminal/font_size", 10);
        terminalConfig["font_weight"] = m_configManager->getValue("terminal/font_weight", "normal");
        terminalConfig["timestamp_enabled"] = m_configManager->getValue("terminal/timestamp_enabled", true);
        terminalConfig["timestamp_format"] = m_configManager->getValue("terminal/timestamp_format", "hh:mm:ss");
        terminalConfig["max_history"] = m_configManager->getValue("terminal/max_history", 1000);
        terminalConfig["echo_enabled"] = m_configManager->getValue("terminal/echo_enabled", true);

        m_terminalConfigDialog->setConfig(terminalConfig);
    }

    m_terminalConfigDialog->show();
    m_terminalConfigDialog->raise();
    m_terminalConfigDialog->activateWindow();
}

void MainWindow::resetConfig()
{
    int ret = QMessageBox::question(this, "重置配置",
                                    "确定要重置所有配置到默认值吗？\n此操作不可撤销。",
                                    QMessageBox::Yes | QMessageBox::No);

    if (ret == QMessageBox::Yes)
    {
        if (m_configManager)
        {
            m_configManager->resetToDefaults();
            applyConfig();
            m_terminal->appendMessage("配置已重置到默认值", LogManager::Info);
        }
    }
}

void MainWindow::onConfigChanged(const QString &key, const QVariant & /*value*/)
{
    try
    {
        qDebug() << "onConfigChanged() called with key:" << key;

        // 注意：不要在这里调用setValue，因为这会导致递归调用
        // 这个方法是用来响应配置变化的，而不是设置配置

        // 应用特定配置更改
        if (key.startsWith("background_") || key.startsWith("background"))
        {
            qDebug() << "Background config change detected for key:" << key;
            // 使用QTimer::singleShot延迟执行，避免在ConfigManager的mutex锁定期间调用getValue
            // 增加延迟时间，确保所有配置都保存完成
            QTimer::singleShot(100, this, [this, key]()
                               {
                qDebug() << "Delayed background config application starting for key:" << key;
                applyBackgroundConfig(); });
        }
        else if (key.startsWith("log_"))
        {
            qDebug() << "Applying log config for key:" << key;
            // 应用日志配置
            if (m_logManager)
            {
                m_logManager->loadConfig();
            }
        }
        else if (key.startsWith("terminal/font_") || key.startsWith("log/font_"))
        {
            qDebug() << "Applying font config for key:" << key;
            // 应用字体配置
            QTimer::singleShot(50, this, [this]()
                               { applyFontConfig(); });
        }

        qDebug() << "onConfigChanged() completed for key:" << key;

        // 安全地显示配置更新消息
        if (m_professionalTerminal)
        {
            // 只在调试模式下显示配置更新消息，避免干扰正常使用
            // m_professionalTerminal->appendMessage(QString("配置已更新: %1\n").arg(key));
        }
        else if (m_terminal)
        {
            // 暂时禁用终端消息，避免可能的崩溃
            // m_terminal->appendMessage(QString("配置已更新: %1").arg(key), LogManager::Debug);
            qDebug() << "配置已更新:" << key;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::onConfigChanged() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::onConfigChanged() - 未知异常";
    }
}

// 导入导出相关槽函数
void MainWindow::showImportExportMenu()
{
    QMenu menu(this);
    menu.addAction("导出配置", this, &MainWindow::exportConfig);
    menu.addAction("导入配置", this, &MainWindow::importConfig);
    menu.addSeparator();
    menu.addAction("导出命令", this, &MainWindow::exportCommands);
    menu.addAction("导入命令", this, &MainWindow::importCommands);

    menu.exec(m_importExportBtn->mapToGlobal(QPoint(0, m_importExportBtn->height())));
}

void MainWindow::exportConfig()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出配置",
                                                    "rf_tool_config.json",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_configManager)
    {
        bool success = m_configManager->exportConfig(fileName);
        if (success)
        {
            m_terminal->appendMessage("配置导出成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("配置导出失败", LogManager::Error);
        }
    }
}

void MainWindow::importConfig()
{
    QString fileName = QFileDialog::getOpenFileName(this, "导入配置",
                                                    "",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_configManager)
    {
        bool success = m_configManager->importConfig(fileName);
        if (success)
        {
            applyConfig();
            m_terminal->appendMessage("配置导入成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("配置导入失败", LogManager::Error);
        }
    }
}

void MainWindow::exportCommands()
{
    QString fileName = QFileDialog::getSaveFileName(this, "导出命令",
                                                    "rf_tool_commands.json",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_commandsList)
    {
        bool success = m_commandsList->exportCommands(fileName);
        if (success)
        {
            m_terminal->appendMessage("命令导出成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("命令导出失败", LogManager::Error);
        }
    }
}

void MainWindow::importCommands()
{
    QString fileName = QFileDialog::getOpenFileName(this, "导入命令",
                                                    "",
                                                    "JSON文件 (*.json)");
    if (!fileName.isEmpty() && m_commandsList)
    {
        bool success = m_commandsList->importCommands(fileName);
        if (success)
        {
            m_terminal->appendMessage("命令导入成功: " + fileName, LogManager::Info);
        }
        else
        {
            m_terminal->appendMessage("命令导入失败", LogManager::Error);
        }
    }
}

// 界面相关槽函数
void MainWindow::toggleCommandsPanel()
{
    // 参考历史面板的实现方式，简化逻辑
    bool isVisible = m_commandsFrame->isVisible();

    if (isVisible)
    {
        // 隐藏命令面板
        m_commandsFrame->setVisible(false);
        m_showCommandsBtn->setVisible(true);

        // 调整分割器大小
        QList<int> sizes = m_mainSplitter->sizes();
        if (sizes.size() >= 3)
        {
            sizes[1] += sizes[2]; // 将命令面板的宽度加到主工作区
            sizes[2] = 0;
            m_mainSplitter->setSizes(sizes);
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("常用命令面板已隐藏，点击右上角的📋命令按钮可重新显示\n");
        }
    }
    else
    {
        // 显示命令面板
        m_commandsFrame->setVisible(true);
        m_showCommandsBtn->setVisible(false);

        // 调整分割器大小
        QList<int> sizes = m_mainSplitter->sizes();
        if (sizes.size() >= 3)
        {
            int commandsWidth = 150;
            sizes[1] -= commandsWidth; // 从主工作区减去命令面板宽度
            sizes[2] = commandsWidth;
            m_mainSplitter->setSizes(sizes);
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("常用命令面板已显示\n");
        }
    }

    // 更新切换按钮文本
    if (m_toggleCommandsBtn)
    {
        m_toggleCommandsBtn->setText(!isVisible ? "✕" : "⚡");
    }

    // 保存状态到配置
    if (m_settings)
    {
        m_settings->setValue("window/commands_panel_visible", !isVisible);
        m_settings->sync();
    }
}

void MainWindow::updateConnectionStatus()
{
    if (m_isConnected)
    {
        m_connectionBtn->setText("断开连接");
        m_connectionTypeLabel->setText(m_connectionType);
        m_statusInfo->setText("🟢 已连接");

        // 更新详细连接信息
        updateConnectionDetails();

        // 更新按钮样式
        m_connectionBtn->setStyleSheet("QPushButton { background-color: #dc3545; }");
    }
    else
    {
        m_connectionBtn->setText("连接设备");
        m_connectionTypeLabel->setText("未连接");
        m_statusInfo->setText("⚪ 未连接");

        // 清空设备和配置信息
        if (m_connectionDetailsLabel)
        {
            m_connectionDetailsLabel->setText("无设备");
        }
        if (m_connectionTimeLabel)
        {
            m_connectionTimeLabel->setText("无配置");
        }

        // 恢复默认样式
        m_connectionBtn->setStyleSheet("");
    }
}

void MainWindow::updateConnectionDetails()
{
    if (!m_connectionDetailsLabel || !m_connectionTimeLabel)
        return;

    qDebug() << "MainWindow::updateConnectionDetails() - 开始更新连接详情";
    qDebug() << "MainWindow::updateConnectionDetails() - 连接参数:" << m_connectionParams;

    QString deviceInfo = "";
    QString configInfo = "";
    QString typeStr = m_connectionParams.value("type").toString();

    qDebug() << "MainWindow::updateConnectionDetails() - 连接类型:" << typeStr;

    // 根据连接类型构建简洁的设备和配置信息
    if (typeStr == "serial" || typeStr == "串口")
    {
        QString port = m_connectionParams.value("port").toString();
        int baudRate = m_connectionParams.value("baud_rate", m_connectionParams.value("baudRate", 115200)).toInt();
        int dataBits = m_connectionParams.value("data_bits", m_connectionParams.value("dataBits", 8)).toInt();
        QString stopBits = m_connectionParams.value("stop_bits", m_connectionParams.value("stopBits", "1")).toString();
        QString parity = m_connectionParams.value("parity", "无").toString();

        // 设备信息：COM口
        deviceInfo = port;

        // 配置信息：波特率和数据格式
        QString parityShort = (parity == "无" || parity == "None") ? "N" : parity.left(1).toUpper();
        configInfo = QString("%1bps | %2%3%4").arg(baudRate).arg(dataBits).arg(parityShort).arg(stopBits);
    }
    else if (typeStr == "tcp" || typeStr == "TCP")
    {
        QString host = m_connectionParams.value("host").toString();
        int port = m_connectionParams.value("port").toInt();
        QString encoding = m_connectionParams.value("encoding", "UTF-8").toString();

        // 设备信息：IP:端口
        deviceInfo = QString("%1:%2").arg(host).arg(port);

        // 配置信息：编码格式
        configInfo = QString("TCP | %1").arg(encoding);
    }
    else if (typeStr == "adb" || typeStr == "ADB")
    {
        QString deviceId = m_connectionParams.value("device", "").toString();

        if (!deviceId.isEmpty())
        {
            // 设备信息：设备序列号（简化显示）
            if (deviceId.contains("emulator"))
            {
                QString port = deviceId.split("-").last();
                deviceInfo = QString("模拟器:%1").arg(port);
                configInfo = "ADB | UTF-8";
            }
            else
            {
                // 真实设备，只显示前8位
                QString shortId = deviceId.length() > 8 ? deviceId.left(8) + "..." : deviceId;
                deviceInfo = shortId;
                configInfo = "ADB设备 | UTF-8";
            }
        }
        else
        {
            deviceInfo = "ADB默认";
            configInfo = "ADB | UTF-8";
        }
    }

    // 设置设备信息
    m_connectionDetailsLabel->setText(deviceInfo);

    // 设置配置信息
    m_connectionTimeLabel->setText(configInfo);

    qDebug() << "MainWindow::updateConnectionDetails() - 设备信息:" << deviceInfo;
    qDebug() << "MainWindow::updateConnectionDetails() - 配置信息:" << configInfo;
}

void MainWindow::performBatchUIUpdate()
{
    // 简化的UI更新，避免复杂的批量处理
    // 暂时禁用批量更新机制
}

void MainWindow::scheduleUIUpdate()
{
    // 简化的UI调度
    // 暂时禁用调度机制
}

void MainWindow::updateCommandButtons()
{
    bool connected = m_isConnected;

    // 这些控件需要设备连接才能使用
    m_sendBtn->setEnabled(connected);
    m_commandInput->setEnabled(connected);
    m_quickCmdInput->setEnabled(connected);

    // 第一个按钮是保存日志功能，始终可用
    m_cmdBtn1->setEnabled(true);

    // 第二、三个按钮需要连接才能使用
    m_cmdBtn2->setEnabled(connected);
    m_cmdBtn3->setEnabled(connected);

    // 更新新增的快捷功能按钮状态
    if (m_quickConnectBtn)
    {
        m_quickConnectBtn->setEnabled(!connected); // 未连接时可用
    }
    if (m_quickDisconnectBtn)
    {
        m_quickDisconnectBtn->setEnabled(connected); // 连接时可用
    }
    if (m_quickClearBtn)
    {
        m_quickClearBtn->setEnabled(true); // 始终可用
    }
    if (m_quickPauseBtn)
    {
        m_quickPauseBtn->setEnabled(true); // 始终可用
    }

    // 常用命令列表始终可用（可以查看、编辑、管理命令）
    if (m_commandsList)
    {
        m_commandsList->setEnabled(true); // 始终启用
    }

    // 更新添加命令按钮，也应该始终可用
    if (m_addCommandBtn)
    {
        m_addCommandBtn->setEnabled(true); // 始终启用
    }
}

void MainWindow::applyBackgroundConfig()
{
    try
    {
        qDebug() << "MainWindow::applyBackgroundConfig() - 开始应用终端背景配置";

        if (!m_configManager)
        {
            qDebug() << "MainWindow::applyBackgroundConfig() - ConfigManager为空";
            return;
        }

        QString type = m_configManager->getValue("background_type", "color").toString();
        QString color = m_configManager->getValue("background_color", "#000000").toString();
        QString color2 = m_configManager->getValue("background_color2", "#333333").toString();
        QString image = m_configManager->getValue("background_image", "").toString();
        QString imageMode = m_configManager->getValue("background_image_mode", "center").toString();
        double opacity = m_configManager->getValue("background_opacity", 1.0).toDouble();

        qDebug() << "MainWindow::applyBackgroundConfig() - 配置详情:";
        qDebug() << "  Type:" << type;
        qDebug() << "  Color:" << color;
        qDebug() << "  Color2:" << color2;
        qDebug() << "  Image:" << image;
        qDebug() << "  ImageMode:" << imageMode;
        qDebug() << "  Opacity:" << opacity;

        // 构建简化的终端样式
        QString terminalStyle = buildSimpleTerminalStyle(type, color, color2, image, imageMode, opacity);

        qDebug() << "MainWindow::applyBackgroundConfig() - 生成样式:" << terminalStyle;

        // 应用到终端组件
        applyStyleToTerminals(terminalStyle, opacity);

        qDebug() << "MainWindow::applyBackgroundConfig() - 背景配置应用完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyBackgroundConfig() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyBackgroundConfig() - 未知异常";
    }
}

QString MainWindow::buildSimpleTerminalStyle(const QString &type, const QString &color, const QString &color2,
                                             const QString &image, const QString &imageMode, double opacity)
{
    // 智能选择字体颜色
    auto getTextColor = [](const QString &bgColor) -> QString
    {
        QColor bg(bgColor);
        if (!bg.isValid())
            return "#ffffff";

        double brightness = (0.299 * bg.red() + 0.587 * bg.green() + 0.114 * bg.blue()) / 255.0;
        return brightness > 0.5 ? "#000000" : "#ffffff";
    };

    QString style;
    if (type == "color")
    {
        QString textColor = getTextColor(color);
        QColor bg(color);
        if (bg.isValid())
        {
            // 使用rgba格式支持透明度
            QColor bg(color);
            if (bg.isValid())
            {
                style = QString("QTextEdit { background-color: rgba(%1, %2, %3, %4); color: %5; border: 1px solid #ccc; border-radius: 4px; }")
                            .arg(bg.red())
                            .arg(bg.green())
                            .arg(bg.blue())
                            .arg(int(opacity * 255))
                            .arg(textColor);
            }
            else
            {
                style = QString("QTextEdit { background-color: %1; color: %2; border: 1px solid #ccc; border-radius: 4px; }")
                            .arg(color)
                            .arg(textColor);
            }
        }
        else
        {
            style = QString("QTextEdit { background-color: %1; color: %2; border: 1px solid #ccc; border-radius: 4px; }").arg(color, textColor);
        }
    }
    else if (type == "gradient")
    {
        QString textColor = getTextColor(color);

        // 使用rgba格式支持透明度的渐变
        QColor c1(color), c2(color2);
        if (c1.isValid() && c2.isValid())
        {
            QString gradientStyle = QString("qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 rgba(%1, %2, %3, %4), stop:1 rgba(%5, %6, %7, %8))")
                                        .arg(c1.red())
                                        .arg(c1.green())
                                        .arg(c1.blue())
                                        .arg(int(opacity * 255))
                                        .arg(c2.red())
                                        .arg(c2.green())
                                        .arg(c2.blue())
                                        .arg(int(opacity * 255));
            style = QString("QTextEdit { background: %1; color: %2; border: 1px solid #ccc; border-radius: 4px; }").arg(gradientStyle, textColor);
        }
        else
        {
            style = QString("QTextEdit { background-color: rgba(128, 128, 128, %1); color: %2; border: 1px solid #ccc; border-radius: 4px; }")
                        .arg(int(opacity * 255))
                        .arg(textColor);
        }
    }
    else if (type == "image" && !image.isEmpty())
    {
        // 检查图片文件是否存在
        QFileInfo imageFile(image);
        if (!imageFile.exists() || !imageFile.isFile())
        {
            qDebug() << "MainWindow::buildSimpleTerminalStyle() - 背景图片不存在:" << image;
            qDebug() << "MainWindow::buildSimpleTerminalStyle() - 使用默认白色背景";

            // 图片不存在，使用默认白色背景
            if (opacity < 1.0)
            {
                int alpha = int(opacity * 255);
                style = QString("QTextEdit { background-color: rgba(255, 255, 255, %1); color: #000000; border: 1px solid #ccc; border-radius: 4px; }")
                            .arg(alpha);
            }
            else
            {
                style = "QTextEdit { background-color: #ffffff; color: #000000; border: 1px solid #ccc; border-radius: 4px; }";
            }
        }
        else
        {
            // 图片存在，正常处理
            QString backgroundProps;
            if (imageMode == "stretch")
            {
                backgroundProps = "background-repeat: no-repeat; background-position: center; background-size: 100% 100%";
            }
            else if (imageMode == "tile")
            {
                backgroundProps = "background-repeat: repeat";
            }
            else if (imageMode == "fit")
            {
                backgroundProps = "background-repeat: no-repeat; background-position: center; background-size: contain";
            }
            else // center
            {
                backgroundProps = "background-repeat: no-repeat; background-position: center";
            }

            // 图片背景的透明度通过叠加半透明背景色实现
            if (opacity < 1.0)
            {
                // 使用半透明的黑色背景叠加，创建透明效果
                int alphaValue = int(opacity * 255);
                style = QString("QTextEdit { background-image: url(%1); %2; background-color: rgba(0, 0, 0, %3); color: #ffffff; border: 1px solid #ccc; border-radius: 4px; }")
                            .arg(image, backgroundProps)
                            .arg(255 - alphaValue); // 反向计算，透明度越高，叠加越少
            }
            else
            {
                style = QString("QTextEdit { background-image: url(%1); %2; color: #ffffff; border: 1px solid #ccc; border-radius: 4px; }")
                            .arg(image, backgroundProps);
            }
        }
    }
    else
    {
        // 默认黑色背景
        style = "QTextEdit { background-color: #000000; color: #ffffff; border: 1px solid #ccc; border-radius: 4px; }";
    }

    return style;
}

void MainWindow::applyStyleToTerminals(const QString &style, double opacity)
{
    // 应用到专业终端
    if (m_professionalTerminal && m_professionalTerminal->getDisplay())
    {
        QTextEdit *display = m_professionalTerminal->getDisplay();

        // 先清空样式，强制刷新
        display->setStyleSheet("");
        display->setStyleSheet(style);

        // 强制重绘
        display->update();
        display->repaint();

        qDebug() << "MainWindow::applyStyleToTerminals() - 专业终端样式已应用，透明度:" << opacity;
        qDebug() << "MainWindow::applyStyleToTerminals() - 应用的样式:" << style;
    }

    // 应用到旧终端（兼容性）
    if (m_terminal)
    {
        // 先清空样式，强制刷新
        m_terminal->setStyleSheet("");
        m_terminal->setStyleSheet(style);

        // 强制重绘
        m_terminal->update();
        m_terminal->repaint();

        qDebug() << "MainWindow::applyStyleToTerminals() - 旧终端样式已应用，透明度:" << opacity;
    }
}

// 其他槽函数
void MainWindow::showHelp()
{
    QString helpText = R"(
RF调试工具 Qt版本 - 帮助文档

=== 主要功能 ===
• 多种连接方式：串口、SSH、ADB、网络、FTP
• 智能命令管理：常用命令、历史记录、快捷命令
• 高级日志功能：时间戳、存储、过滤
• 现代化界面：Mac风格、背景配置、主题切换

=== 快捷键 ===
• Enter: 发送命令
• Ctrl+L: 清空终端
• Ctrl+S: 保存配置
• F1: 显示帮助

=== 连接类型 ===
• 串口: 支持高波特率 (9600-3,000,000)
• SSH: 支持密钥和密码认证
• ADB: Android设备调试
• 网络: TCP/UDP连接
• FTP: 文件传输协议

=== 技术支持 ===
如有问题，请联系开发团队。

=== 版本信息 ===
版本: 2.0.0
作者: flex
联系方式: <EMAIL>
    )";

    QMessageBox::information(this, "帮助", helpText);
}

void MainWindow::autoSave()
{
    if (m_configManager)
    {
        m_configManager->save();
    }

    saveCommonCommands();
    saveWindowConfig();
}

// 私有方法实现
void MainWindow::applyMacStyle()
{
    qDebug() << "applyMacStyle() started";
    // Mac风格样式已在main.cpp中设置
    // 这里可以添加额外的样式定制
    qDebug() << "applyMacStyle() completed";
}

void MainWindow::loadWindowConfig()
{
    if (!m_settings)
    {
        return;
    }

    // 恢复窗口几何
    QByteArray geometry = m_settings->value("window/geometry").toByteArray();
    if (!geometry.isEmpty())
    {
        restoreGeometry(geometry);
    }

    // 恢复窗口状态
    QByteArray state = m_settings->value("window/state").toByteArray();
    if (!state.isEmpty())
    {
        restoreState(state);
    }

    // 恢复分割器状态
    QByteArray splitterState = m_settings->value("window/splitter").toByteArray();
    if (!splitterState.isEmpty() && m_mainSplitter)
    {
        m_mainSplitter->restoreState(splitterState);
    }

    // 恢复命令面板可见性（仅读取设置，不应用UI状态）
    m_commandsPanelVisible = m_settings->value("window/commands_panel_visible", true).toBool();
}

void MainWindow::saveWindowConfig()
{
    if (!m_settings)
    {
        return;
    }

    // 保存窗口几何
    m_settings->setValue("window/geometry", saveGeometry());

    // 保存窗口状态
    m_settings->setValue("window/state", saveState());

    // 保存分割器状态
    if (m_mainSplitter)
    {
        m_settings->setValue("window/splitter", m_mainSplitter->saveState());
    }

    // 保存命令面板可见性
    m_settings->setValue("window/commands_panel_visible", m_commandsPanelVisible);

    m_settings->sync();
}

void MainWindow::applyPanelStates()
{
    qDebug() << "MainWindow::applyPanelStates() - 开始应用面板状态";
    qDebug() << "MainWindow::applyPanelStates() - 命令面板可见性:" << m_commandsPanelVisible;

    // 设置命令面板和显示按钮的状态
    if (m_commandsFrame && m_showCommandsBtn && m_toggleCommandsBtn)
    {
        // 首先检查命令面板的实际可见性，确保状态一致
        bool actualVisible = m_commandsFrame->isVisible();
        qDebug() << "MainWindow::applyPanelStates() - 配置中的状态:" << m_commandsPanelVisible;
        qDebug() << "MainWindow::applyPanelStates() - 实际UI状态:" << actualVisible;

        // 如果状态不一致，以配置为准
        if (actualVisible != m_commandsPanelVisible)
        {
            qDebug() << "MainWindow::applyPanelStates() - 状态不一致，以配置为准";
            m_commandsFrame->setVisible(m_commandsPanelVisible);
        }

        // 设置显示按钮状态
        bool shouldShowButton = !m_commandsPanelVisible;
        m_showCommandsBtn->setVisible(shouldShowButton);
        m_toggleCommandsBtn->setText(m_commandsPanelVisible ? "✕" : "⚡");

        qDebug() << "MainWindow::applyPanelStates() - 最终命令面板可见性:" << m_commandsPanelVisible;
        qDebug() << "MainWindow::applyPanelStates() - 显示按钮可见性:" << shouldShowButton;
        qDebug() << "MainWindow::applyPanelStates() - 按钮实际状态:";
        qDebug() << "  按钮大小:" << m_showCommandsBtn->size();
        qDebug() << "  按钮位置:" << m_showCommandsBtn->pos();
        qDebug() << "  按钮是否可见:" << m_showCommandsBtn->isVisible();
        qDebug() << "  按钮是否启用:" << m_showCommandsBtn->isEnabled();
        qDebug() << "  按钮父控件:" << (m_showCommandsBtn->parent() ? "存在" : "空");

        // 强制更新按钮
        m_showCommandsBtn->update();
        m_showCommandsBtn->repaint();
    }
    else
    {
        qDebug() << "MainWindow::applyPanelStates() - 警告：UI组件未创建";
        qDebug() << "  m_commandsFrame:" << (m_commandsFrame ? "存在" : "空");
        qDebug() << "  m_showCommandsBtn:" << (m_showCommandsBtn ? "存在" : "空");
        qDebug() << "  m_toggleCommandsBtn:" << (m_toggleCommandsBtn ? "存在" : "空");
    }

    qDebug() << "MainWindow::applyPanelStates() - 面板状态应用完成";
}

void MainWindow::forceShowCommandButton()
{
    qDebug() << "MainWindow::forceShowCommandButton() - 强制显示命令按钮";

    if (!m_showCommandsBtn)
    {
        qDebug() << "MainWindow::forceShowCommandButton() - 按钮不存在";
        return;
    }

    // 强制设置按钮为可见
    m_showCommandsBtn->setVisible(true);
    m_showCommandsBtn->move(100, 10);
    m_showCommandsBtn->setStyleSheet(
        "QPushButton {"
        "    background-color: lime;"
        "    color: black;"
        "    border: 3px solid blue;"
        "    border-radius: 5px;"
        "    font-size: 18px;"
        "    font-weight: bold;"
        "}");
    m_showCommandsBtn->setText("强制显示");
    m_showCommandsBtn->setFixedSize(150, 50);
    m_showCommandsBtn->raise();
    m_showCommandsBtn->update();
    m_showCommandsBtn->repaint();

    qDebug() << "MainWindow::forceShowCommandButton() - 强制显示完成";
    qDebug() << "  按钮位置:" << m_showCommandsBtn->pos();
    qDebug() << "  按钮大小:" << m_showCommandsBtn->size();
    qDebug() << "  按钮可见:" << m_showCommandsBtn->isVisible();
}

void MainWindow::resizeEvent(QResizeEvent *event)
{
    QMainWindow::resizeEvent(event);

    // 保存窗口大小
    if (m_settings)
    {
        m_settings->setValue("window/size", event->size());
    }
}

void MainWindow::loadCommonCommands()
{
    qDebug() << "loadCommonCommands() started";

    if (!m_configManager || !m_commandsList)
    {
        qDebug() << "ConfigManager or CommandsList is null, returning";
        return;
    }

    qDebug() << "Getting commands from config manager...";
    QVariantList commands;
    try
    {
        commands = m_configManager->getCommands();
        qDebug() << "Found" << commands.size() << "commands in config";
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception getting commands:" << e.what();
        return;
    }
    catch (...)
    {
        qDebug() << "Unknown exception getting commands";
        return;
    }

    for (const QVariant &var : commands)
    {
        QVariantMap command = var.toMap();
        QString name = command["name"].toString();
        QString content = command["content"].toString();
        QString description = command["description"].toString();

        if (!name.isEmpty() && !content.isEmpty())
        {
            m_commandsList->addCommand(name, content, description);
        }
    }

    // 如果没有命令，添加一些默认命令
    if (commands.isEmpty())
    {
        qDebug() << "No commands found, adding default commands...";
        m_commandsList->addCommand("列出文件", "ls -la", "显示当前目录的详细文件列表");
        m_commandsList->addCommand("当前目录", "pwd", "显示当前工作目录");
        m_commandsList->addCommand("系统信息", "uname -a", "显示系统信息");
        m_commandsList->addCommand("磁盘使用", "df -h", "显示磁盘使用情况");
        m_commandsList->addCommand("内存使用", "free -h", "显示内存使用情况");
        qDebug() << "Default commands added";
    }

    qDebug() << "loadCommonCommands() completed";
}

void MainWindow::saveCommonCommands()
{
    if (!m_configManager || !m_commandsList)
    {
        return;
    }

    QVariantList commands = m_commandsList->toVariantList();
    m_configManager->setCommands(commands);
}

void MainWindow::applyConfig()
{
    qDebug() << "MainWindow::applyConfig() - 开始";

    if (!m_configManager)
    {
        qDebug() << "MainWindow::applyConfig() - ConfigManager为空，返回";
        return;
    }

    // 暂时只应用背景配置，其他部分逐步测试
    qDebug() << "MainWindow::applyConfig() - 应用背景配置";
    try
    {
        applyBackgroundConfig();
        qDebug() << "MainWindow::applyConfig() - 背景配置完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyConfig() - 背景配置异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyConfig() - 背景配置未知异常";
    }

    // 逐步恢复功能 - 测试日志配置
    qDebug() << "MainWindow::applyConfig() - 开始测试日志配置";

    // 应用日志配置
    qDebug() << "MainWindow::applyConfig() - 应用日志配置";
    if (m_logManager)
    {
        qDebug() << "MainWindow::applyConfig() - 创建日志配置映射";
        QVariantMap logConfig;

        qDebug() << "MainWindow::applyConfig() - 获取日志配置值";
        try
        {
            logConfig["timestamp_enabled"] = m_configManager->getValue("log_timestamp_enabled", true);
            logConfig["timestamp_format"] = m_configManager->getValue("log_timestamp_format", "yyyy-MM-dd hh:mm:ss");
            logConfig["echo_enabled"] = m_configManager->getValue("log_echo_enabled", true);
            logConfig["file_enabled"] = m_configManager->getValue("log_file_enabled", false);
            logConfig["file_path"] = m_configManager->getValue("log_file_path", "");
            qDebug() << "MainWindow::applyConfig() - 日志配置值获取完成";

            qDebug() << "MainWindow::applyConfig() - 设置日志管理器配置";
            m_logManager->setConfig(logConfig);
            qDebug() << "MainWindow::applyConfig() - 日志管理器配置完成";
        }
        catch (const std::exception &e)
        {
            qDebug() << "MainWindow::applyConfig() - 日志配置异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "MainWindow::applyConfig() - 日志配置未知异常";
        }
    }
    else
    {
        qDebug() << "MainWindow::applyConfig() - LogManager为空";
    }

    // 应用终端配置
    qDebug() << "MainWindow::applyConfig() - 应用终端配置";
    if (m_terminal)
    {
        qDebug() << "MainWindow::applyConfig() - 获取终端配置值";
        try
        {
            bool timestampEnabled = m_configManager->getValue("terminal_timestamp_enabled", true).toBool();
            QString timestampFormat = m_configManager->getValue("terminal_timestamp_format", "hh:mm:ss").toString();
            bool echoEnabled = m_configManager->getValue("terminal_echo_enabled", true).toBool();
            qDebug() << "MainWindow::applyConfig() - 终端配置值获取完成";

            qDebug() << "MainWindow::applyConfig() - 设置终端配置";
            m_terminal->setTimestampEnabled(timestampEnabled);
            m_terminal->setTimestampFormat(timestampFormat);
            m_terminal->setEchoEnabled(echoEnabled);
            qDebug() << "MainWindow::applyConfig() - 终端配置完成";
        }
        catch (const std::exception &e)
        {
            qDebug() << "MainWindow::applyConfig() - 终端配置异常:" << e.what();
        }
        catch (...)
        {
            qDebug() << "MainWindow::applyConfig() - 终端配置未知异常";
        }
    }
    else
    {
        qDebug() << "MainWindow::applyConfig() - Terminal为空";
    }

    // 更新快捷命令按钮文本
    qDebug() << "MainWindow::applyConfig() - 更新快捷命令按钮";
    qDebug() << "ConfigManager配置文件路径:" << m_configManager->configFile();
    try
    {
        if (m_cmdBtn1)
        {
            QString cmd1Name = m_configManager->getValue("quick_command_1_name", "保存日志").toString();
            qDebug() << "读取到的quick_command_1_name值:" << cmd1Name;
            m_cmdBtn1->setText(cmd1Name);
            qDebug() << "MainWindow::applyConfig() - 命令按钮1更新完成";
        }
        if (m_cmdBtn2)
        {
            QString cmd2Name = m_configManager->getValue("quick_command_2_name", "路径").toString();
            m_cmdBtn2->setText(cmd2Name);
            qDebug() << "MainWindow::applyConfig() - 命令按钮2更新完成";
        }
        if (m_cmdBtn3)
        {
            QString cmd3Name = m_configManager->getValue("quick_command_3_name", "用户").toString();
            m_cmdBtn3->setText(cmd3Name);
            qDebug() << "MainWindow::applyConfig() - 命令按钮3更新完成";
        }
        qDebug() << "MainWindow::applyConfig() - 快捷命令按钮更新完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyConfig() - 快捷命令按钮异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyConfig() - 快捷命令按钮未知异常";
    }

    // 加载时间戳设置
    qDebug() << "MainWindow::applyConfig() - 加载时间戳设置";
    try
    {
        bool timestampEnabled = m_configManager->getValue("timestamp_enabled", false).toBool();
        m_timestampEnabled = timestampEnabled;

        if (m_timestampBtn)
        {
            m_timestampBtn->setChecked(timestampEnabled);
            if (timestampEnabled)
            {
                m_timestampBtn->setText("🕐 时间戳✓");
                m_timestampBtn->setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; }");
            }
            else
            {
                m_timestampBtn->setText("🕐 时间戳");
                m_timestampBtn->setStyleSheet("");
            }
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->setTimestampEnabled(timestampEnabled);
        }

        qDebug() << "MainWindow::applyConfig() - 时间戳设置加载完成，状态:" << timestampEnabled;
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyConfig() - 时间戳设置异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyConfig() - 时间戳设置未知异常";
    }

    // 应用字体配置
    qDebug() << "MainWindow::applyConfig() - 应用字体配置";
    try
    {
        applyFontConfig();
        qDebug() << "MainWindow::applyConfig() - 字体配置完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyConfig() - 字体配置异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyConfig() - 字体配置未知异常";
    }

    qDebug() << "MainWindow::applyConfig() - 完成";
}

void MainWindow::applyFontConfig()
{
    qDebug() << "MainWindow::applyFontConfig() - 开始应用字体配置";

    if (!m_configManager)
    {
        qDebug() << "MainWindow::applyFontConfig() - ConfigManager为空，返回";
        return;
    }

    try
    {
        // 获取终端字体配置
        QString terminalFontFamily = m_configManager->getValue("terminal/font_family", "Consolas").toString();
        int terminalFontSize = m_configManager->getValue("terminal/font_size", 10).toInt();
        QString terminalFontWeight = m_configManager->getValue("terminal/font_weight", "normal").toString();

        // 获取日志字体配置
        QString logFontFamily = m_configManager->getValue("log/font_family", "Consolas").toString();
        int logFontSize = m_configManager->getValue("log/font_size", 10).toInt();
        QString logFontWeight = m_configManager->getValue("log/font_weight", "normal").toString();

        // 调试：检查配置值（可选，用于故障排除）
        // qDebug() << "Raw config values:";
        // qDebug() << "  log/font_family:" << m_configManager->getValue("log/font_family", "DEFAULT");
        // qDebug() << "  log/font_size:" << m_configManager->getValue("log/font_size", -999);
        // qDebug() << "  log/font_weight:" << m_configManager->getValue("log/font_weight", "DEFAULT");

        // 如果日志字体配置不存在，使用终端字体配置作为默认值
        if (logFontSize <= 0)
        {
            qDebug() << "Log font size invalid, using terminal font size as fallback";
            logFontSize = terminalFontSize;
            logFontFamily = terminalFontFamily;
            logFontWeight = terminalFontWeight;

            // 保存默认的日志字体配置
            m_configManager->setValue("log/font_family", logFontFamily);
            m_configManager->setValue("log/font_size", logFontSize);
            m_configManager->setValue("log/font_weight", logFontWeight);
            qDebug() << "Saved default log font config";
        }

        qDebug() << "MainWindow::applyFontConfig() - Terminal font config:" << terminalFontFamily << terminalFontSize << terminalFontWeight;
        qDebug() << "MainWindow::applyFontConfig() - Log font config:" << logFontFamily << logFontSize << logFontWeight;

        // 验证字体大小
        if (logFontSize <= 0)
        {
            qDebug() << "MainWindow::applyFontConfig() - ERROR: Invalid log font size, using default";
            logFontSize = 10;
        }
        if (terminalFontSize <= 0)
        {
            qDebug() << "MainWindow::applyFontConfig() - ERROR: Invalid terminal font size, using default";
            terminalFontSize = 10;
        }

        // 应用到专业终端（日志显示区域）
        if (m_professionalTerminal)
        {
            qDebug() << "MainWindow::applyFontConfig() - Applying font to ProfessionalTerminal:" << logFontFamily << logFontSize << logFontWeight;
            m_professionalTerminal->setTerminalFont(logFontFamily, logFontSize, logFontWeight);
            qDebug() << "MainWindow::applyFontConfig() - ProfessionalTerminal font applied";

            // 验证字体是否真的被设置了
            QFont currentFont = m_professionalTerminal->font();
            qDebug() << "MainWindow::applyFontConfig() - Current font after setting:" << currentFont.family()
                     << "pointSize:" << currentFont.pointSize()
                     << "pixelSize:" << currentFont.pixelSize();
        }
        else
        {
            qDebug() << "MainWindow::applyFontConfig() - WARNING: m_professionalTerminal is null";
        }

        // 应用到交互式终端
        if (m_terminal)
        {
            m_terminal->setTerminalFont(terminalFontFamily, terminalFontSize);
            qDebug() << "MainWindow::applyFontConfig() - 交互式终端字体配置完成";
        }

        qDebug() << "MainWindow::applyFontConfig() - Font configuration completed";

        // 验证字体是否正确应用，但不显示消息
        if (m_professionalTerminal)
        {
            QFont actualFont = m_professionalTerminal->font();
            int actualSize = actualFont.pointSize();
            if (actualSize <= 0)
            {
                actualSize = actualFont.pixelSize();
            }

            // 仅在调试日志中记录，不向用户显示
            qDebug() << "MainWindow::applyFontConfig() - Font verification:"
                     << logFontFamily << logFontSize << "pt (actual:" << actualSize << ")";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::applyFontConfig() - Exception:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::applyFontConfig() - 未知异常";
    }
}

QString MainWindow::buildBackgroundStyle(const QVariantMap &config)
{
    QString type = config.value("type", "color").toString();
    QString color = config.value("color", "#f8f9fa").toString();
    QString image = config.value("image", "").toString();
    double opacity = config.value("opacity", 1.0).toDouble();

    qDebug() << "MainWindow::buildBackgroundStyle() - Type:" << type << "Color:" << color << "Image:" << image;

    QString style;

    if (type == "color")
    {
        style = QString("QMainWindow { background-color: %1; }").arg(color);
    }
    else if (type == "image" && !image.isEmpty())
    {
        style = QString("QMainWindow { background-image: url(%1); background-repeat: no-repeat; background-position: center; background-attachment: fixed; }").arg(image);
    }
    else if (type == "gradient")
    {
        QString color2 = config.value("color2", "#e9ecef").toString();
        style = QString("QMainWindow { background: qlineargradient(x1:0, y1:0, x2:0, y2:1, stop:0 %1, stop:1 %2); }").arg(color, color2);
    }

    // 应用透明度
    if (opacity < 1.0)
    {
        style += QString(" QMainWindow { opacity: %1; }").arg(opacity);
    }

    qDebug() << "MainWindow::buildBackgroundStyle() - 生成样式:" << style;
    return style;
}

void MainWindow::updateMainWindowStyle(const QString &bgStyle)
{
    try
    {
        if (!bgStyle.isEmpty())
        {
            qDebug() << "MainWindow::updateMainWindowStyle() - 应用样式:" << bgStyle;

            // 简化处理：直接设置样式，不进行复杂的合并
            setStyleSheet(bgStyle);

            qDebug() << "MainWindow::updateMainWindowStyle() - 样式应用完成";
        }
        else
        {
            qDebug() << "MainWindow::updateMainWindowStyle() - 样式为空";
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::updateMainWindowStyle() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::updateMainWindowStyle() - 未知异常";
    }
}

// 事件处理函数实现
void MainWindow::closeEvent(QCloseEvent *event)
{
    qDebug() << "MainWindow::closeEvent() - 开始处理关闭事件";

    // 保存配置（快速操作）
    qDebug() << "MainWindow::closeEvent() - 保存配置";
    saveWindowConfig();
    saveCommonCommands();

    // 非阻塞断开连接
    if (m_isConnected && m_connectionManager)
    {
        qDebug() << "MainWindow::closeEvent() - 断开连接";
        // 使用异步方式断开连接，不等待完成
        QTimer::singleShot(0, m_connectionManager, &ConnectionManager::disconnectFromDevice);
    }

    qDebug() << "MainWindow::closeEvent() - 接受关闭事件";
    // 立即接受关闭事件，不等待断开连接完成
    event->accept();
}

void MainWindow::showEvent(QShowEvent *event)
{
    QMainWindow::showEvent(event);

    // 窗口显示时的处理逻辑
    if (m_terminal)
    {
        m_terminal->appendMessage("窗口已显示", LogManager::Debug);
    }
}

void MainWindow::loadConnectionHistory()
{
    if (!m_settings || !m_historyList)
        return;

    m_historyList->clear();

    // 从设置中加载历史连接
    int size = m_settings->beginReadArray("connectionHistory");
    for (int i = 0; i < size; ++i)
    {
        m_settings->setArrayIndex(i);
        QString type = m_settings->value("type").toString();
        QString name = m_settings->value("name").toString();
        QString config = m_settings->value("config").toString();
        QDateTime lastUsed = m_settings->value("lastUsed").toDateTime();

        if (!type.isEmpty() && !name.isEmpty())
        {
            QListWidgetItem *item = new QListWidgetItem;
            item->setText(QString("%1\n%2").arg(name, type));
            item->setData(Qt::UserRole, type);
            item->setData(Qt::UserRole + 1, config);
            item->setData(Qt::UserRole + 2, lastUsed);
            item->setToolTip(QString("类型: %1\n配置: %2\n最后使用: %3")
                                 .arg(type, config, lastUsed.toString("yyyy-MM-dd hh:mm:ss")));

            m_historyList->addItem(item);
        }
    }
    m_settings->endArray();

    // 添加日志保存选项
    if (m_historyList->count() == 0)
    {
        // 添加日志保存选项
        QListWidgetItem *item1 = new QListWidgetItem;
        item1->setText("💾 保存当前日志\n📄 保存终端显示的所有内容\n🕐 " + QDateTime::currentDateTime().toString("MM-dd hh:mm"));
        item1->setData(Qt::UserRole, "save_current");
        item1->setToolTip("点击保存当前终端显示的所有日志内容到文件");
        m_historyList->addItem(item1);

        QListWidgetItem *item2 = new QListWidgetItem;
        item2->setText("📋 保存会话日志\n📄 保存本次会话的完整日志\n🕐 " + QDateTime::currentDateTime().toString("MM-dd hh:mm"));
        item2->setData(Qt::UserRole, "save_session");
        item2->setToolTip("点击保存从连接开始到现在的完整会话日志");
        m_historyList->addItem(item2);

        QListWidgetItem *item3 = new QListWidgetItem;
        item3->setText("🗂️ 保存原始数据\n📄 保存未处理的原始通信数据\n🕐 " + QDateTime::currentDateTime().toString("MM-dd hh:mm"));
        item3->setData(Qt::UserRole, "save_raw");
        item3->setToolTip("点击保存原始的通信数据，包含所有控制字符");
        m_historyList->addItem(item3);

        QListWidgetItem *item4 = new QListWidgetItem;
        item4->setText("⚙️ 自动保存设置\n📄 配置自动保存选项\n🕐 " + QDateTime::currentDateTime().toString("MM-dd hh:mm"));
        item4->setData(Qt::UserRole, "auto_save_config");
        item4->setToolTip("点击配置自动保存日志的选项");
        m_historyList->addItem(item4);
    }

    qDebug() << "MainWindow::loadConnectionHistory() - 加载了" << m_historyList->count() << "个日志保存选项";
}

void MainWindow::saveConnectionHistory()
{
    qDebug() << "MainWindow::saveConnectionHistory() - 开始保存连接历史";

    if (!m_settings || !m_historyList)
    {
        qDebug() << "MainWindow::saveConnectionHistory() - 设置或历史列表为空";
        return;
    }

    try
    {
        m_settings->beginWriteArray("connectionHistory");
        for (int i = 0; i < m_historyList->count(); ++i)
        {
            QListWidgetItem *item = m_historyList->item(i);
            if (item)
            {
                m_settings->setArrayIndex(i);
                m_settings->setValue("type", item->data(Qt::UserRole).toString());
                m_settings->setValue("name", item->text().split('\n').first());
                m_settings->setValue("config", item->data(Qt::UserRole + 1).toString());
                m_settings->setValue("lastUsed", item->data(Qt::UserRole + 2).toDateTime());
            }
        }
        m_settings->endArray();
        m_settings->sync(); // 确保数据写入磁盘

        qDebug() << "MainWindow::saveConnectionHistory() - 保存了" << m_historyList->count() << "个历史连接";
    }
    catch (...)
    {
        qDebug() << "MainWindow::saveConnectionHistory() - 保存时发生异常";
    }
}

void MainWindow::addConnectionToHistory(const QString &type, const QString &config)
{
    qDebug() << "MainWindow::addConnectionToHistory() - 开始添加历史记录:" << type << config;

    if (!m_historyList)
    {
        qDebug() << "MainWindow::addConnectionToHistory() - 历史列表为空";
        return;
    }

    if (type.isEmpty() || config.isEmpty())
    {
        qDebug() << "MainWindow::addConnectionToHistory() - 类型或配置为空";
        return;
    }

    // 生成连接名称
    QString name = QString("%1连接_%2").arg(type, QDateTime::currentDateTime().toString("MM-dd hh:mm"));

    try
    {
        // 检查是否已存在相同配置
        for (int i = 0; i < m_historyList->count(); ++i)
        {
            QListWidgetItem *item = m_historyList->item(i);
            if (item && item->data(Qt::UserRole + 1).toString() == config)
            {
                qDebug() << "MainWindow::addConnectionToHistory() - 找到相同配置，更新时间";
                // 更新最后使用时间
                item->setData(Qt::UserRole + 2, QDateTime::currentDateTime());
                // 移到顶部
                m_historyList->takeItem(i);
                m_historyList->insertItem(0, item);
                saveConnectionHistory();
                return;
            }
        }
    }
    catch (...)
    {
        qDebug() << "MainWindow::addConnectionToHistory() - 检查现有项目时发生异常";
        return;
    }

    // 添加新的历史记录 - 使用简洁美观的格式
    QListWidgetItem *item = new QListWidgetItem;

    // 根据连接类型选择图标和格式化配置信息
    QString icon, displayText, formattedConfig;
    if (type == "串口")
    {
        icon = "●"; // 使用圆点图标，类似参考样式
        // 简化串口配置显示
        QStringList parts = config.split(',');
        if (parts.size() >= 2)
        {
            formattedConfig = parts[1]; // 只显示波特率
            displayText = QString("%1 %2  %3").arg(icon, parts[0], formattedConfig);
        }
        else
        {
            formattedConfig = config;
            displayText = QString("%1 %2").arg(icon, formattedConfig);
        }
    }
    else if (type == "TCP")
    {
        icon = "●";
        formattedConfig = config;
        displayText = QString("%1 %2").arg(icon, formattedConfig);
    }
    else if (type == "ADB")
    {
        icon = "●";
        // 显示ADB设备序列号
        if (!config.isEmpty())
        {
            formattedConfig = config; // 直接显示设备序列号
            displayText = QString("%1 ADB  %2").arg(icon, formattedConfig);
        }
        else
        {
            formattedConfig = "ADB设备";
            displayText = QString("%1 %2").arg(icon, formattedConfig);
        }
    }
    else
    {
        icon = "●";
        formattedConfig = config;
        displayText = QString("%1 %2").arg(icon, formattedConfig);
    }

    item->setText(displayText);
    item->setData(Qt::UserRole, type);
    item->setData(Qt::UserRole + 1, config);
    item->setData(Qt::UserRole + 2, QDateTime::currentDateTime());
    item->setToolTip(QString("类型: %1\n配置: %2\n最后使用: %3")
                         .arg(type, config, QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss")));

    m_historyList->insertItem(0, item);

    // 限制历史记录数量
    while (m_historyList->count() > 20)
    {
        delete m_historyList->takeItem(m_historyList->count() - 1);
    }

    saveConnectionHistory();
    qDebug() << "MainWindow::addConnectionToHistory() - 添加历史连接:" << name;
}

void MainWindow::toggleHistoryPanel()
{
    bool isVisible = m_historyFrame->isVisible();

    if (isVisible)
    {
        // 隐藏历史面板
        m_historyFrame->setVisible(false);
        m_showHistoryBtn->setVisible(true);

        // 调整分割器大小
        QList<int> sizes = m_mainSplitter->sizes();
        if (sizes.size() >= 3)
        {
            sizes[1] += sizes[0]; // 将历史面板的宽度加到主工作区
            sizes[0] = 0;
            m_mainSplitter->setSizes(sizes);
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("历史连接面板已隐藏\n");
        }
    }
    else
    {
        // 显示历史面板
        m_historyFrame->setVisible(true);
        m_showHistoryBtn->setVisible(false);

        // 调整分割器大小
        QList<int> sizes = m_mainSplitter->sizes();
        if (sizes.size() >= 3)
        {
            int historyWidth = 200;
            sizes[1] -= historyWidth; // 从主工作区减去历史面板宽度
            sizes[0] = historyWidth;
            m_mainSplitter->setSizes(sizes);
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("历史连接面板已显示\n");
        }
    }
}

void MainWindow::onLogSaveItemClicked(QListWidgetItem *item)
{
    if (!item)
        return;

    QString saveType = item->data(Qt::UserRole).toString();

    if (saveType == "save_current")
    {
        // 保存当前终端显示内容
        saveCurrentTerminalContent();
    }
    else if (saveType == "save_session")
    {
        // 保存会话日志
        saveSessionLog();
    }
    else if (saveType == "save_raw")
    {
        // 保存原始数据
        saveRawData();
    }
    else if (saveType == "auto_save_config")
    {
        // 打开自动保存配置
        showLogConfig();
    }
}

void MainWindow::saveCurrentTerminalContent()
{
    if (!m_professionalTerminal || !m_professionalTerminal->getDisplay())
    {
        QMessageBox::warning(this, "保存失败", "没有可保存的终端内容");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存当前终端内容",
                                                    QString("terminal_content_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
                                                    "文本文件 (*.txt);;所有文件 (*.*)");

    if (!fileName.isEmpty())
    {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QTextStream out(&file);
            out.setCodec("UTF-8");
            out << m_professionalTerminal->getDisplay()->toPlainText();
            file.close();

            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage(QString("💾 当前终端内容已保存到: %1\n").arg(fileName));
            }
        }
        else
        {
            QMessageBox::warning(this, "保存失败", "无法创建文件: " + fileName);
        }
    }
}

void MainWindow::saveSessionLog()
{
    if (!m_logManager)
    {
        QMessageBox::warning(this, "保存失败", "日志管理器未初始化");
        return;
    }

    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存会话日志",
                                                    QString("session_log_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
                                                    "文本文件 (*.txt);;所有文件 (*.*)");

    if (!fileName.isEmpty())
    {
        if (m_logManager->exportLogs(fileName))
        {
            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage(QString("📋 会话日志已保存到: %1\n").arg(fileName));
            }
        }
        else
        {
            QMessageBox::warning(this, "保存失败", "无法保存会话日志到: " + fileName);
        }
    }
}

void MainWindow::saveRawData()
{
    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存原始数据",
                                                    QString("raw_data_%1.bin").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
                                                    "二进制文件 (*.bin);;文本文件 (*.txt);;所有文件 (*.*)");

    if (!fileName.isEmpty())
    {
        // 这里可以保存原始的通信数据
        // 目前先保存终端内容作为原始数据
        if (m_professionalTerminal && m_professionalTerminal->getDisplay())
        {
            QFile file(fileName);
            if (file.open(QIODevice::WriteOnly | QIODevice::Text))
            {
                QTextStream out(&file);
                out.setCodec("UTF-8");
                out << m_professionalTerminal->getDisplay()->toPlainText();
                file.close();

                if (m_professionalTerminal)
                {
                    m_professionalTerminal->appendMessage(QString("🗂️ 原始数据已保存到: %1\n").arg(fileName));
                }
            }
            else
            {
                QMessageBox::warning(this, "保存失败", "无法创建文件: " + fileName);
            }
        }
        else
        {
            QMessageBox::warning(this, "保存失败", "没有可保存的原始数据");
        }
    }
}

void MainWindow::onHistoryItemClicked(QListWidgetItem *item)
{
    if (!item)
        return;

    QString type = item->data(Qt::UserRole).toString();
    QString config = item->data(Qt::UserRole + 1).toString();

    // 获取显示文本（去掉可能的连接状态图标）
    QString displayText = item->text();
    if (displayText.startsWith("🟢 "))
    {
        displayText = displayText.mid(3);
    }

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("🔗 点击快速连接: %1 (%2)\n").arg(displayText.split('\n').first(), type));
    }

    // 更新最后使用时间
    item->setData(Qt::UserRole + 2, QDateTime::currentDateTime());

    // 如果当前已连接，先断开
    if (m_isConnected && m_connectionManager)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("⚠️ 断开当前连接，准备切换到新连接...\n");
        }

        // 先更新UI状态
        m_isConnected = false;
        m_connectionType = "切换中...";
        updateConnectionStatus();
        updateHistoryConnectionStatus(); // 清除当前高亮

        m_connectionManager->disconnectFromDevice();

        // 优化：快速切换连接
        QTimer::singleShot(300, this, [this, type, config, displayText]()
                           {
                               if (m_professionalTerminal)
                               {
                                   m_professionalTerminal->appendMessage(QString("🔄 开始连接到: %1\n").arg(displayText.split('\n').first()));
                               }
                               connectToDeviceByType(type, config); });
    }
    else
    {
        // 直接连接
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("🔄 开始连接到: %1\n").arg(displayText.split('\n').first()));
        }
        connectToDeviceByType(type, config);
    }

    saveConnectionHistory();
}

void MainWindow::onHistoryItemDoubleClicked(QListWidgetItem *item)
{
    if (!item)
        return;

    QString type = item->data(Qt::UserRole).toString();
    QString config = item->data(Qt::UserRole + 1).toString();

    // 获取显示文本（去掉可能的连接状态图标）
    QString displayText = item->text();
    if (displayText.startsWith("🟢 "))
    {
        displayText = displayText.mid(3);
    }

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("⚙️ 双击编辑连接配置: %1 (%2)\n").arg(displayText.split('\n').first(), type));
    }

    // 双击打开连接配置对话框进行编辑
    if (!m_connectionDialog)
    {
        m_connectionDialog = new ConnectionDialog(this);
        connect(m_connectionDialog, &ConnectionDialog::connectionRequested,
                this, &MainWindow::connectDevice);
    }

    // 解析配置并设置到对话框
    QJsonDocument doc = QJsonDocument::fromJson(config.toUtf8());
    if (!doc.isNull() && doc.isObject())
    {
        QVariantMap params;
        QJsonObject configObj = doc.object();

        // 将JSON对象转换为QVariantMap
        for (auto it = configObj.begin(); it != configObj.end(); ++it)
        {
            params[it.key()] = it.value().toVariant();
        }

        // 根据连接类型设置对话框
        if (type == "Serial")
        {
            m_connectionDialog->setConnectionType(ConnectionDialog::Serial);
        }
        else if (type == "ADB")
        {
            m_connectionDialog->setConnectionType(ConnectionDialog::ADB);
        }
        else if (type == "TCP")
        {
            m_connectionDialog->setConnectionType(ConnectionDialog::TCP);
        }

        // 设置连接参数
        m_connectionDialog->setConnectionParams(params);
    }

    m_connectionDialog->show();
    m_connectionDialog->raise();
    m_connectionDialog->activateWindow();

    // 更新最后使用时间
    item->setData(Qt::UserRole + 2, QDateTime::currentDateTime());
    saveConnectionHistory();
}

void MainWindow::onHistoryContextMenu(const QPoint &pos)
{
    QListWidgetItem *item = m_historyList->itemAt(pos);
    if (!item)
        return;

    QMenu contextMenu(this);

    // 设置菜单样式
    contextMenu.setStyleSheet(
        "QMenu {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #ffffff, stop:1 #f8f9fa);"
        "    border: 2px solid #e9ecef;"
        "    border-radius: 8px;"
        "    padding: 4px;"
        "}"
        "QMenu::item {"
        "    background: transparent;"
        "    padding: 8px 16px;"
        "    border-radius: 4px;"
        "    color: #495057;"
        "    font-size: 12px;"
        "}"
        "QMenu::item:selected {"
        "    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,"
        "        stop:0 #2196f3, stop:1 #1976d2);"
        "    color: white;"
        "}"
        "QMenu::separator {"
        "    height: 1px;"
        "    background: #e9ecef;"
        "    margin: 4px 8px;"
        "}");

    // 创建菜单项
    QAction *connectAction = contextMenu.addAction("🔗 连接");
    QAction *renameAction = contextMenu.addAction("✏️ 重命名");
    QAction *editAction = contextMenu.addAction("⚙️ 修改配置");
    contextMenu.addSeparator();
    QAction *deleteAction = contextMenu.addAction("🗑️ 删除");

    // 设置工具提示
    connectAction->setToolTip("双击也可以直接连接");
    renameAction->setToolTip("修改连接的显示名称");
    editAction->setToolTip("修改连接的配置参数");
    deleteAction->setToolTip("从历史记录中删除此连接");

    // 显示菜单并处理选择
    QAction *selectedAction = contextMenu.exec(m_historyList->mapToGlobal(pos));

    if (selectedAction == connectAction)
    {
        // 连接到选中的历史项
        onHistoryItemDoubleClicked(item);
    }
    else if (selectedAction == renameAction)
    {
        // 重命名历史项
        renameHistoryItem(item);
    }
    else if (selectedAction == editAction)
    {
        // 编辑历史项配置
        editHistoryItem(item);
    }
    else if (selectedAction == deleteAction)
    {
        // 删除历史项
        deleteHistoryItem(item);
    }
}

void MainWindow::connectToSerial(const QString &config)
{
    if (!m_connectionManager)
        return;

    // 解析串口配置：COM3,115200,8,1,无
    QStringList parts = config.split(',');
    if (parts.size() < 2)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("❌ 串口配置格式错误\n");
        }
        return;
    }

    QVariantMap params;
    params["type"] = "serial"; // 指定连接类型
    params["port"] = parts[0].trimmed();
    // 使用与ConnectionDialog一致的参数名
    params["baud_rate"] = parts[1].trimmed().toInt();
    if (parts.size() > 2)
        params["data_bits"] = parts[2].trimmed().toInt();
    if (parts.size() > 3)
        params["stop_bits"] = parts[3].trimmed();
    if (parts.size() > 4)
        params["parity"] = parts[4].trimmed();

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("🔌 连接串口: %1 @ %2\n").arg(parts[0], parts[1]));
    }

    // 保存连接参数，用于状态栏显示
    m_connectionParams = params;
    m_connectionType = "串口";

    m_connectionManager->connectToDevice(params);
}

void MainWindow::connectToTCP(const QString &config)
{
    if (!m_connectionManager)
        return;

    // 解析TCP配置：*************:8080
    QStringList parts = config.split(':');
    if (parts.size() != 2)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("❌ TCP配置格式错误\n");
        }
        return;
    }

    QVariantMap params;
    params["type"] = "tcp"; // 指定连接类型
    params["host"] = parts[0].trimmed();
    params["port"] = parts[1].trimmed().toInt();

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("🌐 连接TCP: %1:%2\n").arg(parts[0], parts[1]));
    }

    // 保存连接参数，用于状态栏显示
    m_connectionParams = params;
    m_connectionType = "TCP";

    m_connectionManager->connectToDevice(params);
}

void MainWindow::connectToADB(const QString &deviceId)
{
    if (!m_connectionManager)
        return;

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage("📱 连接ADB设备...\n");
        m_professionalTerminal->appendMessage("🔍 检查ADB设备连接状态...\n");

        // 先检查是否有ADB设备连接
        QProcess adbCheck;
        adbCheck.start("adb", QStringList() << "devices");
        if (adbCheck.waitForFinished(3000))
        {
            QString output = adbCheck.readAllStandardOutput();
            QStringList lines = output.split('\n', QString::SkipEmptyParts);

            int deviceCount = 0;
            for (const QString &line : lines)
            {
                if (line.contains('\t') && !line.startsWith("List of devices"))
                {
                    deviceCount++;
                    m_professionalTerminal->appendMessage(QString("📱 发现设备: %1\n").arg(line.trimmed()));
                }
            }

            if (deviceCount == 0)
            {
                m_professionalTerminal->appendMessage("⚠️ 未发现ADB设备，请确保：\n");
                m_professionalTerminal->appendMessage("   1. 设备已连接并开启USB调试\n");
                m_professionalTerminal->appendMessage("   2. 已授权此计算机进行调试\n");
                m_professionalTerminal->appendMessage("   3. ADB驱动已正确安装\n");
            }
            else
            {
                m_professionalTerminal->appendMessage(QString("✅ 发现 %1 个ADB设备\n").arg(deviceCount));
            }
        }
        else
        {
            m_professionalTerminal->appendMessage("❌ 无法执行ADB命令，请检查ADB是否已安装\n");
        }
    }

    QVariantMap params;
    params["type"] = "adb"; // 指定连接类型
    if (!deviceId.isEmpty())
    {
        params["device"] = deviceId; // 指定设备ID
    }

    // 保存连接参数，用于状态栏显示
    m_connectionParams = params;
    m_connectionType = "ADB";

    qDebug() << "MainWindow::connectToADB() - 开始ADB连接，参数:" << params;
    bool result = m_connectionManager->connectToDevice(params);
    qDebug() << "MainWindow::connectToADB() - 连接启动结果:" << result;

    if (!result)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("❌ ADB连接启动失败\n");
        }
    }
}

void MainWindow::connectToDeviceByType(const QString &type, const QString &config)
{
    qDebug() << "MainWindow::connectToDeviceByType() - 类型:" << type << "配置:" << config;

    // 根据连接类型进行连接
    if (type == "串口")
    {
        connectToSerial(config);
    }
    else if (type == "TCP")
    {
        connectToTCP(config);
    }
    else if (type == "ADB")
    {
        connectToADB(config); // 传递设备ID
    }
    else
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("❌ 不支持的连接类型: %1\n").arg(type));
        }
    }
}

void MainWindow::renameHistoryItem(QListWidgetItem *item)
{
    if (!item)
        return;

    QString currentName = item->text().split('\n').first();
    // 移除图标前缀
    if (currentName.contains(' '))
    {
        currentName = currentName.split(' ', QString::SkipEmptyParts).last();
    }

    bool ok;
    QString newName = QInputDialog::getText(this, "重命名连接",
                                            "请输入新的连接名称:",
                                            QLineEdit::Normal,
                                            currentName, &ok);

    if (ok && !newName.isEmpty())
    {
        QString type = item->data(Qt::UserRole).toString();
        QString config = item->data(Qt::UserRole + 1).toString();
        QDateTime lastUsed = item->data(Qt::UserRole + 2).toDateTime();

        // 根据连接类型选择图标
        QString icon;
        QString formattedConfig;
        if (type == "串口")
        {
            icon = "🔌";
            QStringList parts = config.split(',');
            if (parts.size() >= 2)
            {
                formattedConfig = QString("%1, %2").arg(parts[0], parts[1]);
            }
            else
            {
                formattedConfig = config;
            }
        }
        else if (type == "TCP")
        {
            icon = "🌐";
            formattedConfig = config;
        }
        else if (type == "ADB")
        {
            icon = "📱";
            formattedConfig = "设备已连接";
        }
        else
        {
            icon = "🔗";
            formattedConfig = config;
        }

        QString displayText = QString("%1 %2\n📅 %3\n⚙️ %4")
                                  .arg(icon, newName)
                                  .arg(lastUsed.toString("MM-dd hh:mm"))
                                  .arg(formattedConfig);

        item->setText(displayText);
        item->setToolTip(QString("%1 类型: %2\n⚙️ 配置: %3\n📅 最后使用: %4")
                             .arg(icon, type, config, lastUsed.toString("yyyy-MM-dd hh:mm:ss")));

        saveConnectionHistory();

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("✏️ 连接已重命名为: %1\n").arg(newName));
        }
    }
}

void MainWindow::editHistoryItem(QListWidgetItem *item)
{
    if (!item)
        return;

    QString type = item->data(Qt::UserRole).toString();
    QString config = item->data(Qt::UserRole + 1).toString();

    // 使用专门的配置对话框
    ConnectionConfigDialog dialog(type, config, this);

    if (dialog.exec() == QDialog::Accepted)
    {
        QString newConfig = dialog.getConfig();

        if (!newConfig.isEmpty() && newConfig != config)
        {
            // 更新配置
            item->setData(Qt::UserRole + 1, newConfig);

            // 更新显示文本
            QString currentName = item->text().split('\n').first();
            QDateTime lastUsed = item->data(Qt::UserRole + 2).toDateTime();

            QString icon;
            QString formattedConfig;
            if (type == "串口")
            {
                icon = "🔌";
                QStringList parts = newConfig.split(',');
                if (parts.size() >= 2)
                {
                    formattedConfig = QString("%1, %2").arg(parts[0], parts[1]);
                }
                else
                {
                    formattedConfig = newConfig;
                }
            }
            else if (type == "TCP")
            {
                icon = "🌐";
                formattedConfig = newConfig;
            }
            else
            {
                icon = "🔗";
                formattedConfig = newConfig;
            }

            QString displayText = QString("%1\n📅 %2\n⚙️ %3")
                                      .arg(currentName)
                                      .arg(lastUsed.toString("MM-dd hh:mm"))
                                      .arg(formattedConfig);

            item->setText(displayText);
            item->setToolTip(QString("%1 类型: %2\n⚙️ 配置: %3\n📅 最后使用: %4")
                                 .arg(icon, type, newConfig, lastUsed.toString("yyyy-MM-dd hh:mm:ss")));

            saveConnectionHistory();

            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage(QString("⚙️ 配置已更新: %1\n").arg(newConfig));
            }
        }
    }
}

void MainWindow::deleteHistoryItem(QListWidgetItem *item)
{
    if (!item)
        return;

    QString itemName = item->text().split('\n').first();

    QMessageBox::StandardButton reply = QMessageBox::question(this,
                                                              "删除确认",
                                                              QString("确定要删除历史连接 \"%1\" 吗？").arg(itemName),
                                                              QMessageBox::Yes | QMessageBox::No,
                                                              QMessageBox::No);

    if (reply == QMessageBox::Yes)
    {
        int row = m_historyList->row(item);
        delete m_historyList->takeItem(row);

        saveConnectionHistory();

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("🗑️ 已删除历史连接: %1\n").arg(itemName));
        }
    }
}

void MainWindow::saveCurrentConnectionToHistory()
{
    qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 开始保存历史记录";

    if (!m_connectionManager)
    {
        qDebug() << "MainWindow::saveCurrentConnectionToHistory() - ConnectionManager为空";
        return;
    }

    try
    {
        // 使用本地缓存的连接参数，避免调用可能产生死锁的函数
        if (m_connectionParams.isEmpty())
        {
            qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 连接参数为空";
            return;
        }

        QString connectionType = m_connectionType;
        QString connectionConfig = "";

        // 根据连接类型生成配置字符串
        QString typeStr = m_connectionParams.value("type", "").toString().toLower();

        if (typeStr == "serial" || typeStr == "串口")
        {
            connectionType = "串口";
            connectionConfig = QString("%1,%2,%3,%4,%5")
                                   .arg(m_connectionParams.value("port").toString())
                                   .arg(m_connectionParams.value("baud_rate", m_connectionParams.value("baudRate", 115200)).toInt())
                                   .arg(m_connectionParams.value("data_bits", m_connectionParams.value("dataBits", 8)).toInt())
                                   .arg(m_connectionParams.value("stop_bits", m_connectionParams.value("stopBits", 1)).toString())
                                   .arg(m_connectionParams.value("parity", "无").toString());
        }
        else if (typeStr == "tcp" || typeStr == "网络")
        {
            connectionType = "TCP";
            connectionConfig = QString("%1:%2")
                                   .arg(m_connectionParams.value("host").toString())
                                   .arg(m_connectionParams.value("port").toInt());
        }
        else if (typeStr == "adb")
        {
            connectionType = "ADB";
            connectionConfig = m_connectionParams.value("device").toString();
        }

        if (!connectionType.isEmpty() && !connectionConfig.isEmpty())
        {
            qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 准备添加历史记录:" << connectionType << connectionConfig;
            addConnectionToHistory(connectionType, connectionConfig);
            qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 成功保存历史记录:" << connectionType << connectionConfig;
        }
        else
        {
            qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 连接类型或配置为空:" << connectionType << connectionConfig;
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 未知异常";
    }

    qDebug() << "MainWindow::saveCurrentConnectionToHistory() - 完成";
}

void MainWindow::updateHistoryConnectionStatus()
{
    if (!m_historyList)
        return;

    // 恢复简单的全量更新机制
    // 重置所有项目的样式
    for (int i = 0; i < m_historyList->count(); ++i)
    {
        QListWidgetItem *item = m_historyList->item(i);
        if (item)
        {
            // 恢复默认样式
            item->setData(Qt::UserRole + 10, false); // 清除连接状态标记
            item->setBackground(QBrush(QColor("#ffffff")));
            item->setForeground(QBrush(QColor("#333333")));

            // 移除特殊样式
            QString originalText = item->text();
            if (originalText.startsWith("🟢 "))
            {
                item->setText(originalText.mid(3)); // 移除绿色圆点
            }
        }
    }

    // 如果有连接，高亮当前连接项
    if (m_isConnected && m_currentConnectionItem)
    {
        // 设置当前连接项的明显高亮样式
        m_currentConnectionItem->setData(Qt::UserRole + 10, true); // 设置连接状态标记

        // 使用绿色渐变背景表示当前连接
        QLinearGradient gradient(0, 0, 1, 0);
        gradient.setColorAt(0, QColor("#4CAF50")); // 绿色
        gradient.setColorAt(1, QColor("#66BB6A")); // 浅绿色
        m_currentConnectionItem->setBackground(QBrush(gradient));
        m_currentConnectionItem->setForeground(QBrush(QColor("#ffffff"))); // 白色文字

        // 在文本前添加绿色圆点表示连接状态
        QString currentText = m_currentConnectionItem->text();
        if (!currentText.startsWith("🟢 "))
        {
            m_currentConnectionItem->setText("🟢 " + currentText);
        }

        qDebug() << "MainWindow::updateHistoryConnectionStatus() - 高亮当前连接项:" << m_currentConnectionItem->text();
    }

    m_currentConnectionItem = nullptr; // 重置当前连接项
}

void MainWindow::findAndHighlightCurrentConnection()
{
    if (!m_historyList || !m_isConnected)
        return;

    // 构建当前连接的匹配字符串
    QString currentConnectionText;
    QString typeStr = m_connectionParams.value("type").toString();

    if (typeStr == "serial" || typeStr == "串口")
    {
        QString port = m_connectionParams.value("port").toString();
        int baudRate = m_connectionParams.value("baud_rate", m_connectionParams.value("baudRate", 115200)).toInt();
        currentConnectionText = QString("● %1  %2").arg(port, QString::number(baudRate));
    }
    else if (typeStr == "tcp" || typeStr == "TCP")
    {
        QString host = m_connectionParams.value("host").toString();
        int port = m_connectionParams.value("port").toInt();
        currentConnectionText = QString("● %1:%2").arg(host, QString::number(port));
    }
    else if (typeStr == "adb" || typeStr == "ADB")
    {
        QString deviceId = m_connectionParams.value("device", "").toString();
        if (!deviceId.isEmpty())
        {
            currentConnectionText = QString("● ADB  %1").arg(deviceId);
        }
        else
        {
            currentConnectionText = "● ADB设备";
        }
    }

    // 查找匹配的历史项
    for (int i = 0; i < m_historyList->count(); ++i)
    {
        QListWidgetItem *item = m_historyList->item(i);
        if (item && item->text().contains(currentConnectionText.split("  ")[0])) // 模糊匹配
        {
            // 找到匹配项，设置为当前连接项
            m_currentConnectionItem = item;

            // 设置明显的高亮样式
            QLinearGradient gradient(0, 0, 1, 0);
            gradient.setColorAt(0, QColor("#4CAF50")); // 绿色
            gradient.setColorAt(1, QColor("#66BB6A")); // 浅绿色
            item->setBackground(QBrush(gradient));
            item->setForeground(QBrush(QColor("#ffffff"))); // 白色文字

            // 在文本前添加绿色圆点表示连接状态
            QString currentText = item->text();
            if (!currentText.startsWith("🟢 "))
            {
                item->setText("🟢 " + currentText);
            }

            qDebug() << "MainWindow::findAndHighlightCurrentConnection() - 找到并高亮连接项:" << item->text();
            break;
        }
    }
}

// 新增的快捷功能实现
void MainWindow::quickConnect()
{
    qDebug() << "MainWindow::quickConnect() - 快速连接";
    showConnectionDialog();
}

void MainWindow::quickDisconnect()
{
    qDebug() << "MainWindow::quickDisconnect() - 快速断开";
    disconnectDevice();
}

void MainWindow::quickClear()
{
    qDebug() << "MainWindow::quickClear() - 快速清空";
    if (m_professionalTerminal)
    {
        m_professionalTerminal->clear();
        m_professionalTerminal->appendMessage("🗑️ 终端已清空\n");
    }
    if (m_terminal)
    {
        m_terminal->clear();
        m_terminal->appendMessage("🗑️ 终端已清空", LogManager::Info);
    }
}

void MainWindow::quickPause()
{
    qDebug() << "MainWindow::quickPause() - 快速暂停/恢复";

    static bool isPaused = false;
    isPaused = !isPaused;

    if (isPaused)
    {
        // 暂停数据处理
        m_processingRawData = false;
        m_quickPauseBtn->setText("恢复");
        m_quickPauseBtn->setToolTip("点击恢复数据接收显示");

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("⏸️ 数据接收已暂停\n");
        }
        if (m_terminal)
        {
            m_terminal->appendMessage("⏸️ 数据接收已暂停", LogManager::Warning);
        }
    }
    else
    {
        // 恢复数据处理
        m_processingRawData = true;
        m_quickPauseBtn->setText("暂停");
        m_quickPauseBtn->setToolTip("点击暂停数据接收显示");

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("▶️ 数据接收已恢复\n");
        }
        if (m_terminal)
        {
            m_terminal->appendMessage("▶️ 数据接收已恢复", LogManager::Info);
        }
    }
}

void MainWindow::showFindDialog()
{
    qDebug() << "MainWindow::showFindDialog() - 显示查找对话框";

    if (!m_professionalTerminal)
    {
        QMessageBox::warning(this, "提示", "终端未初始化");
        return;
    }

    bool ok;
    QString searchText = QInputDialog::getText(this, "🔍 查找内容",
                                               "请输入要查找的文本:",
                                               QLineEdit::Normal, "", &ok);

    if (ok && !searchText.isEmpty())
    {
        // 调用终端的查找功能
        m_professionalTerminal->findText(searchText);

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("🔍 正在查找: %1\n").arg(searchText));
        }
    }
}

void MainWindow::toggleHighlight()
{
    qDebug() << "MainWindow::toggleHighlight() - 切换高亮功能";

    if (!m_professionalTerminal)
    {
        QMessageBox::warning(this, "提示", "终端未初始化");
        return;
    }

    if (m_highlightBtn->isChecked())
    {
        // 启用高亮功能
        bool ok;
        QString highlightText = QInputDialog::getText(this, "🖍️ 高亮内容",
                                                      "请输入要高亮的文本:",
                                                      QLineEdit::Normal, "", &ok);

        if (ok && !highlightText.isEmpty())
        {
            // 这里可以实现高亮功能
            // 暂时显示提示信息
            m_professionalTerminal->appendMessage(QString("🖍️ 已启用高亮: %1\n").arg(highlightText));
            m_highlightBtn->setText("🖍️ 取消高亮");
            m_highlightBtn->setToolTip("点击取消高亮显示");
        }
        else
        {
            m_highlightBtn->setChecked(false);
        }
    }
    else
    {
        // 禁用高亮功能
        m_professionalTerminal->appendMessage("🖍️ 已取消高亮显示\n");
        m_highlightBtn->setText("🖍️ 高亮");
        m_highlightBtn->setToolTip("高亮显示指定内容");
    }
}

void MainWindow::showRemoteDialog()
{
    qDebug() << "MainWindow::showRemoteDialog() - 显示远程连接对话框";

    QDialog dialog(this);
    dialog.setWindowTitle("🌐 远程连接");
    dialog.setFixedSize(400, 300);

    QVBoxLayout *layout = new QVBoxLayout(&dialog);

    // 说明文本
    QLabel *infoLabel = new QLabel("远程连接到其他RF调试工具实例");
    infoLabel->setStyleSheet("font-weight: bold; color: #2c5aa0; margin-bottom: 10px;");
    layout->addWidget(infoLabel);

    // IP地址输入
    QHBoxLayout *ipLayout = new QHBoxLayout;
    ipLayout->addWidget(new QLabel("IP地址:"));
    QLineEdit *ipEdit = new QLineEdit;
    ipEdit->setPlaceholderText("例如: *************");
    ipLayout->addWidget(ipEdit);
    layout->addLayout(ipLayout);

    // 端口输入
    QHBoxLayout *portLayout = new QHBoxLayout;
    portLayout->addWidget(new QLabel("端口:"));
    QLineEdit *portEdit = new QLineEdit;
    portEdit->setPlaceholderText("例如: 8080");
    portEdit->setText("8080");
    portLayout->addWidget(portEdit);
    layout->addLayout(portLayout);

    // 连接模式
    QGroupBox *modeGroup = new QGroupBox("连接模式");
    QVBoxLayout *modeLayout = new QVBoxLayout(modeGroup);
    QRadioButton *viewOnlyRadio = new QRadioButton("仅查看模式");
    QRadioButton *controlRadio = new QRadioButton("远程控制模式");
    viewOnlyRadio->setChecked(true);
    modeLayout->addWidget(viewOnlyRadio);
    modeLayout->addWidget(controlRadio);
    layout->addWidget(modeGroup);

    // 功能说明
    QTextEdit *descEdit = new QTextEdit;
    descEdit->setMaximumHeight(80);
    descEdit->setReadOnly(true);
    descEdit->setText("功能说明:\n• 仅查看模式: 可以查看远程终端输出\n• 远程控制模式: 可以远程操作和发送命令");
    layout->addWidget(descEdit);

    // 服务器控制按钮
    QHBoxLayout *serverLayout = new QHBoxLayout;
    QPushButton *startServerBtn = new QPushButton("启动服务器");
    QPushButton *stopServerBtn = new QPushButton("停止服务器");
    serverLayout->addWidget(startServerBtn);
    serverLayout->addWidget(stopServerBtn);
    layout->addLayout(serverLayout);

    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    QPushButton *connectBtn = new QPushButton("连接");
    QPushButton *cancelBtn = new QPushButton("取消");
    buttonLayout->addStretch();
    buttonLayout->addWidget(connectBtn);
    buttonLayout->addWidget(cancelBtn);
    layout->addLayout(buttonLayout);

    // 服务器控制信号
    connect(startServerBtn, &QPushButton::clicked, [this, portEdit]()
            {
        int port = portEdit->text().toInt();
        if (port <= 0 || port > 65535)
        {
            port = 8080;
            portEdit->setText("8080");
        }
        startRemoteServer(port); });

    connect(stopServerBtn, &QPushButton::clicked, this, &MainWindow::stopRemoteServer);

    // 连接信号
    connect(connectBtn, &QPushButton::clicked, [&]()
            {
        QString ip = ipEdit->text().trimmed();
        QString port = portEdit->text().trimmed();

        if (ip.isEmpty() || port.isEmpty())
        {
            QMessageBox::warning(&dialog, "错误", "请输入完整的IP地址和端口");
            return;
        }

        bool portOk;
        int portNum = port.toInt(&portOk);
        if (!portOk || portNum <= 0 || portNum > 65535)
        {
            QMessageBox::warning(&dialog, "错误", "请输入有效的端口号 (1-65535)");
            return;
        }

        // 实际的远程连接逻辑
        bool controlMode = controlRadio->isChecked();
        connectToRemoteHost(ip, portNum, controlMode);

        dialog.accept(); });

    connect(cancelBtn, &QPushButton::clicked, &dialog, &QDialog::reject);

    dialog.exec();
}

// 远程连接功能实现
void MainWindow::startRemoteServer(int port)
{
    qDebug() << "MainWindow::startRemoteServer() - 启动远程服务器，端口:" << port;

    if (m_remoteServer && m_remoteServer->isListening())
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("⚠️ 远程服务器已在运行中\n");
        }
        return;
    }

    // 创建TCP服务器
    if (!m_remoteServer)
    {
        m_remoteServer = new QTcpServer(this);
        connect(m_remoteServer, &QTcpServer::newConnection, this, &MainWindow::onRemoteClientConnected);
    }

    // 启动服务器
    if (m_remoteServer->listen(QHostAddress::Any, port))
    {
        m_remoteServerEnabled = true;
        m_remoteServerPort = port;

        // 获取本机IP地址
        QString localIP = "127.0.0.1";
        foreach (const QNetworkInterface &interface, QNetworkInterface::allInterfaces())
        {
            if (interface.flags() & QNetworkInterface::IsUp &&
                interface.flags() & QNetworkInterface::IsRunning &&
                !(interface.flags() & QNetworkInterface::IsLoopBack))
            {
                foreach (const QNetworkAddressEntry &entry, interface.addressEntries())
                {
                    if (entry.ip().protocol() == QAbstractSocket::IPv4Protocol)
                    {
                        localIP = entry.ip().toString();
                        break;
                    }
                }
                if (localIP != "127.0.0.1")
                    break;
            }
        }

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("🌐 远程服务器已启动\n"));
            m_professionalTerminal->appendMessage(QString("📡 监听地址: %1:%2\n").arg(localIP).arg(port));
            m_professionalTerminal->appendMessage(QString("💡 其他用户可通过此地址连接到本工具\n"));
        }
    }
    else
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("❌ 远程服务器启动失败: %1\n").arg(m_remoteServer->errorString()));
        }
    }
}

void MainWindow::stopRemoteServer()
{
    qDebug() << "MainWindow::stopRemoteServer() - 停止远程服务器";

    if (m_remoteServer && m_remoteServer->isListening())
    {
        // 断开所有客户端
        for (QTcpSocket *client : m_remoteClients)
        {
            client->disconnectFromHost();
            client->deleteLater();
        }
        m_remoteClients.clear();

        m_remoteServer->close();
        m_remoteServerEnabled = false;

        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("🌐 远程服务器已停止\n");
        }
    }
}

void MainWindow::connectToRemoteHost(const QString &host, int port, bool controlMode)
{
    qDebug() << "MainWindow::connectToRemoteHost() - 连接到远程主机:" << host << ":" << port << "控制模式:" << controlMode;

    if (m_remoteClient && m_remoteClient->state() == QAbstractSocket::ConnectedState)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage("⚠️ 已连接到远程主机，请先断开当前连接\n");
        }
        return;
    }

    // 创建客户端连接
    if (!m_remoteClient)
    {
        m_remoteClient = new QTcpSocket(this);
        connect(m_remoteClient, &QTcpSocket::connected, this, [this]()
                {
            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage("🌐 已成功连接到远程主机\n");
            } });
        connect(m_remoteClient, &QTcpSocket::disconnected, this, [this]()
                {
            if (m_professionalTerminal)
            {
                m_professionalTerminal->appendMessage("🌐 已断开远程连接\n");
            } });
        connect(m_remoteClient, &QTcpSocket::readyRead, this, &MainWindow::onRemoteDataReceived);
        connect(m_remoteClient, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
                this, &MainWindow::onRemoteConnectionError);
    }

    // 连接到远程主机
    if (m_professionalTerminal)
    {
        QString mode = controlMode ? "远程控制" : "仅查看";
        m_professionalTerminal->appendMessage(QString("🌐 正在连接到远程主机: %1:%2 (%3模式)\n").arg(host).arg(port).arg(mode));
    }

    m_remoteClient->connectToHost(host, port);

    // 发送连接信息
    QTimer::singleShot(1000, this, [this, controlMode]()
                       {
        if (m_remoteClient && m_remoteClient->state() == QAbstractSocket::ConnectedState)
        {
            QJsonObject connectInfo;
            connectInfo["type"] = "connect";
            connectInfo["mode"] = controlMode ? "control" : "view";
            connectInfo["client"] = "RF调试工具";

            QJsonDocument doc(connectInfo);
            m_remoteClient->write(doc.toJson(QJsonDocument::Compact) + "\n");
        } });
}

void MainWindow::disconnectFromRemoteHost()
{
    qDebug() << "MainWindow::disconnectFromRemoteHost() - 断开远程连接";

    if (m_remoteClient)
    {
        m_remoteClient->disconnectFromHost();
        if (m_remoteClient->state() != QAbstractSocket::UnconnectedState)
        {
            m_remoteClient->waitForDisconnected(3000);
        }
    }
}

void MainWindow::sendRemoteMessage(const QString &message)
{
    if (m_remoteClient && m_remoteClient->state() == QAbstractSocket::ConnectedState)
    {
        QJsonObject msgObj;
        msgObj["type"] = "message";
        msgObj["content"] = message;
        msgObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

        QJsonDocument doc(msgObj);
        m_remoteClient->write(doc.toJson(QJsonDocument::Compact) + "\n");
    }
}

void MainWindow::broadcastToRemoteClients(const QString &message)
{
    if (m_remoteClients.isEmpty())
        return;

    QJsonObject msgObj;
    msgObj["type"] = "broadcast";
    msgObj["content"] = message;
    msgObj["timestamp"] = QDateTime::currentDateTime().toString(Qt::ISODate);

    QJsonDocument doc(msgObj);
    QByteArray data = doc.toJson(QJsonDocument::Compact) + "\n";

    for (QTcpSocket *client : m_remoteClients)
    {
        if (client->state() == QAbstractSocket::ConnectedState)
        {
            client->write(data);
        }
    }
}

// 远程连接槽函数实现
void MainWindow::onRemoteClientConnected()
{
    if (!m_remoteServer)
        return;

    QTcpSocket *clientSocket = m_remoteServer->nextPendingConnection();
    if (!clientSocket)
        return;

    m_remoteClients.append(clientSocket);

    // 连接客户端信号
    connect(clientSocket, &QTcpSocket::disconnected, this, &MainWindow::onRemoteClientDisconnected);
    connect(clientSocket, &QTcpSocket::readyRead, this, &MainWindow::onRemoteDataReceived);
    connect(clientSocket, QOverload<QAbstractSocket::SocketError>::of(&QAbstractSocket::error),
            this, &MainWindow::onRemoteConnectionError);

    QString clientAddress = clientSocket->peerAddress().toString();
    int clientPort = clientSocket->peerPort();

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("🌐 远程客户端已连接: %1:%2\n").arg(clientAddress).arg(clientPort));
        m_professionalTerminal->appendMessage(QString("👥 当前连接数: %1\n").arg(m_remoteClients.size()));
    }

    // 发送欢迎消息
    QJsonObject welcomeMsg;
    welcomeMsg["type"] = "welcome";
    welcomeMsg["server"] = "RF调试工具远程服务器";
    welcomeMsg["version"] = "2.0";

    QJsonDocument doc(welcomeMsg);
    clientSocket->write(doc.toJson(QJsonDocument::Compact) + "\n");
}

void MainWindow::onRemoteClientDisconnected()
{
    QTcpSocket *clientSocket = qobject_cast<QTcpSocket *>(sender());
    if (!clientSocket)
        return;

    QString clientAddress = clientSocket->peerAddress().toString();
    int clientPort = clientSocket->peerPort();

    m_remoteClients.removeAll(clientSocket);
    clientSocket->deleteLater();

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("🌐 远程客户端已断开: %1:%2\n").arg(clientAddress).arg(clientPort));
        m_professionalTerminal->appendMessage(QString("👥 当前连接数: %1\n").arg(m_remoteClients.size()));
    }
}

void MainWindow::onRemoteDataReceived()
{
    QTcpSocket *socket = qobject_cast<QTcpSocket *>(sender());
    if (!socket)
        return;

    QByteArray data = socket->readAll();
    QString message = QString::fromUtf8(data).trimmed();

    if (message.isEmpty())
        return;

    // 解析JSON消息
    QJsonParseError error;
    QJsonDocument doc = QJsonDocument::fromJson(message.toUtf8(), &error);

    if (error.error != QJsonParseError::NoError)
    {
        if (m_professionalTerminal)
        {
            m_professionalTerminal->appendMessage(QString("🌐 收到远程消息: %1\n").arg(message));
        }
        return;
    }

    QJsonObject obj = doc.object();
    QString type = obj["type"].toString();
    QString content = obj["content"].toString();

    if (type == "message")
    {
        if (m_professionalTerminal)
        {
            QString clientAddr = socket->peerAddress().toString();
            m_professionalTerminal->appendMessage(QString("🌐 [%1] %2\n").arg(clientAddr).arg(content));
        }
    }
    else if (type == "command" && obj["mode"].toString() == "control")
    {
        // 远程控制命令
        if (m_professionalTerminal)
        {
            QString clientAddr = socket->peerAddress().toString();
            m_professionalTerminal->appendMessage(QString("🎮 [远程控制-%1] 执行命令: %2\n").arg(clientAddr).arg(content));

            // 这里可以执行远程命令
            // 例如发送到当前连接的设备
            if (m_isConnected && m_connectionManager)
            {
                m_connectionManager->sendData(content.toUtf8());
            }
        }
    }
}

void MainWindow::onRemoteConnectionError(QAbstractSocket::SocketError /*error*/)
{
    QTcpSocket *socket = qobject_cast<QTcpSocket *>(sender());
    if (!socket)
        return;

    QString errorString = socket->errorString();

    if (m_professionalTerminal)
    {
        m_professionalTerminal->appendMessage(QString("❌ 远程连接错误: %1\n").arg(errorString));
    }
}
