#ifndef TERMINALCONFIGDIALOG_H
#define TERMINALCONFIGDIALOG_H

#include <QDialog>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QFormLayout>
#include <QGroupBox>
#include <QCheckBox>
#include <QComboBox>
#include <QSpinBox>
#include <QLineEdit>
#include <QPushButton>
#include <QLabel>
#include <QScrollArea>

/**
 * 终端配置对话框
 * 用于配置终端相关的设置，包括字体、时间戳、历史记录等
 */
class TerminalConfigDialog : public QDialog
{
    Q_OBJECT

public:
    explicit TerminalConfigDialog(QWidget *parent = nullptr);
    ~TerminalConfigDialog();

    void setConfig(const QVariantMap &config);
    QVariantMap getConfig() const;

signals:
    void configChanged(const QString &key, const QVariant &value);

private slots:
    void onOkClicked();
    void onCancelClicked();
    void onResetClicked();
    void onApplyClicked();

private:
    void setupUI();
    void loadConfig();
    void saveConfig();
    void updateUI();

private:
    // 主布局
    QVBoxLayout *m_mainLayout;
    QScrollArea *m_scrollArea;
    QWidget *m_scrollWidget;
    QVBoxLayout *m_scrollLayout;

    // 字体配置
    QGroupBox *m_fontGroup;
    QComboBox *m_fontFamilyCombo;
    QSpinBox *m_fontSizeSpin;
    QComboBox *m_fontWeightCombo;

    // 时间戳配置
    QGroupBox *m_timestampGroup;
    QCheckBox *m_timestampEnabledCheck;
    QComboBox *m_timestampFormatCombo;

    // 历史记录配置
    QGroupBox *m_historyGroup;
    QSpinBox *m_maxHistorySpin;
    QCheckBox *m_echoEnabledCheck;

    // 显示配置
    QGroupBox *m_displayGroup;
    QSpinBox *m_cursorWidthSpin;
    QCheckBox *m_wordWrapCheck;
    QComboBox *m_scrollBarPolicyCombo;

    // 按钮
    QPushButton *m_okBtn;
    QPushButton *m_cancelBtn;
    QPushButton *m_resetBtn;
    QPushButton *m_applyBtn;

    // 配置数据
    QVariantMap m_config;
};

#endif // TERMINALCONFIGDIALOG_H
