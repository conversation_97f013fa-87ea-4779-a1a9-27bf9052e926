# RF调试工具 Qt版本

一个功能强大的RF调试工具，基于Qt框架开发，支持多种连接方式和专业的终端功能。

## 主要功能

### 🔌 多种连接方式
- **串口连接**: 支持高波特率 (9600-3,000,000)，完整的串口参数配置
- **TCP连接**: TCP/UDP网络连接支持，适合设备调试
- **ADB连接**: Android设备调试，支持多设备管理

### 💻 专业终端
- 类似WindTerm的交互式终端体验
- 时间戳显示，可自定义格式
- 自动滚动和手动导航
- 支持复制、粘贴、查找功能
- 可调节字体大小和颜色主题
- 文本选择和搜索高亮

### 📝 命令管理
- 常用命令列表，支持分类管理
- 命令历史记录，智能搜索
- 快捷命令按钮，一键执行
- 命令导入导出功能

### 🎨 现代化界面
- 美观易用的界面设计
- 可自定义背景（纯色、渐变、图片）
- 支持透明度设置
- 响应式布局，支持窗口缩放
- 历史连接管理面板

### 📊 智能日志
- 多级别日志系统（调试、信息、警告、错误）
- 日志文件自动保存
- 时间戳和格式化输出
- 日志过滤和搜索

### ⚙️ 配置管理
- 完整的配置系统，支持导入导出
- 连接历史记录
- 窗口状态保存
- UTF-8编码配置文件

### 🌐 远程协作
- 支持IP地址远程访问
- 协作调试功能
- 实时数据共享

## 项目结构

```
RFTool_Qt/
├── src/                           # 源代码目录
│   ├── main.cpp                   # 程序入口
│   ├── MainWindow.h/.cpp          # 主窗口
│   ├── ConnectionManager.h/.cpp   # 连接管理器
│   ├── ConfigManager.h/.cpp       # 配置管理器
│   ├── LogManager.h/.cpp          # 日志管理器
│   ├── InteractiveTerminal.h/.cpp # 交互式终端
│   ├── ProfessionalTerminal.h/.cpp # 专业终端
│   ├── CommandHistory.h/.cpp      # 命令历史
│   ├── CommandListWidget.h/.cpp   # 命令列表组件
│   ├── SerialConnection.h/.cpp    # 串口连接
│   ├── TCPConnection.h/.cpp       # TCP连接
│   ├── ADBConnection.h/.cpp       # ADB连接
│   ├── ConnectionDialog.h/.cpp    # 连接对话框
│   ├── ConnectionConfigDialog.h/.cpp # 连接配置对话框
│   ├── LogConfigDialog.h/.cpp     # 日志配置对话框
│   ├── BackgroundConfigDialog.h/.cpp # 背景配置对话框
│   ├── RFTool_Qt.pro             # qmake项目文件
│   ├── RFTool_Qt_resource.rc     # Windows资源文件
│   ├── config.ini                # 配置文件
│   ├── 简单打包.bat               # 简单打包脚本
│   ├── qt_ide_package.bat        # 高级打包脚本
│   └── bin/                      # 编译输出目录
└── README.md                     # 项目说明
```

## 编译要求

### 依赖项
- Qt 5.12.9 或更高版本
- C++17 编译器
- Windows 7 或更高版本

### Qt模块
- Qt::Core
- Qt::Widgets
- Qt::SerialPort
- Qt::Network

## 编译方法

### 使用Qt Creator (推荐)
1. 打开 `RFTool_Qt.pro`
2. 选择合适的构建套件
3. 选择Release模式
4. 点击构建按钮

### 使用qmake命令行
```bash
qmake RFTool_Qt.pro
make
```

## 一键打包

项目提供了一个完整的打包脚本：

### 简单打包.bat
```bash
简单打包.bat
```
**功能特点：**
- 适合Qt IDE用户，无需命令行编译
- 自动检测编译好的exe文件
- 自动部署Qt依赖库
- 创建便携版和压缩包
- 支持多种压缩格式（7-Zip、PowerShell）
- 生成详细的用户手册和版本信息
- 质量检查和文件统计
- 交互式操作选择

**包含文件：**
- 主程序和所有依赖库
- 配置文件和资源文件
- 用户手册和版本信息
- 快速启动脚本
- 便携版目录
- 分发用压缩包

## 使用说明

### 快速开始
1. 在Qt Creator中编译项目到Release模式
2. 双击运行 `简单打包.bat` 生成可分发版本
3. 选择操作：打开目录、测试程序或退出
4. 启动程序，点击"连接设备"按钮
5. 选择连接类型并配置参数
6. 点击"连接"开始调试

### 连接配置
- **串口**: 选择端口、波特率等参数，支持手动输入
- **TCP**: 配置主机地址和端口
- **ADB**: 选择Android设备，自动检测可用设备

### 命令操作
- 在终端区域直接输入命令，按Enter发送
- 使用快捷命令按钮快速执行常用命令
- 在命令列表中管理和组织常用命令
- 使用上下箭头键浏览命令历史

### 界面定制
- 通过配置菜单设置背景和透明度
- 调整终端字体和颜色
- 配置日志级别和输出格式
- 管理历史连接

## 项目清理说明

项目已进行了彻底的代码清理和整理：

### 已删除的无用文件
- 测试文件: `simple_main.cpp`, `test_window.cpp`, `test_window.pro`, `compile_test.pro`
- CMake配置: `CMakeLists.txt` (用户使用Qt IDE和qmake)
- 生成文件: `Makefile*`, `object_script.*`
- 用户配置: `RFTool_Qt.pro.user`
- 构建目录: `build/`, `debug/`, `release/`

### 项目配置优化
- 优化了 `RFTool_Qt.pro` 文件
- 添加了Windows资源文件支持
- 配置了高DPI支持
- 设置了编译优化选项

## 版本信息

- **版本**: 2.0.0
- **开发语言**: C++17
- **UI框架**: Qt 5.12.9+
- **作者**: flex (<EMAIL>)
- **支持平台**: Windows 7+

## 技术支持

- **作者**: flex
- **邮箱**: <EMAIL>
- **版本**: 2.0.0

如有问题或建议，请联系技术支持。

---

**RF调试工具 Qt版本** - 专业的RF设备调试解决方案！
