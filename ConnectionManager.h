#ifndef CONNECTIONMANAGER_H
#define CONNECTIONMANAGER_H

#include <QObject>
#include <QVariantMap>
#include <QTimer>
#include <QThread>
#include <QMutex>
#include <QWaitCondition>

// 前向声明
class SerialConnection;
class TCPConnection;
class ADBConnection;
class NetworkConnection;
class FTPConnection;

class ConnectionManager : public QObject
{
    Q_OBJECT

public:
    enum ConnectionType
    {
        None,
        Serial,
        TCP,
        ADB,
        Network,
        FTP
    };
    Q_ENUM(ConnectionType)

    enum ConnectionStatus
    {
        Disconnected,
        Connecting,
        Connected,
        Disconnecting,
        Error
    };
    Q_ENUM(ConnectionStatus)

    explicit ConnectionManager(QObject *parent = nullptr);
    ~ConnectionManager();

    // 连接管理
    bool connectToDevice(const QVariantMap &params);
    void disconnectFromDevice();
    bool isConnected() const;
    ConnectionType currentConnectionType() const;
    ConnectionStatus currentStatus() const;
    QString currentStatusString() const;
    QVariantMap currentConnectionParams() const;

    // 数据传输
    bool sendCommand(const QString &command);
    bool sendData(const QByteArray &data);

    // 连接信息
    QString getConnectionInfo() const;
    QStringList getAvailablePorts() const;
    QStringList getAvailableADBDevices() const;

    // 配置管理
    void setConnectionTimeout(int seconds);
    int connectionTimeout() const;
    void setAutoReconnect(bool enabled);
    bool autoReconnectEnabled() const;

public slots:
    void reconnect();
    void testConnection(const QVariantMap &params);

signals:
    void statusChanged(const QString &status, const QString &details);
    void dataReceived(const QString &data);
    void rawDataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void connectionEstablished();
    void connectionLost();
    void testResult(bool success, const QString &message);

private slots:
    void onConnectionStatusChanged();
    void onDataReceived(const QByteArray &data);
    void onErrorOccurred(const QString &error);
    void onReconnectTimer();
    void onAdbConnected();
    void onAdbDisconnected();

private:
    void initializeConnections();
    void cleanupConnections();
    void setStatus(ConnectionStatus status, const QString &details = QString());
    bool createConnection(ConnectionType type, const QVariantMap &params);
    void destroyCurrentConnection();

    QString connectionTypeToString(ConnectionType type) const;
    ConnectionType stringToConnectionType(const QString &typeStr) const;
    QString statusToString(ConnectionStatus status) const;

private:
    // 连接对象
    SerialConnection *m_serialConnection;
    TCPConnection *m_tcpConnection;
    ADBConnection *m_adbConnection;
    NetworkConnection *m_networkConnection;
    FTPConnection *m_ftpConnection;

    // 当前连接状态
    ConnectionType m_currentType;
    ConnectionStatus m_currentStatus;
    QString m_statusDetails;
    QVariantMap m_connectionParams;

    // 配置参数
    int m_connectionTimeout;
    bool m_autoReconnectEnabled;

    // 重连机制
    QTimer *m_reconnectTimer;
    int m_reconnectAttempts;
    int m_maxReconnectAttempts;

    // 线程安全
    mutable QMutex m_mutex;
    QWaitCondition m_waitCondition;

    // 数据缓冲
    QByteArray m_dataBuffer;
    QString m_lineBuffer;
};

#endif // CONNECTIONMANAGER_H
