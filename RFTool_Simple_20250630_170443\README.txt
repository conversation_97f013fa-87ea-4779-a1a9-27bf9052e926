RF Debug Tool Qt Version - Simple Package
========================================

Package Time: 2025-06-30 17:04:43
Author: flex (<EMAIL>)

IMPORTANT NOTICE:
This is an ENHANCED SIMPLE package that includes common Qt libraries.
Most computers should be able to run this directly.

If the program still fails to start, please:
  1. Install Microsoft Visual C++ Redistributable, or
  2. Install Qt runtime libraries, or
  3. Use the full package created by package.bat

File Description:
  * RFTool_Qt.exe - Main program
  * Start.bat - Quick start script
  * config.ini - Configuration file (if exists)
  * *.dll - Runtime libraries (if copied)

How to use:
  1. Copy this entire folder to target computer
  2. Double-click RFTool_Qt.exe to run directly
  3. Or double-click "Start.bat" to run

Main Features:
  * Multiple connection types: ADB, Serial, TCP
  * Smart log management and storage
  * Remote collaboration debugging
  * Search and highlight functions
  * Professional terminal interface
  * Connection history management
  * Customizable background and transparency

System Requirements:
  * Windows 7 SP1 or higher
  * Qt runtime libraries (may need separate installation)
  * At least 50MB available disk space

Technical Support: <EMAIL>
