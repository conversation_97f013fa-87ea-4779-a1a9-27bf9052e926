#include "ADBConnection.h"
#include <QMutexLocker>
#include <QDebug>
#include <QStandardPaths>
#include <QDir>
#include <QFileInfo>
#include <QRegularExpression>
#include <QProcess>
#include <QTimer>
#include <QDateTime>
#include <QThread>

ADBConnection::ADBConnection(QObject *parent)
    : QObject(parent), m_adbProcess(nullptr), m_checkProcess(nullptr), m_adbPath("adb"), m_state(Disconnected), m_isConnected(false), m_checkState(NotChecking), m_connectionTimeout(30), m_autoReconnectEnabled(false), m_bytesReceived(0), m_bytesSent(0), m_commandCount(0), m_isExecutingCommand(false)
{
    // 尝试找到ADB路径
    QString foundPath = findAdbPath();
    if (!foundPath.isEmpty())
    {
        m_adbPath = foundPath;
    }

    // 创建ADB进程
    m_adbProcess = new QProcess(this);
    QObject::connect(m_adbProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                     this, &ADBConnection::onProcessFinished);
    QObject::connect(m_adbProcess, &QProcess::errorOccurred, this, &ADBConnection::onProcessError);
    QObject::connect(m_adbProcess, &QProcess::readyReadStandardOutput, this, &ADBConnection::onProcessReadyRead);
    QObject::connect(m_adbProcess, &QProcess::readyReadStandardError, this, &ADBConnection::onProcessReadyRead);

    // 创建检查进程
    m_checkProcess = new QProcess(this);
    m_checkProcess->setProcessChannelMode(QProcess::MergedChannels); // 合并标准输出和错误输出
    QObject::connect(m_checkProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                     this, &ADBConnection::onCheckProcessFinished);
    QObject::connect(m_checkProcess, &QProcess::errorOccurred, this, [this](QProcess::ProcessError error)
                     {
        if (m_state == Connecting)
        {
            QString errorString;
            switch (error)
            {
            case QProcess::FailedToStart:
                errorString = "无法启动ADB进程，请检查ADB路径配置";
                break;
            case QProcess::Crashed:
                errorString = "ADB检查进程崩溃";
                break;
            case QProcess::Timedout:
                errorString = "ADB检查进程超时";
                break;
            default:
                errorString = "ADB检查进程错误";
                break;
            }

            m_checkState = NotChecking;
            setState(Error);
            emit errorOccurred(errorString);
        } });

    // 创建定时器
    m_connectionTimer = new QTimer(this);
    m_connectionTimer->setSingleShot(true);
    QObject::connect(m_connectionTimer, &QTimer::timeout, this, &ADBConnection::onConnectionTimeout);

    m_deviceCheckTimer = new QTimer(this);
    m_deviceCheckTimer->setInterval(5000); // 每5秒检查一次设备状态
    QObject::connect(m_deviceCheckTimer, &QTimer::timeout, this, &ADBConnection::onDeviceCheckTimer);

    // 初始化设备信息
    m_deviceInfo.state = Unknown;
    m_deviceInfo.isEmulator = false;

    // 尝试预启动ADB服务器
    QTimer::singleShot(500, this, &ADBConnection::preStartAdbServer);
}

ADBConnection::~ADBConnection()
{
    disconnect();
}

bool ADBConnection::connect(const QVariantMap &params)
{
    qDebug() << "ADBConnection::connect() - 开始ADB连接，参数:" << params;
    QMutexLocker locker(&m_mutex);

    if (m_isConnected || m_state == Connecting)
    {
        qDebug() << "ADBConnection::connect() - 已连接或正在连接，返回false";
        return false;
    }

    // 解析连接参数
    m_deviceId = params.value("device", "").toString();
    QString specifiedAdbPath = params.value("adb_path", "").toString();

    // 如果没有指定ADB路径或者是默认的"adb"，尝试自动查找
    if (specifiedAdbPath.isEmpty() || specifiedAdbPath == "adb")
    {
        qDebug() << "ADBConnection::connect() - 未指定具体ADB路径，开始自动查找";
        QString foundPath = findAdbPath();
        if (!foundPath.isEmpty())
        {
            m_adbPath = foundPath;
            qDebug() << "ADBConnection::connect() - 自动找到ADB路径:" << m_adbPath;
        }
        else
        {
            qDebug() << "ADBConnection::connect() - 未找到ADB路径，使用默认值";
            m_adbPath = specifiedAdbPath.isEmpty() ? "adb" : specifiedAdbPath;
        }
    }
    else
    {
        m_adbPath = specifiedAdbPath;
        qDebug() << "ADBConnection::connect() - 使用指定的ADB路径:" << m_adbPath;
    }

    qDebug() << "ADBConnection::connect() - 设备ID:" << m_deviceId << "ADB路径:" << m_adbPath;

    qDebug() << "ADBConnection::connect() - 设置连接状态";
    setState(Connecting);
    m_connectionTimer->start(m_connectionTimeout * 1000);

    // 首先确保ADB服务器启动
    qDebug() << "ADBConnection::connect() - 启动ADB服务器";
    m_checkState = StartingAdbServer;

    // 确保之前的进程已经结束
    if (m_checkProcess->state() != QProcess::NotRunning)
    {
        qDebug() << "ADBConnection::connect() - 终止现有检查进程";
        m_checkProcess->kill();
        m_checkProcess->waitForFinished(1000); // 等待最多1秒
    }

    // 优化：先检查ADB服务器是否已经运行
    qDebug() << "ADBConnection::connect() - 检查ADB服务器状态:" << m_adbPath;

    // 先验证ADB路径是否有效
    QFileInfo adbFileInfo(m_adbPath);
    if (!adbFileInfo.exists() && m_adbPath != "adb" && m_adbPath != "adb.exe")
    {
        qDebug() << "ADBConnection::connect() - ADB文件不存在:" << m_adbPath;
        setState(Error);
        emit errorOccurred(QString("ADB文件不存在: %1").arg(m_adbPath));
        return false;
    }

    // 检查ADB服务器是否已经运行
    bool serverRunning = isAdbServerRunning();
    qDebug() << "ADBConnection::connect() - ADB服务器运行状态:" << serverRunning;

    if (!serverRunning)
    {
        qDebug() << "ADBConnection::connect() - ADB服务器未运行，启动服务器";

        // 只有在服务器未运行时才启动
        m_checkState = StartingAdbServer;
        m_checkProcess->start(m_adbPath, QStringList() << "start-server");

        // 检查进程是否成功启动
        if (!m_checkProcess->waitForStarted(3000))
        {
            qDebug() << "ADBConnection::connect() - ADB进程启动失败";
            setState(Error);
            emit errorOccurred("无法启动ADB进程，请检查ADB路径配置和权限");
            return false;
        }

        qDebug() << "ADBConnection::connect() - ADB start-server进程已启动，等待完成";
    }
    else
    {
        qDebug() << "ADBConnection::connect() - ADB服务器已运行，直接检查设备连接";

        // 服务器已运行，直接跳到设备检查
        m_checkState = CheckingDeviceConnected;
        QTimer::singleShot(100, this, [this]()
                           { m_checkProcess->start(m_adbPath, QStringList() << "devices"); });
    }

    qDebug() << "ADBConnection::connect() - ADB连接启动完成，等待异步结果";
    // 连接过程现在是异步的，通过onCheckProcessFinished处理结果
    return true;
}

void ADBConnection::disconnect()
{
    qDebug() << "ADBConnection::disconnect() - 开始断开ADB连接";

    bool shouldEmitDisconnected = false;

    {
        QMutexLocker locker(&m_mutex);

        if (!m_isConnected && m_state == Disconnected)
        {
            qDebug() << "ADBConnection::disconnect() - 已经断开，直接返回";
            return;
        }

        qDebug() << "ADBConnection::disconnect() - 执行清理";
        cleanup();

        setState(Disconnected);
        m_isConnected = false;
        shouldEmitDisconnected = true;
    } // 释放互斥锁

    // 在锁外发出信号
    if (shouldEmitDisconnected)
    {
        qDebug() << "ADBConnection::disconnect() - 发出disconnected信号";
        emit disconnected();
        qDebug() << "ADBConnection::disconnect() - disconnected信号发出完成";
    }

    qDebug() << "ADBConnection::disconnect() - 断开完成";
}

bool ADBConnection::isConnected() const
{
    QMutexLocker locker(&m_mutex);
    return m_isConnected && m_deviceInfo.state == Device;
}

ADBConnection::ConnectionState ADBConnection::state() const
{
    QMutexLocker locker(&m_mutex);
    return m_state;
}

QString ADBConnection::stateString() const
{
    switch (state())
    {
    case Disconnected:
        return "未连接";
    case Connecting:
        return "连接中";
    case Connected:
        return "已连接";
    case Error:
        return "错误";
    default:
        return "未知状态";
    }
}

bool ADBConnection::sendCommand(const QString &command)
{
    return executeShellCommand(command);
}

bool ADBConnection::executeShellCommand(const QString &command)
{
    qDebug() << "ADBConnection::executeShellCommand() - 开始执行命令:" << command;
    qDebug() << "ADBConnection::executeShellCommand() - 连接状态: m_isConnected=" << m_isConnected << " m_state=" << m_state;

    // 避免调用isConnected()导致死锁，直接检查内部状态
    if (!m_isConnected || m_state != Connected)
    {
        qDebug() << "ADBConnection::executeShellCommand() - 设备未连接";
        return false;
    }

    // 检查交互式shell是否运行
    if (!m_adbProcess)
    {
        qDebug() << "ADBConnection::executeShellCommand() - m_adbProcess为空";
        return false;
    }

    QProcess::ProcessState processState = m_adbProcess->state();
    qDebug() << "ADBConnection::executeShellCommand() - 进程状态:" << processState;

    if (processState != QProcess::Running)
    {
        qDebug() << "ADBConnection::executeShellCommand() - 交互式shell未运行，状态:" << processState;
        return false;
    }

    qDebug() << "ADBConnection::executeShellCommand() - 发送命令到交互式shell:" << command;
    QByteArray data = command.toUtf8();
    qDebug() << "ADBConnection::executeShellCommand() - 数据大小:" << data.size() << "内容hex:" << data.toHex();

    qint64 written = m_adbProcess->write(data);
    qDebug() << "ADBConnection::executeShellCommand() - 写入结果:" << written << "期望:" << data.size();

    if (written > 0)
    {
        m_bytesSent += written;
        qDebug() << "ADBConnection::executeShellCommand() - 命令发送成功，字节数:" << written;
        return true;
    }
    else
    {
        qDebug() << "ADBConnection::executeShellCommand() - 写入失败，错误:" << m_adbProcess->errorString();
        return false;
    }
}

bool ADBConnection::executeAdbCommand(const QStringList &args)
{
    QMutexLocker locker(&m_mutex);

    if (m_isExecutingCommand)
    {
        m_commandQueue.append(args.join(" "));
        return true;
    }

    m_isExecutingCommand = true;
    m_currentCommand = args.join(" ");

    bool success = startAdbProcess(args);
    if (success)
    {
        m_commandCount++;
    }
    else
    {
        m_isExecutingCommand = false;
    }

    return success;
}

void ADBConnection::setDevice(const QString &deviceId)
{
    QMutexLocker locker(&m_mutex);

    if (m_deviceId != deviceId)
    {
        bool wasConnected = m_isConnected;

        if (wasConnected)
        {
            disconnect();
        }

        m_deviceId = deviceId;

        if (wasConnected)
        {
            // 重新连接到新设备
            QVariantMap params;
            params["device"] = deviceId;
            connect(params);
        }
    }
}

QString ADBConnection::device() const
{
    QMutexLocker locker(&m_mutex);
    return m_deviceId;
}

ADBConnection::DeviceInfo ADBConnection::deviceInfo() const
{
    QMutexLocker locker(&m_mutex);
    return m_deviceInfo;
}

QStringList ADBConnection::getAvailableDevices()
{
    QStringList devices;

    // 静态函数中需要找到ADB路径
    QString adbPath = findAdbPath();
    if (adbPath.isEmpty())
    {
        adbPath = "adb"; // 使用默认路径
    }

    QProcess process;
    process.start(adbPath, QStringList() << "devices");
    if (process.waitForFinished(2000)) // 减少超时时间到2秒
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.contains('\t') && !line.startsWith("List of devices"))
            {
                QStringList parts = line.split('\t');
                if (parts.size() >= 2 && parts[1].trimmed() == "device")
                {
                    QString deviceId = parts[0].trimmed();
                    if (!deviceId.isEmpty())
                    {
                        devices.append(deviceId);
                    }
                }
            }
        }
    }

    return devices;
}

QList<ADBConnection::DeviceInfo> ADBConnection::getDeviceList()
{
    QList<DeviceInfo> deviceList;

    // 静态函数中需要找到ADB路径
    QString adbPath = findAdbPath();
    if (adbPath.isEmpty())
    {
        adbPath = "adb"; // 使用默认路径
    }

    QProcess process;
    process.start(adbPath, QStringList() << "devices" << "-l");
    if (process.waitForFinished(2000)) // 减少超时时间
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.contains('\t') && !line.startsWith("List of devices"))
            {
                DeviceInfo info;
                QStringList parts = line.split(QRegularExpression("\\s+"));
                if (parts.size() >= 2)
                {
                    info.serialNumber = parts[0];
                    info.state = parseDeviceState(parts[1]);

                    // 解析设备属性
                    for (int i = 2; i < parts.size(); ++i)
                    {
                        QString part = parts[i];
                        if (part.startsWith("model:"))
                        {
                            info.model = part.mid(6);
                        }
                        else if (part.startsWith("product:"))
                        {
                            info.product = part.mid(8);
                        }
                        else if (part.startsWith("device:"))
                        {
                            info.device = part.mid(7);
                        }
                    }

                    info.isEmulator = info.serialNumber.startsWith("emulator-");
                    deviceList.append(info);
                }
            }
        }
    }

    return deviceList;
}

void ADBConnection::setAdbPath(const QString &path)
{
    QMutexLocker locker(&m_mutex);
    m_adbPath = path;
}

QString ADBConnection::adbPath() const
{
    QMutexLocker locker(&m_mutex);
    return m_adbPath;
}

QString ADBConnection::findAdbPath()
{
    qDebug() << "ADBConnection::findAdbPath() - 开始查找ADB路径";
    qDebug() << "ADBConnection::findAdbPath() - 当前工作目录:" << QDir::currentPath();
    qDebug() << "ADBConnection::findAdbPath() - PATH环境变量:" << qgetenv("PATH");

    // 常见的ADB路径
    QStringList possiblePaths = {
        "adb",
        "adb.exe",
        "C:/Windows/system32/adb.exe", // 添加系统路径
        QDir::homePath() + "/Android/Sdk/platform-tools/adb",
        QDir::homePath() + "/Android/Sdk/platform-tools/adb.exe",
        QDir::homePath() + "/AppData/Local/Android/Sdk/platform-tools/adb.exe",
        QDir::homePath() + "/AppData/Local/Android/Sdk/platform-tools/adb",
        "C:/Users/" + qgetenv("USERNAME") + "/AppData/Local/Android/Sdk/platform-tools/adb.exe",
        "C:/Users/" + qgetenv("USERNAME") + "/AppData/Local/Android/Sdk/platform-tools/adb",
        "/usr/bin/adb",
        "/usr/local/bin/adb",
        "C:/Android/Sdk/platform-tools/adb.exe",
        "C:/Android/Sdk/platform-tools/adb",
        "C:/Android/platform-tools/adb.exe",
        "C:/Android/platform-tools/adb",
        "C:/Program Files/Android/Android Studio/bin/adb.exe",
        "C:/Program Files/Android/Android Studio/bin/adb",
        "C:/Program Files (x86)/Android/android-sdk/platform-tools/adb.exe",
        "C:/Program Files (x86)/Android/android-sdk/platform-tools/adb",
        "./platform-tools/adb.exe", // 相对于当前目录
        "./platform-tools/adb",
        "../platform-tools/adb.exe", // 相对于上级目录
        "../platform-tools/adb"};

    // 检查环境变量
    QString androidHome = qgetenv("ANDROID_HOME");
    qDebug() << "ADBConnection::findAdbPath() - ANDROID_HOME:" << androidHome;
    if (!androidHome.isEmpty())
    {
        possiblePaths.prepend(androidHome + "/platform-tools/adb");
        possiblePaths.prepend(androidHome + "/platform-tools/adb.exe");
    }

    QString androidSdkRoot = qgetenv("ANDROID_SDK_ROOT");
    qDebug() << "ADBConnection::findAdbPath() - ANDROID_SDK_ROOT:" << androidSdkRoot;
    if (!androidSdkRoot.isEmpty())
    {
        possiblePaths.prepend(androidSdkRoot + "/platform-tools/adb");
        possiblePaths.prepend(androidSdkRoot + "/platform-tools/adb.exe");
    }

    qDebug() << "ADBConnection::findAdbPath() - 检查路径列表:" << possiblePaths;

    for (const QString &path : possiblePaths)
    {
        qDebug() << "ADBConnection::findAdbPath() - 测试路径:" << path;
        QProcess process;
        process.start(path, QStringList() << "version");

        bool finished = process.waitForFinished(3000); // 增加超时时间到3秒
        int exitCode = process.exitCode();
        QString output = process.readAllStandardOutput();
        QString error = process.readAllStandardError();

        qDebug() << "ADBConnection::findAdbPath() - 进程完成:" << finished
                 << "退出码:" << exitCode
                 << "输出:" << output.left(50)
                 << "错误:" << error.left(50);

        if (finished && exitCode == 0)
        {
            qDebug() << "ADBConnection::findAdbPath() - 找到有效ADB路径:" << path;
            return path;
        }
    }

    qDebug() << "ADBConnection::findAdbPath() - 未找到有效的ADB路径";
    return QString();
}

bool ADBConnection::isAdbServerRunning()
{
    qDebug() << "ADBConnection::isAdbServerRunning() - 检查ADB服务器是否运行";

    // 检查ADB路径是否有效
    if (m_adbPath.isEmpty())
    {
        qDebug() << "ADBConnection::isAdbServerRunning() - ADB路径为空";
        return false;
    }

    // 使用get-state命令检查ADB服务器状态
    QProcess checkProcess;
    checkProcess.setProcessChannelMode(QProcess::MergedChannels);

    // 设置较短的超时时间，避免长时间等待
    checkProcess.start(m_adbPath, QStringList() << "get-state");

    // 等待最多1秒
    if (!checkProcess.waitForStarted(1000))
    {
        qDebug() << "ADBConnection::isAdbServerRunning() - 启动检查进程失败";
        return false;
    }

    // 等待最多2秒
    if (!checkProcess.waitForFinished(2000))
    {
        qDebug() << "ADBConnection::isAdbServerRunning() - 检查进程超时";
        checkProcess.kill();
        return false;
    }

    // 检查退出码
    int exitCode = checkProcess.exitCode();
    QString output = checkProcess.readAll();

    qDebug() << "ADBConnection::isAdbServerRunning() - 检查结果: 退出码=" << exitCode << "输出=" << output;

    // 如果退出码为0，或者输出包含"unknown"或"device"，说明服务器正在运行
    // (即使没有设备连接，get-state也会返回"unknown"状态)
    return (exitCode == 0 || output.contains("unknown") || output.contains("device"));
}

void ADBConnection::preStartAdbServer()
{
    qDebug() << "ADBConnection::preStartAdbServer() - 预启动ADB服务器";

    // 只有在未连接状态下才预启动
    if (m_state != Disconnected)
    {
        qDebug() << "ADBConnection::preStartAdbServer() - 当前状态不是断开，跳过预启动";
        return;
    }

    // 检查ADB路径是否有效
    if (m_adbPath.isEmpty())
    {
        qDebug() << "ADBConnection::preStartAdbServer() - ADB路径为空，跳过预启动";
        return;
    }

    // 先检查ADB服务器是否已经运行
    if (isAdbServerRunning())
    {
        qDebug() << "ADBConnection::preStartAdbServer() - ADB服务器已经运行，跳过预启动";
        return;
    }

    // 静默启动ADB服务器，不改变连接状态
    QProcess *preStartProcess = new QProcess(this);
    preStartProcess->setProcessChannelMode(QProcess::MergedChannels);

    QObject::connect(preStartProcess, QOverload<int, QProcess::ExitStatus>::of(&QProcess::finished),
                     [this, preStartProcess](int exitCode, QProcess::ExitStatus)
                     {
                         qDebug() << "ADBConnection::preStartAdbServer() - 预启动完成，退出码:" << exitCode;
                         preStartProcess->deleteLater();
                     });

    QObject::connect(preStartProcess, &QProcess::errorOccurred,
                     [this, preStartProcess](QProcess::ProcessError error)
                     {
                         qDebug() << "ADBConnection::preStartAdbServer() - 预启动失败:" << error;
                         preStartProcess->deleteLater();
                     });

    qDebug() << "ADBConnection::preStartAdbServer() - 执行 adb start-server";
    preStartProcess->start(m_adbPath, QStringList() << "start-server");
}

void ADBConnection::setConnectionTimeout(int seconds)
{
    m_connectionTimeout = qMax(1, seconds);
}

int ADBConnection::connectionTimeout() const
{
    return m_connectionTimeout;
}

void ADBConnection::setAutoReconnect(bool enabled)
{
    m_autoReconnectEnabled = enabled;
}

bool ADBConnection::autoReconnectEnabled() const
{
    return m_autoReconnectEnabled;
}

bool ADBConnection::pushFile(const QString &localPath, const QString &remotePath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "push" << localPath << remotePath;

    return executeAdbCommand(args);
}

bool ADBConnection::pullFile(const QString &remotePath, const QString &localPath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "pull" << remotePath << localPath;

    return executeAdbCommand(args);
}

bool ADBConnection::installApk(const QString &apkPath)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "install" << apkPath;

    return executeAdbCommand(args);
}

bool ADBConnection::uninstallPackage(const QString &packageName)
{
    if (!isConnected())
    {
        return false;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "uninstall" << packageName;

    return executeAdbCommand(args);
}

QString ADBConnection::getProperty(const QString &property)
{
    if (!isConnected())
    {
        return QString();
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "getprop" << property;

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(2000)) // 减少超时时间
    {
        return process.readAllStandardOutput().trimmed();
    }

    return QString();
}

QVariantMap ADBConnection::getSystemProperties()
{
    QVariantMap properties;

    if (!isConnected())
    {
        return properties;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "getprop";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(3000)) // 减少超时时间
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        QRegularExpression regex(R"(\[([^\]]+)\]:\s*\[([^\]]*)\])");
        for (const QString &line : lines)
        {
            QRegularExpressionMatch match = regex.match(line);
            if (match.hasMatch())
            {
                QString key = match.captured(1);
                QString value = match.captured(2);
                properties[key] = value;
            }
        }
    }

    return properties;
}

QStringList ADBConnection::getInstalledPackages()
{
    QStringList packages;

    if (!isConnected())
    {
        return packages;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "pm" << "list" << "packages";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(3000)) // 减少超时时间
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (const QString &line : lines)
        {
            if (line.startsWith("package:"))
            {
                packages.append(line.mid(8).trimmed());
            }
        }
    }

    return packages;
}

QStringList ADBConnection::getRunningProcesses()
{
    QStringList processes;

    if (!isConnected())
    {
        return processes;
    }

    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell" << "ps";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(2000)) // 减少超时时间
    {
        QString output = process.readAllStandardOutput();
        QStringList lines = output.split('\n');

        for (int i = 1; i < lines.size(); ++i)
        { // 跳过标题行
            QString line = lines[i].trimmed();
            if (!line.isEmpty())
            {
                processes.append(line);
            }
        }
    }

    return processes;
}

qint64 ADBConnection::bytesReceived() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesReceived;
}

qint64 ADBConnection::bytesSent() const
{
    QMutexLocker locker(&m_mutex);
    return m_bytesSent;
}

QDateTime ADBConnection::connectionTime() const
{
    QMutexLocker locker(&m_mutex);
    return m_connectionTime;
}

int ADBConnection::commandCount() const
{
    QMutexLocker locker(&m_mutex);
    return m_commandCount;
}

void ADBConnection::reconnect()
{
    disconnect();

    QVariantMap params;
    params["device"] = m_deviceId;
    params["adb_path"] = m_adbPath;

    connect(params);
}

void ADBConnection::refreshDeviceInfo()
{
    try
    {
        // 不调用isConnected()避免死锁，直接检查内部状态
        if (!m_isConnected || m_state != Connected)
        {
            qDebug() << "ADBConnection::refreshDeviceInfo() - 设备未连接，跳过";
            return;
        }

        qDebug() << "ADBConnection::refreshDeviceInfo() - 开始获取设备信息";

        // 获取设备基本信息
        m_deviceInfo.serialNumber = m_deviceId;
        m_deviceInfo.isEmulator = m_deviceId.startsWith("emulator-");
        m_deviceInfo.state = Device;

        // 异步获取其他信息，避免阻塞
        // 暂时设置默认值，后续可以通过其他方式异步获取
        m_deviceInfo.model = "Unknown";
        m_deviceInfo.product = "Unknown";
        m_deviceInfo.device = "Unknown";
        m_deviceInfo.androidVersion = "Unknown";
        m_deviceInfo.apiLevel = "Unknown";

        qDebug() << "ADBConnection::refreshDeviceInfo() - 设备信息设置完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "ADBConnection::refreshDeviceInfo() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ADBConnection::refreshDeviceInfo() - 未知异常";
    }
}

// 私有槽函数实现
void ADBConnection::onProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    Q_UNUSED(exitStatus)
    QMutexLocker locker(&m_mutex);

    QString output = m_adbProcess->readAllStandardOutput();
    QString error = m_adbProcess->readAllStandardError();

    if (!output.isEmpty())
    {
        m_bytesReceived += output.toUtf8().size();
        emit dataReceived(output.toUtf8());
    }

    if (!error.isEmpty())
    {
        emit errorOccurred(error);
    }

    emit commandFinished(exitCode, output);

    m_isExecutingCommand = false;

    // 处理命令队列
    if (!m_commandQueue.isEmpty())
    {
        QString nextCommand = m_commandQueue.takeFirst();
        QStringList args = nextCommand.split(' ');
        executeAdbCommand(args);
    }
}

void ADBConnection::onProcessError(QProcess::ProcessError error)
{
    QMutexLocker locker(&m_mutex);

    QString errorString;
    switch (error)
    {
    case QProcess::FailedToStart:
        errorString = "ADB进程启动失败";
        break;
    case QProcess::Crashed:
        errorString = "ADB进程崩溃";
        break;
    case QProcess::Timedout:
        errorString = "ADB进程超时";
        break;
    case QProcess::WriteError:
        errorString = "ADB进程写入错误";
        break;
    case QProcess::ReadError:
        errorString = "ADB进程读取错误";
        break;
    default:
        errorString = "ADB进程未知错误";
        break;
    }

    setState(Error);
    emit errorOccurred(formatError(errorString));

    m_isExecutingCommand = false;
}

void ADBConnection::onProcessReadyRead()
{
    processOutput();
}

void ADBConnection::onConnectionTimeout()
{
    QMutexLocker locker(&m_mutex);

    if (m_state == Connecting)
    {
        // 停止检查进程
        if (m_checkProcess && m_checkProcess->state() != QProcess::NotRunning)
        {
            m_checkProcess->kill();
        }
        m_checkState = NotChecking;

        setState(Error);
        emit errorOccurred("连接超时");
    }
}

void ADBConnection::onDeviceCheckTimer()
{
    if (m_isConnected)
    {
        // 检查设备是否仍然连接
        if (!checkDeviceConnected())
        {
            setState(Error);
            m_isConnected = false;
            emit errorOccurred("设备连接丢失");

            if (m_autoReconnectEnabled)
            {
                QTimer::singleShot(2000, this, &ADBConnection::reconnect);
            }
        }
    }
}

// 私有方法实现
void ADBConnection::setState(ConnectionState state)
{
    if (m_state != state)
    {
        m_state = state;
        emit stateChanged(state);
    }
}

bool ADBConnection::startAdbProcess(const QStringList &arguments)
{
    if (m_adbProcess->state() != QProcess::NotRunning)
    {
        return false;
    }

    m_adbProcess->start(m_adbPath, arguments);
    // 不等待进程启动，异步处理
    return true;
}

void ADBConnection::processOutput()
{
    QByteArray data = m_adbProcess->readAllStandardOutput();
    if (!data.isEmpty())
    {
        m_outputBuffer.append(data);
        m_bytesReceived += data.size();
        emit dataReceived(data);
    }

    QByteArray errorData = m_adbProcess->readAllStandardError();
    if (!errorData.isEmpty())
    {
        m_errorBuffer.append(errorData);
    }
}

bool ADBConnection::checkAdbAvailable()
{
    QProcess process;
    process.start(m_adbPath, QStringList() << "version");
    return process.waitForFinished(1000) && process.exitCode() == 0; // 减少超时时间到1秒
}

bool ADBConnection::checkDeviceConnected()
{
    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "get-state";

    QProcess process;
    process.start(m_adbPath, args);
    if (process.waitForFinished(2000)) // 减少超时时间
    {
        QString output = process.readAllStandardOutput().trimmed();
        return output == "device";
    }

    return false;
}

ADBConnection::DeviceInfo ADBConnection::parseDeviceInfo(const QString &deviceLine)
{
    DeviceInfo info;
    QStringList parts = deviceLine.split(QRegularExpression("\\s+"));

    if (parts.size() >= 2)
    {
        info.serialNumber = parts[0];
        info.state = parseDeviceState(parts[1]);
        info.isEmulator = info.serialNumber.startsWith("emulator-");

        // 解析额外属性
        for (int i = 2; i < parts.size(); ++i)
        {
            QString part = parts[i];
            if (part.startsWith("model:"))
            {
                info.model = part.mid(6);
            }
            else if (part.startsWith("product:"))
            {
                info.product = part.mid(8);
            }
            else if (part.startsWith("device:"))
            {
                info.device = part.mid(7);
            }
        }
    }

    return info;
}

ADBConnection::DeviceState ADBConnection::parseDeviceState(const QString &stateStr)
{
    QString state = stateStr.toLower();

    if (state == "device")
    {
        return Device;
    }
    else if (state == "offline")
    {
        return Offline;
    }
    else if (state == "unauthorized")
    {
        return Unauthorized;
    }
    else if (state == "no permissions")
    {
        return NoPermissions;
    }
    else if (state == "bootloader")
    {
        return Bootloader;
    }
    else if (state == "recovery")
    {
        return Recovery;
    }
    else
    {
        return Unknown;
    }
}

QString ADBConnection::formatError(const QString &error) const
{
    return QString("ADB连接错误 [%1]: %2").arg(m_deviceId.isEmpty() ? "未指定设备" : m_deviceId).arg(error);
}

bool ADBConnection::startInteractiveShell()
{
    if (m_adbProcess && m_adbProcess->state() == QProcess::Running)
    {
        qDebug() << "ADBConnection::startInteractiveShell() - Shell已经在运行";
        return true;
    }

    // 直接启动adb shell，简单直接
    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "shell";

    qDebug() << "ADBConnection::startInteractiveShell() - 启动adb shell:" << m_adbPath << args;

    // 设置进程环境，确保能找到adb
    QProcessEnvironment env = QProcessEnvironment::systemEnvironment();
    m_adbProcess->setProcessEnvironment(env);

    m_adbProcess->start(m_adbPath, args);

    // 等待进程启动
    if (!m_adbProcess->waitForStarted(5000))
    {
        qDebug() << "ADBConnection::startInteractiveShell() - 进程启动失败:" << m_adbProcess->errorString();
        return false;
    }

    qDebug() << "ADBConnection::startInteractiveShell() - 交互式shell启动成功，进程状态:" << m_adbProcess->state();
    return true;
}

void ADBConnection::cleanup()
{
    qDebug() << "ADBConnection::cleanup() - 开始清理";

    if (m_connectionTimer)
    {
        m_connectionTimer->stop();
    }

    if (m_deviceCheckTimer)
    {
        m_deviceCheckTimer->stop();
    }

    // 强制终止ADB进程，不等待
    if (m_adbProcess && m_adbProcess->state() != QProcess::NotRunning)
    {
        qDebug() << "ADBConnection::cleanup() - 终止ADB进程";
        m_adbProcess->kill();
        // 不等待进程结束，避免卡死
    }

    // 强制终止检查进程，不等待
    if (m_checkProcess && m_checkProcess->state() != QProcess::NotRunning)
    {
        qDebug() << "ADBConnection::cleanup() - 终止检查进程";
        m_checkProcess->kill();
        // 不等待进程结束，避免卡死
    }

    m_outputBuffer.clear();
    m_errorBuffer.clear();
    m_commandQueue.clear();
    m_isExecutingCommand = false;

    qDebug() << "ADBConnection::cleanup() - 清理完成";
}

void ADBConnection::onCheckProcessFinished(int exitCode, QProcess::ExitStatus exitStatus)
{
    try
    {
        qDebug() << "ADBConnection::onCheckProcessFinished() - 检查状态:" << m_checkState
                 << "退出码:" << exitCode << "状态:" << exitStatus;
        qDebug() << "ADBConnection::onCheckProcessFinished() - 状态枚举值: CheckingAdbAvailable=" << CheckingAdbAvailable
                 << " CheckingDeviceConnected=" << CheckingDeviceConnected
                 << " CheckingDeviceState=" << CheckingDeviceState;

        if (!m_checkProcess)
        {
            qDebug() << "ADBConnection::onCheckProcessFinished() - m_checkProcess为空";
            return;
        }

        QString output = m_checkProcess->readAllStandardOutput();
        QString error = m_checkProcess->readAllStandardError();
        qDebug() << "ADBConnection::onCheckProcessFinished() - 输出:" << output.left(100);
        qDebug() << "ADBConnection::onCheckProcessFinished() - 错误:" << error.left(100);

        if (m_checkState == StartingAdbServer)
        {
            qDebug() << "ADBConnection::onCheckProcessFinished() - ADB服务器启动完成";

            // 检查启动是否成功
            if (exitCode != 0 || exitStatus != QProcess::NormalExit)
            {
                qDebug() << "ADBConnection::onCheckProcessFinished() - ADB服务器启动失败，退出码:" << exitCode;

// 尝试使用cmd.exe启动ADB服务器（Windows特定解决方案）
#ifdef Q_OS_WIN
                qDebug() << "ADBConnection::onCheckProcessFinished() - 尝试使用cmd.exe启动ADB服务器";

                QProcess cmdProcess;
                QStringList cmdArgs;
                cmdArgs << "/c" << m_adbPath << "start-server";
                qDebug() << "执行命令: cmd.exe" << cmdArgs.join(" ");

                cmdProcess.start("cmd.exe", cmdArgs);
                if (cmdProcess.waitForStarted(2000) && cmdProcess.waitForFinished(8000))
                {
                    int exitCode = cmdProcess.exitCode();
                    QString output = cmdProcess.readAllStandardOutput();
                    QString error = cmdProcess.readAllStandardError();

                    qDebug() << "cmd.exe启动ADB服务器完成，退出码:" << exitCode;
                    qDebug() << "输出:" << output;
                    qDebug() << "错误:" << error;

                    // 即使退出码不为0，也尝试继续检查设备连接
                    // 因为ADB start-server有时会返回非0退出码但实际启动成功
                    m_checkState = CheckingDeviceConnected;
                    QTimer::singleShot(500, this, [this]()
                                       {
                        qDebug() << "cmd.exe启动后直接检查设备连接";
                        m_checkProcess->start(m_adbPath, QStringList() << "devices"); });
                    return;
                }
                else
                {
                    qDebug() << "cmd.exe启动ADB服务器失败或超时";
                    qDebug() << "进程状态:" << cmdProcess.state();
                    qDebug() << "错误:" << cmdProcess.errorString();
                }
#endif

                setState(Error);
                emit errorOccurred("ADB服务器启动失败，请检查ADB路径配置或手动启动ADB服务器");
                m_checkState = NotChecking;
                return;
            }

            // 优化：服务器启动成功后，直接检查设备连接，跳过ADB可用性检查
            qDebug() << "ADBConnection::onCheckProcessFinished() - ADB服务器启动成功，直接检查设备连接";
            QTimer::singleShot(500, this, [this]()
                               {
                m_checkState = CheckingDeviceConnected;
                m_checkProcess->start(m_adbPath, QStringList() << "devices"); });
        }
        else if (m_checkState == CheckingAdbAvailable)
        {
            if (exitCode == 0 && exitStatus == QProcess::NormalExit)
            {
                qDebug() << "ADBConnection::onCheckProcessFinished() - ADB可用，继续检查设备连接";
                // ADB可用，继续检查设备连接
                if (m_deviceId.isEmpty())
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 获取设备列表";
                    // 获取设备列表
                    m_checkState = CheckingDeviceConnected;
                    m_checkProcess->start(m_adbPath, QStringList() << "devices");
                }
                else
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 检查指定设备:" << m_deviceId;
                    // 检查指定设备的状态
                    checkSpecificDevice();
                    // 不重置状态，等待checkSpecificDevice的结果
                    return;
                }
            }
            else
            {
                qDebug() << "ADBConnection::onCheckProcessFinished() - ADB不可用";
                setState(Error);
                emit errorOccurred("ADB不可用，请检查ADB路径配置");
                m_checkState = NotChecking;
            }
        }
        else if (m_checkState == CheckingDeviceConnected)
        {
            qDebug() << "ADBConnection::onCheckProcessFinished() - 设备连接检查完成";
            if (exitCode == 0 && exitStatus == QProcess::NormalExit)
            {
                QString output = m_checkProcess->readAllStandardOutput();
                QStringList deviceLines = parseDeviceList(output);

                qDebug() << "ADBConnection::onCheckProcessFinished() - 发现设备行:" << deviceLines;

                if (deviceLines.isEmpty())
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 未发现设备";
                    setState(Error);
                    emit errorOccurred("没有找到可用的Android设备");
                    m_checkState = NotChecking;
                    return;
                }

                // 优化：直接从设备列表输出中获取设备状态，避免额外的get-state调用
                QString targetDevice;
                bool deviceFound = false;

                if (!m_deviceId.isEmpty())
                {
                    // 查找指定的设备
                    for (const QString &deviceLine : deviceLines)
                    {
                        QString deviceId = deviceLine.split("\t").first().trimmed();
                        if (deviceId == m_deviceId)
                        {
                            targetDevice = deviceLine;
                            deviceFound = true;
                            break;
                        }
                    }

                    if (!deviceFound)
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 指定的设备未连接:" << m_deviceId;
                        setState(Error);
                        emit errorOccurred(QString("指定的设备未连接: %1").arg(m_deviceId));
                        m_checkState = NotChecking;
                        return;
                    }
                }
                else
                {
                    // 使用第一个设备
                    targetDevice = deviceLines.first();
                    m_deviceId = targetDevice.split("\t").first().trimmed();
                    deviceFound = true;
                }

                qDebug() << "ADBConnection::onCheckProcessFinished() - 目标设备:" << targetDevice;

                // 检查设备状态
                if (targetDevice.contains("\tdevice"))
                {
                    // 设备已连接且可用，直接启动shell
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备已连接且可用，直接启动shell:" << m_deviceId;
                    m_deviceInfo.id = m_deviceId;
                    m_deviceInfo.state = Device;

                    // 直接启动shell，跳过get-state检查
                    startInteractiveShell();
                    return;
                }
                else if (targetDevice.contains("\toffline"))
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备离线:" << m_deviceId;
                    setState(Error);
                    emit errorOccurred(QString("设备离线: %1").arg(m_deviceId));
                    m_checkState = NotChecking;
                    return;
                }
                else if (targetDevice.contains("\tunauthorized"))
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备未授权:" << m_deviceId;
                    setState(Error);
                    emit errorOccurred(QString("设备未授权，请在设备上允许USB调试: %1").arg(m_deviceId));
                    m_checkState = NotChecking;
                    return;
                }
                else
                {
                    // 其他状态，使用原有的get-state检查
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态未知，使用get-state检查:" << targetDevice;
                    checkSpecificDevice();
                    return;
                }
            }
            else
            {
                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备连接检查失败，退出码:" << exitCode;
                setState(Error);
                emit errorOccurred("无法获取设备列表");
                m_checkState = NotChecking;
            }
        }
        else if (m_checkState == CheckingDeviceState)
        {
            qDebug() << "ADBConnection::onCheckProcessFinished() - 进入CheckingDeviceState分支";
            if (exitCode == 0 && exitStatus == QProcess::NormalExit)
            {
                QString rawOutput = output;
                QString state = rawOutput.trimmed();

                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态检查 - 原始输出:" << rawOutput.toUtf8().toHex();
                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态检查 - 处理后状态:" << state;
                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态检查 - 状态长度:" << state.length();
                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态检查 - 比较结果:" << (state == "device");

                if (state == "device")
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态正常，设置为已连接";
                    setState(Connected);
                    m_isConnected = true;
                    m_connectionTime = QDateTime::currentDateTime();

                    // 获取设备信息（异步安全版本）
                    try
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 调用refreshDeviceInfo";
                        refreshDeviceInfo();
                        qDebug() << "ADBConnection::onCheckProcessFinished() - refreshDeviceInfo完成";
                    }
                    catch (...)
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - refreshDeviceInfo异常，继续连接";
                    }

                    // 开始设备状态监控
                    if (m_deviceCheckTimer)
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 启动设备监控定时器";
                        m_deviceCheckTimer->start();
                    }

                    qDebug() << "ADBConnection::onCheckProcessFinished() - 启动交互式shell";
                    if (startInteractiveShell())
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 交互式shell启动成功";
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 发射connected信号";
                        emit connected();
                        qDebug() << "ADBConnection::onCheckProcessFinished() - connected信号发射完成";
                    }
                    else
                    {
                        qDebug() << "ADBConnection::onCheckProcessFinished() - 交互式shell启动失败";
                        setState(Error);
                        emit errorOccurred("无法启动交互式shell");
                    }
                    m_checkState = NotChecking;
                }
                else
                {
                    qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态异常";
                    setState(Error);
                    emit errorOccurred(QString("设备状态异常: %1").arg(state));
                    m_checkState = NotChecking;
                }
            }
            else
            {
                qDebug() << "ADBConnection::onCheckProcessFinished() - 设备状态检查失败，退出码:" << exitCode;
                setState(Error);
                emit errorOccurred(QString("无法连接到设备: %1").arg(m_deviceId));
                m_checkState = NotChecking;
            }
        }
        else
        {
            qDebug() << "ADBConnection::onCheckProcessFinished() - 未知检查状态:" << m_checkState;
        }

        // 在每个分支的末尾重置状态，而不是在这里统一重置
    }
    catch (const std::exception &e)
    {
        qDebug() << "ADBConnection::onCheckProcessFinished() - 异常:" << e.what();
        m_checkState = NotChecking;
        setState(Error);
        emit errorOccurred("连接检查过程中发生异常");
    }
    catch (...)
    {
        qDebug() << "ADBConnection::onCheckProcessFinished() - 未知异常";
        m_checkState = NotChecking;
        setState(Error);
        emit errorOccurred("连接检查过程中发生未知异常");
    }
}

void ADBConnection::checkSpecificDevice()
{
    QStringList args;
    if (!m_deviceId.isEmpty())
    {
        args << "-s" << m_deviceId;
    }
    args << "get-state";

    qDebug() << "ADBConnection::checkSpecificDevice() - 设置检查状态为CheckingDeviceState";
    m_checkState = CheckingDeviceState;
    m_checkProcess->start(m_adbPath, args);
    // 不创建新的信号连接，使用现有的onCheckProcessFinished处理
}

QStringList ADBConnection::parseDeviceList(const QString &output)
{
    QStringList devices;
    QStringList lines = output.split('\n');

    for (const QString &line : lines)
    {
        if (line.contains('\t') && !line.startsWith("List of devices"))
        {
            QStringList parts = line.split('\t');
            if (parts.size() >= 2 && parts[1].trimmed() == "device")
            {
                QString deviceId = parts[0].trimmed();
                if (!deviceId.isEmpty())
                {
                    devices.append(deviceId);
                }
            }
        }
    }

    return devices;
}

void ADBConnection::processDeviceStateFromOutput(const QString &output)
{
    QString state = output.trimmed();

    qDebug() << "ADBConnection::processDeviceStateFromOutput() - 原始输出:" << output.toUtf8().toHex();
    qDebug() << "ADBConnection::processDeviceStateFromOutput() - 处理后状态:" << state;
    qDebug() << "ADBConnection::processDeviceStateFromOutput() - 状态长度:" << state.length();
    qDebug() << "ADBConnection::processDeviceStateFromOutput() - 比较结果:" << (state == "device");

    if (state == "device")
    {
        qDebug() << "ADBConnection::processDeviceStateFromOutput() - 设备状态正常，设置为已连接";
        setState(Connected);
        m_isConnected = true;
        m_connectionTime = QDateTime::currentDateTime();

        // 获取设备信息
        refreshDeviceInfo();

        // 开始设备状态监控
        m_deviceCheckTimer->start();

        emit connected();
    }
    else
    {
        qDebug() << "ADBConnection::processDeviceStateFromOutput() - 设备状态异常";
        setState(Error);
        emit errorOccurred(QString("设备状态异常: %1").arg(state));
    }
}
