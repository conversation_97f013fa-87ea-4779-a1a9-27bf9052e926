/****************************************************************************
** Meta object code from reading C++ file 'MainWindow.h'
**
** Created by: The Qt Meta Object Compiler version 67 (Qt 5.12.9)
**
** WARNING! All changes made in this file will be lost!
*****************************************************************************/

#include "../../MainWindow.h"
#include <QtCore/qbytearray.h>
#include <QtCore/qmetatype.h>
#if !defined(Q_MOC_OUTPUT_REVISION)
#error "The header file 'MainWindow.h' doesn't include <QObject>."
#elif Q_MOC_OUTPUT_REVISION != 67
#error "This file was generated using the moc from 5.12.9. It"
#error "cannot be used with the include files from this version of Qt."
#error "(The moc has changed too much.)"
#endif

QT_BEGIN_MOC_NAMESPACE
QT_WARNING_PUSH
QT_WARNING_DISABLE_DEPRECATED
struct qt_meta_stringdata_MainWindow_t {
    QByteArrayData data[77];
    char stringdata0[1199];
};
#define QT_MOC_LITERAL(idx, ofs, len) \
    Q_STATIC_BYTE_ARRAY_DATA_HEADER_INITIALIZER_WITH_OFFSET(len, \
    qptrdiff(offsetof(qt_meta_stringdata_MainWindow_t, stringdata0) + ofs \
        - idx * sizeof(QByteArrayData)) \
    )
static const qt_meta_stringdata_MainWindow_t qt_meta_stringdata_MainWindow = {
    {
QT_MOC_LITERAL(0, 0, 10), // "MainWindow"
QT_MOC_LITERAL(1, 11, 20), // "showConnectionDialog"
QT_MOC_LITERAL(2, 32, 0), // ""
QT_MOC_LITERAL(3, 33, 13), // "connectDevice"
QT_MOC_LITERAL(4, 47, 16), // "disconnectDevice"
QT_MOC_LITERAL(5, 64, 25), // "onConnectionButtonClicked"
QT_MOC_LITERAL(6, 90, 25), // "onConnectionStatusChanged"
QT_MOC_LITERAL(7, 116, 6), // "status"
QT_MOC_LITERAL(8, 123, 7), // "details"
QT_MOC_LITERAL(9, 131, 14), // "onDataReceived"
QT_MOC_LITERAL(10, 146, 4), // "data"
QT_MOC_LITERAL(11, 151, 17), // "onRawDataReceived"
QT_MOC_LITERAL(12, 169, 15), // "onErrorOccurred"
QT_MOC_LITERAL(13, 185, 5), // "error"
QT_MOC_LITERAL(14, 191, 23), // "onConnectionEstablished"
QT_MOC_LITERAL(15, 215, 16), // "onConnectionLost"
QT_MOC_LITERAL(16, 232, 11), // "sendCommand"
QT_MOC_LITERAL(17, 244, 19), // "executeQuickCommand"
QT_MOC_LITERAL(18, 264, 15), // "toggleTimestamp"
QT_MOC_LITERAL(19, 280, 20), // "executeCommonCommand"
QT_MOC_LITERAL(20, 301, 4), // "name"
QT_MOC_LITERAL(21, 306, 7), // "content"
QT_MOC_LITERAL(22, 314, 15), // "onCommandEdited"
QT_MOC_LITERAL(23, 330, 7), // "oldName"
QT_MOC_LITERAL(24, 338, 7), // "newName"
QT_MOC_LITERAL(25, 346, 10), // "newContent"
QT_MOC_LITERAL(26, 357, 16), // "onCommandDeleted"
QT_MOC_LITERAL(27, 374, 14), // "onCommandAdded"
QT_MOC_LITERAL(28, 389, 13), // "addNewCommand"
QT_MOC_LITERAL(29, 403, 12), // "quickConnect"
QT_MOC_LITERAL(30, 416, 15), // "quickDisconnect"
QT_MOC_LITERAL(31, 432, 10), // "quickClear"
QT_MOC_LITERAL(32, 443, 10), // "quickPause"
QT_MOC_LITERAL(33, 454, 14), // "showFindDialog"
QT_MOC_LITERAL(34, 469, 15), // "toggleHighlight"
QT_MOC_LITERAL(35, 485, 16), // "showRemoteDialog"
QT_MOC_LITERAL(36, 502, 17), // "startRemoteServer"
QT_MOC_LITERAL(37, 520, 4), // "port"
QT_MOC_LITERAL(38, 525, 16), // "stopRemoteServer"
QT_MOC_LITERAL(39, 542, 19), // "connectToRemoteHost"
QT_MOC_LITERAL(40, 562, 4), // "host"
QT_MOC_LITERAL(41, 567, 11), // "controlMode"
QT_MOC_LITERAL(42, 579, 24), // "disconnectFromRemoteHost"
QT_MOC_LITERAL(43, 604, 17), // "sendRemoteMessage"
QT_MOC_LITERAL(44, 622, 7), // "message"
QT_MOC_LITERAL(45, 630, 24), // "broadcastToRemoteClients"
QT_MOC_LITERAL(46, 655, 23), // "onRemoteClientConnected"
QT_MOC_LITERAL(47, 679, 26), // "onRemoteClientDisconnected"
QT_MOC_LITERAL(48, 706, 20), // "onRemoteDataReceived"
QT_MOC_LITERAL(49, 727, 23), // "onRemoteConnectionError"
QT_MOC_LITERAL(50, 751, 28), // "QAbstractSocket::SocketError"
QT_MOC_LITERAL(51, 780, 14), // "showConfigMenu"
QT_MOC_LITERAL(52, 795, 13), // "showLogConfig"
QT_MOC_LITERAL(53, 809, 20), // "showBackgroundConfig"
QT_MOC_LITERAL(54, 830, 12), // "showUIConfig"
QT_MOC_LITERAL(55, 843, 18), // "showTerminalConfig"
QT_MOC_LITERAL(56, 862, 11), // "resetConfig"
QT_MOC_LITERAL(57, 874, 15), // "onConfigChanged"
QT_MOC_LITERAL(58, 890, 3), // "key"
QT_MOC_LITERAL(59, 894, 5), // "value"
QT_MOC_LITERAL(60, 900, 20), // "showImportExportMenu"
QT_MOC_LITERAL(61, 921, 12), // "exportConfig"
QT_MOC_LITERAL(62, 934, 12), // "importConfig"
QT_MOC_LITERAL(63, 947, 14), // "exportCommands"
QT_MOC_LITERAL(64, 962, 14), // "importCommands"
QT_MOC_LITERAL(65, 977, 19), // "toggleCommandsPanel"
QT_MOC_LITERAL(66, 997, 22), // "updateConnectionStatus"
QT_MOC_LITERAL(67, 1020, 23), // "updateConnectionDetails"
QT_MOC_LITERAL(68, 1044, 20), // "updateCommandButtons"
QT_MOC_LITERAL(69, 1065, 20), // "performBatchUIUpdate"
QT_MOC_LITERAL(70, 1086, 16), // "scheduleUIUpdate"
QT_MOC_LITERAL(71, 1103, 21), // "applyBackgroundConfig"
QT_MOC_LITERAL(72, 1125, 15), // "applyFontConfig"
QT_MOC_LITERAL(73, 1141, 31), // "onTerminalTimestampStateChanged"
QT_MOC_LITERAL(74, 1173, 7), // "enabled"
QT_MOC_LITERAL(75, 1181, 8), // "showHelp"
QT_MOC_LITERAL(76, 1190, 8) // "autoSave"

    },
    "MainWindow\0showConnectionDialog\0\0"
    "connectDevice\0disconnectDevice\0"
    "onConnectionButtonClicked\0"
    "onConnectionStatusChanged\0status\0"
    "details\0onDataReceived\0data\0"
    "onRawDataReceived\0onErrorOccurred\0"
    "error\0onConnectionEstablished\0"
    "onConnectionLost\0sendCommand\0"
    "executeQuickCommand\0toggleTimestamp\0"
    "executeCommonCommand\0name\0content\0"
    "onCommandEdited\0oldName\0newName\0"
    "newContent\0onCommandDeleted\0onCommandAdded\0"
    "addNewCommand\0quickConnect\0quickDisconnect\0"
    "quickClear\0quickPause\0showFindDialog\0"
    "toggleHighlight\0showRemoteDialog\0"
    "startRemoteServer\0port\0stopRemoteServer\0"
    "connectToRemoteHost\0host\0controlMode\0"
    "disconnectFromRemoteHost\0sendRemoteMessage\0"
    "message\0broadcastToRemoteClients\0"
    "onRemoteClientConnected\0"
    "onRemoteClientDisconnected\0"
    "onRemoteDataReceived\0onRemoteConnectionError\0"
    "QAbstractSocket::SocketError\0"
    "showConfigMenu\0showLogConfig\0"
    "showBackgroundConfig\0showUIConfig\0"
    "showTerminalConfig\0resetConfig\0"
    "onConfigChanged\0key\0value\0"
    "showImportExportMenu\0exportConfig\0"
    "importConfig\0exportCommands\0importCommands\0"
    "toggleCommandsPanel\0updateConnectionStatus\0"
    "updateConnectionDetails\0updateCommandButtons\0"
    "performBatchUIUpdate\0scheduleUIUpdate\0"
    "applyBackgroundConfig\0applyFontConfig\0"
    "onTerminalTimestampStateChanged\0enabled\0"
    "showHelp\0autoSave"
};
#undef QT_MOC_LITERAL

static const uint qt_meta_data_MainWindow[] = {

 // content:
       8,       // revision
       0,       // classname
       0,    0, // classinfo
      60,   14, // methods
       0,    0, // properties
       0,    0, // enums/sets
       0,    0, // constructors
       0,       // flags
       0,       // signalCount

 // slots: name, argc, parameters, tag, flags
       1,    0,  314,    2, 0x08 /* Private */,
       3,    0,  315,    2, 0x08 /* Private */,
       4,    0,  316,    2, 0x08 /* Private */,
       5,    0,  317,    2, 0x08 /* Private */,
       6,    2,  318,    2, 0x08 /* Private */,
       9,    1,  323,    2, 0x08 /* Private */,
      11,    1,  326,    2, 0x08 /* Private */,
      12,    1,  329,    2, 0x08 /* Private */,
      14,    0,  332,    2, 0x08 /* Private */,
      15,    0,  333,    2, 0x08 /* Private */,
      16,    0,  334,    2, 0x08 /* Private */,
      17,    0,  335,    2, 0x08 /* Private */,
      18,    0,  336,    2, 0x08 /* Private */,
      19,    2,  337,    2, 0x08 /* Private */,
      22,    3,  342,    2, 0x08 /* Private */,
      26,    1,  349,    2, 0x08 /* Private */,
      27,    2,  352,    2, 0x08 /* Private */,
      28,    0,  357,    2, 0x08 /* Private */,
      29,    0,  358,    2, 0x08 /* Private */,
      30,    0,  359,    2, 0x08 /* Private */,
      31,    0,  360,    2, 0x08 /* Private */,
      32,    0,  361,    2, 0x08 /* Private */,
      33,    0,  362,    2, 0x08 /* Private */,
      34,    0,  363,    2, 0x08 /* Private */,
      35,    0,  364,    2, 0x08 /* Private */,
      36,    1,  365,    2, 0x08 /* Private */,
      36,    0,  368,    2, 0x28 /* Private | MethodCloned */,
      38,    0,  369,    2, 0x08 /* Private */,
      39,    3,  370,    2, 0x08 /* Private */,
      39,    2,  377,    2, 0x28 /* Private | MethodCloned */,
      42,    0,  382,    2, 0x08 /* Private */,
      43,    1,  383,    2, 0x08 /* Private */,
      45,    1,  386,    2, 0x08 /* Private */,
      46,    0,  389,    2, 0x08 /* Private */,
      47,    0,  390,    2, 0x08 /* Private */,
      48,    0,  391,    2, 0x08 /* Private */,
      49,    1,  392,    2, 0x08 /* Private */,
      51,    0,  395,    2, 0x08 /* Private */,
      52,    0,  396,    2, 0x08 /* Private */,
      53,    0,  397,    2, 0x08 /* Private */,
      54,    0,  398,    2, 0x08 /* Private */,
      55,    0,  399,    2, 0x08 /* Private */,
      56,    0,  400,    2, 0x08 /* Private */,
      57,    2,  401,    2, 0x08 /* Private */,
      60,    0,  406,    2, 0x08 /* Private */,
      61,    0,  407,    2, 0x08 /* Private */,
      62,    0,  408,    2, 0x08 /* Private */,
      63,    0,  409,    2, 0x08 /* Private */,
      64,    0,  410,    2, 0x08 /* Private */,
      65,    0,  411,    2, 0x08 /* Private */,
      66,    0,  412,    2, 0x08 /* Private */,
      67,    0,  413,    2, 0x08 /* Private */,
      68,    0,  414,    2, 0x08 /* Private */,
      69,    0,  415,    2, 0x08 /* Private */,
      70,    0,  416,    2, 0x08 /* Private */,
      71,    0,  417,    2, 0x08 /* Private */,
      72,    0,  418,    2, 0x08 /* Private */,
      73,    1,  419,    2, 0x08 /* Private */,
      75,    0,  422,    2, 0x08 /* Private */,
      76,    0,  423,    2, 0x08 /* Private */,

 // slots: parameters
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,    7,    8,
    QMetaType::Void, QMetaType::QString,   10,
    QMetaType::Void, QMetaType::QByteArray,   10,
    QMetaType::Void, QMetaType::QString,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   20,   21,
    QMetaType::Void, QMetaType::QString, QMetaType::QString, QMetaType::QString,   23,   24,   25,
    QMetaType::Void, QMetaType::QString,   20,
    QMetaType::Void, QMetaType::QString, QMetaType::QString,   20,   21,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Int,   37,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::Int, QMetaType::Bool,   40,   37,   41,
    QMetaType::Void, QMetaType::QString, QMetaType::Int,   40,   37,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString,   44,
    QMetaType::Void, QMetaType::QString,   44,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, 0x80000000 | 50,   13,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::QString, QMetaType::QVariant,   58,   59,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void,
    QMetaType::Void, QMetaType::Bool,   74,
    QMetaType::Void,
    QMetaType::Void,

       0        // eod
};

void MainWindow::qt_static_metacall(QObject *_o, QMetaObject::Call _c, int _id, void **_a)
{
    if (_c == QMetaObject::InvokeMetaMethod) {
        auto *_t = static_cast<MainWindow *>(_o);
        Q_UNUSED(_t)
        switch (_id) {
        case 0: _t->showConnectionDialog(); break;
        case 1: _t->connectDevice(); break;
        case 2: _t->disconnectDevice(); break;
        case 3: _t->onConnectionButtonClicked(); break;
        case 4: _t->onConnectionStatusChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 5: _t->onDataReceived((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 6: _t->onRawDataReceived((*reinterpret_cast< const QByteArray(*)>(_a[1]))); break;
        case 7: _t->onErrorOccurred((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 8: _t->onConnectionEstablished(); break;
        case 9: _t->onConnectionLost(); break;
        case 10: _t->sendCommand(); break;
        case 11: _t->executeQuickCommand(); break;
        case 12: _t->toggleTimestamp(); break;
        case 13: _t->executeCommonCommand((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 14: _t->onCommandEdited((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2])),(*reinterpret_cast< const QString(*)>(_a[3]))); break;
        case 15: _t->onCommandDeleted((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 16: _t->onCommandAdded((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QString(*)>(_a[2]))); break;
        case 17: _t->addNewCommand(); break;
        case 18: _t->quickConnect(); break;
        case 19: _t->quickDisconnect(); break;
        case 20: _t->quickClear(); break;
        case 21: _t->quickPause(); break;
        case 22: _t->showFindDialog(); break;
        case 23: _t->toggleHighlight(); break;
        case 24: _t->showRemoteDialog(); break;
        case 25: _t->startRemoteServer((*reinterpret_cast< int(*)>(_a[1]))); break;
        case 26: _t->startRemoteServer(); break;
        case 27: _t->stopRemoteServer(); break;
        case 28: _t->connectToRemoteHost((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2])),(*reinterpret_cast< bool(*)>(_a[3]))); break;
        case 29: _t->connectToRemoteHost((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< int(*)>(_a[2]))); break;
        case 30: _t->disconnectFromRemoteHost(); break;
        case 31: _t->sendRemoteMessage((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 32: _t->broadcastToRemoteClients((*reinterpret_cast< const QString(*)>(_a[1]))); break;
        case 33: _t->onRemoteClientConnected(); break;
        case 34: _t->onRemoteClientDisconnected(); break;
        case 35: _t->onRemoteDataReceived(); break;
        case 36: _t->onRemoteConnectionError((*reinterpret_cast< QAbstractSocket::SocketError(*)>(_a[1]))); break;
        case 37: _t->showConfigMenu(); break;
        case 38: _t->showLogConfig(); break;
        case 39: _t->showBackgroundConfig(); break;
        case 40: _t->showUIConfig(); break;
        case 41: _t->showTerminalConfig(); break;
        case 42: _t->resetConfig(); break;
        case 43: _t->onConfigChanged((*reinterpret_cast< const QString(*)>(_a[1])),(*reinterpret_cast< const QVariant(*)>(_a[2]))); break;
        case 44: _t->showImportExportMenu(); break;
        case 45: _t->exportConfig(); break;
        case 46: _t->importConfig(); break;
        case 47: _t->exportCommands(); break;
        case 48: _t->importCommands(); break;
        case 49: _t->toggleCommandsPanel(); break;
        case 50: _t->updateConnectionStatus(); break;
        case 51: _t->updateConnectionDetails(); break;
        case 52: _t->updateCommandButtons(); break;
        case 53: _t->performBatchUIUpdate(); break;
        case 54: _t->scheduleUIUpdate(); break;
        case 55: _t->applyBackgroundConfig(); break;
        case 56: _t->applyFontConfig(); break;
        case 57: _t->onTerminalTimestampStateChanged((*reinterpret_cast< bool(*)>(_a[1]))); break;
        case 58: _t->showHelp(); break;
        case 59: _t->autoSave(); break;
        default: ;
        }
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        switch (_id) {
        default: *reinterpret_cast<int*>(_a[0]) = -1; break;
        case 36:
            switch (*reinterpret_cast<int*>(_a[1])) {
            default: *reinterpret_cast<int*>(_a[0]) = -1; break;
            case 0:
                *reinterpret_cast<int*>(_a[0]) = qRegisterMetaType< QAbstractSocket::SocketError >(); break;
            }
            break;
        }
    }
}

QT_INIT_METAOBJECT const QMetaObject MainWindow::staticMetaObject = { {
    &QMainWindow::staticMetaObject,
    qt_meta_stringdata_MainWindow.data,
    qt_meta_data_MainWindow,
    qt_static_metacall,
    nullptr,
    nullptr
} };


const QMetaObject *MainWindow::metaObject() const
{
    return QObject::d_ptr->metaObject ? QObject::d_ptr->dynamicMetaObject() : &staticMetaObject;
}

void *MainWindow::qt_metacast(const char *_clname)
{
    if (!_clname) return nullptr;
    if (!strcmp(_clname, qt_meta_stringdata_MainWindow.stringdata0))
        return static_cast<void*>(this);
    return QMainWindow::qt_metacast(_clname);
}

int MainWindow::qt_metacall(QMetaObject::Call _c, int _id, void **_a)
{
    _id = QMainWindow::qt_metacall(_c, _id, _a);
    if (_id < 0)
        return _id;
    if (_c == QMetaObject::InvokeMetaMethod) {
        if (_id < 60)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 60;
    } else if (_c == QMetaObject::RegisterMethodArgumentMetaType) {
        if (_id < 60)
            qt_static_metacall(this, _c, _id, _a);
        _id -= 60;
    }
    return _id;
}
QT_WARNING_POP
QT_END_MOC_NAMESPACE
