#include "ProfessionalTerminal.h"
#include <QDebug>
#include <QScrollBar>
#include <QTextBlock>
#include <QTextDocumentFragment>
#include <QTimer>
#include <QMouseEvent>
#include <QWheelEvent>
#include <QRegularExpression>
#include <QDateTime>
#include <QMenu>
#include <QAction>
#include <QClipboard>
#include <QApplication>
#include <QFileDialog>
#include <QInputDialog>
#include <QFile>
#include <QTextStream>

ProfessionalTerminal::ProfessionalTerminal(QWidget *parent)
    : QWidget(parent), m_isConnected(false), m_echoEnabled(false), m_timestampEnabled(false), m_atLineStart(true), m_historyIndex(-1), m_keyRepeatCount(0)
{
    qDebug() << "ProfessionalTerminal constructor started";

    try
    {
        qDebug() << "Setting up UI...";
        setupUI();

        qDebug() << "Setting up font...";
        setupFont();

        qDebug() << "Setting up colors...";
        setupColors();

        // 重要：在setupColors()之后重新确保文本选择功能
        if (m_display)
        {
            qDebug() << "重新设置文本交互标志...";
            m_display->setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);
            qDebug() << "文本交互标志重新设置完成:" << m_display->textInteractionFlags();

            // 添加定时检查，看看标志是否被其他代码修改
            QTimer *checkTimer = new QTimer(this);
            connect(checkTimer, &QTimer::timeout, this, [this]()
                    {
                if (m_display) {
                    Qt::TextInteractionFlags flags = m_display->textInteractionFlags();
                    if (!(flags & Qt::TextSelectableByMouse)) {
                        qDebug() << "警告：文本交互标志被修改了！当前标志:" << flags;
                        qDebug() << "重新设置文本选择标志...";
                        m_display->setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);
                    }
                } });
            checkTimer->start(1000); // 每秒检查一次
        }

        qDebug() << "Setting up timers...";
        // 简化光标处理，使用Qt内置光标
        m_cursorTimer = new QTimer(this);
        // 不需要复杂的光标字符处理

        // 设置数据刷新定时器
        m_flushTimer = new QTimer(this);
        m_flushTimer->setSingleShot(true);
        connect(m_flushTimer, &QTimer::timeout, this, [this]()
                {
            try {
                qDebug() << "ProfessionalTerminal::flushTimer - 定时器触发";
                if (!m_dataBuffer.isEmpty() && m_display) {
                    QString data = m_dataBuffer;
                    m_dataBuffer.clear();
                    qDebug() << "ProfessionalTerminal::flushTimer - 处理数据:" << data.left(50);
                    appendToDisplay(data);
                    qDebug() << "ProfessionalTerminal::flushTimer - 数据处理完成";
                } else {
                    qDebug() << "ProfessionalTerminal::flushTimer - 无数据或显示器为空";
                }
                qDebug() << "ProfessionalTerminal::flushTimer - 定时器回调即将结束";
            } catch (const std::exception &e) {
                qDebug() << "ProfessionalTerminal::flushTimer - 异常:" << e.what();
            } catch (...) {
                qDebug() << "ProfessionalTerminal::flushTimer - 未知异常";
            }
            qDebug() << "ProfessionalTerminal::flushTimer - 定时器回调结束"; });

        // 设置防抖定时器
        m_keyRepeatTimer = new QTimer(this);
        m_keyRepeatTimer->setSingleShot(true);
        m_keyRepeatTimer->setInterval(50); // 50ms防抖间隔
        connect(m_keyRepeatTimer, &QTimer::timeout, this, [this]()
                {
                    m_keyRepeatCount = 0; // 重置重复计数
                });

        // 设置右键菜单
        if (m_display)
        {
            m_display->setContextMenuPolicy(Qt::CustomContextMenu);
            connect(m_display, &QTextEdit::customContextMenuRequested,
                    this, &ProfessionalTerminal::showContextMenu);
        }

        qDebug() << "ProfessionalTerminal constructor completed successfully";
    }
    catch (const std::exception &e)
    {
        qDebug() << "Exception in ProfessionalTerminal constructor:" << e.what();
        throw;
    }
    catch (...)
    {
        qDebug() << "Unknown exception in ProfessionalTerminal constructor";
        throw;
    }
}

ProfessionalTerminal::~ProfessionalTerminal()
{
}

void ProfessionalTerminal::setupUI()
{
    m_layout = new QVBoxLayout(this);
    m_layout->setContentsMargins(0, 0, 0, 0);
    m_layout->setSpacing(0);

    // 创建QTextEdit，使用最简单的设置
    m_display = new QTextEdit(this);

    // 设置测试文本
    m_display->setPlainText("RF调试工具终端\n这是一个测试文本，您应该能够选择这些文字。\n请尝试用鼠标拖拽选择文本。");

    // 设置为只读但可选择
    m_display->setReadOnly(true);
    m_display->setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);

    // 确保焦点策略正确
    m_display->setFocusPolicy(Qt::StrongFocus);

    // 确保光标样式正确
    m_display->viewport()->setCursor(Qt::IBeamCursor);

    qDebug() << "ProfessionalTerminal::setupUI() - 主要QTextEdit创建完成";
    qDebug() << "ProfessionalTerminal::setupUI() - 文本交互标志:" << m_display->textInteractionFlags();
    qDebug() << "ProfessionalTerminal::setupUI() - 只读状态:" << m_display->isReadOnly();

    // 设置光标样式
    m_display->setCursorWidth(3); // 设置光标宽度

    // 设置焦点策略
    m_display->setFocusPolicy(Qt::StrongFocus);
    this->setFocusPolicy(Qt::StrongFocus);
    this->setFocusProxy(m_display);

    // 暂时完全禁用事件过滤器，测试文本选择
    // m_display->installEventFilter(this);

    // 连接信号
    connect(m_display, &QTextEdit::cursorPositionChanged,
            this, &ProfessionalTerminal::onCursorPositionChanged);
    connect(m_display, &QTextEdit::textChanged,
            this, &ProfessionalTerminal::onTextChanged);

    m_layout->addWidget(m_display);

    // 确保m_display填充整个区域
    m_layout->setStretch(0, 1);

    // 设置焦点
    m_display->setFocus();

    // 确保ProfessionalTerminal能接收鼠标事件
    this->setAttribute(Qt::WA_AcceptTouchEvents, false);
    this->setMouseTracking(true);

    // 调试信息：打印几何信息
    qDebug() << "ProfessionalTerminal::setupUI() - ProfessionalTerminal几何:" << this->geometry();
    qDebug() << "ProfessionalTerminal::setupUI() - m_display几何:" << m_display->geometry();
}

void ProfessionalTerminal::setupFont()
{
    m_terminalFont = QFont("Consolas", 10);
    m_terminalFont.setFixedPitch(true);
    m_display->setFont(m_terminalFont);
}

void ProfessionalTerminal::setupColors()
{
    m_backgroundColor = QColor(0, 0, 0);       // 黑色背景
    m_foregroundColor = QColor(255, 255, 255); // 白色前景
    m_cursorColor = QColor(0, 255, 255);       // 青色光标，更醒目

    QString styleSheet = QString(
                             "QTextEdit {"
                             "    background-color: %1;"
                             "    color: %2;"
                             "    border: none;"
                             "    selection-background-color: #3399ff;"
                             "}")
                             .arg(m_backgroundColor.name(), m_foregroundColor.name());

    m_display->setStyleSheet(styleSheet);

    // 确保样式表设置后文本选择功能仍然正常
    m_display->setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);
    qDebug() << "setupColors() - 样式表设置后重新确认文本交互标志:" << m_display->textInteractionFlags();
}

void ProfessionalTerminal::clear()
{
    m_display->clear();
    m_inputBuffer.clear();
    m_dataBuffer.clear();
    m_atLineStart = true; // 清屏后重置为行首状态

    if (m_isConnected)
    {
        appendMessage("Terminal cleared\n");
        // 清屏后确保光标位置正确
        updateCursor();
    }
}

void ProfessionalTerminal::setConnected(bool connected)
{
    qDebug() << "ProfessionalTerminal::setConnected() - 设置连接状态:" << connected << "当前状态:" << m_isConnected;

    try
    {
        // 避免重复设置相同状态
        if (m_isConnected == connected)
        {
            qDebug() << "ProfessionalTerminal::setConnected() - 状态未改变，跳过";
            return;
        }

        m_isConnected = connected;

        if (connected)
        {
            qDebug() << "ProfessionalTerminal::setConnected() - 连接成功，添加消息";
            appendMessage("=== Connected ===\n");
        }
        else
        {
            qDebug() << "ProfessionalTerminal::setConnected() - 连接断开，添加消息";
            appendMessage("=== Disconnected ===\n");
            m_inputBuffer.clear();
        }

        // 简单更新光标
        updateCursor();

        qDebug() << "ProfessionalTerminal::setConnected() - 设置完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::setConnected() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::setConnected() - 未知异常";
    }
}

void ProfessionalTerminal::appendData(const QString &data)
{
    try
    {
        if (data.isEmpty())
            return;

        qDebug() << "ProfessionalTerminal::appendData() - 接收数据:" << data.left(50);
        qDebug() << "ProfessionalTerminal::appendData() - 原始数据hex:" << data.toUtf8().toHex();

        // 检查数据长度，防止过大的数据导致问题
        QString processedData = data;
        if (processedData.length() > 10000)
        {
            qDebug() << "ProfessionalTerminal::appendData() - 数据过大，截断处理";
            processedData = processedData.left(10000);
        }

        // 改进的换行符处理 - 避免重复换行符
        processedData.replace("\r\n", "\n"); // 先处理Windows风格的换行符
        processedData.replace("\r", "\n");   // 再处理单独的回车符

        // 移除连续的换行符，避免空行
        processedData.replace(QRegExp("\n+"), "\n");

        // 处理ANSI转义序列，移除常见的控制字符
        processedData = removeAnsiEscapeSequences(processedData);

        // 时间戳处理将在appendToDisplay中进行，这里不处理

        qDebug() << "ProfessionalTerminal::appendData() - 处理后数据:" << processedData.left(50);
        qDebug() << "ProfessionalTerminal::appendData() - 处理后数据hex:" << processedData.toUtf8().toHex();
        qDebug() << "ProfessionalTerminal::appendData() - 当前缓冲区长度:" << m_dataBuffer.length();

        // 移除所有重复过滤 - 让终端数据自然显示

        // 检查缓冲区大小，防止内存溢出
        if (m_dataBuffer.length() + processedData.length() > 10000)
        {
            qDebug() << "ProfessionalTerminal::appendData() - 缓冲区过大，立即刷新";
            // 立即刷新现有数据，然后清空缓冲区
            if (!m_dataBuffer.isEmpty())
            {
                appendToDisplay(m_dataBuffer);
                m_dataBuffer.clear();
            }
        }

        // 累积数据，批量处理以提高性能
        m_dataBuffer.append(processedData);

        // 快速响应的刷新机制
        int delay = 20; // 减少基础延迟到20ms

        // 对于包含换行符的数据，立即显示
        if (processedData.contains("\n"))
        {
            delay = 5; // 换行数据几乎立即显示
        }

        if (m_flushTimer && !m_flushTimer->isActive())
        {
            m_flushTimer->start(delay);
        }
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::appendData() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::appendData() - 未知异常";
    }
}

void ProfessionalTerminal::appendMessage(const QString &message)
{
    try
    {
        if (message.isEmpty())
        {
            qDebug() << "ProfessionalTerminal::appendMessage() - 消息为空";
            return;
        }

        qDebug() << "ProfessionalTerminal::appendMessage() - 添加消息:" << message.left(50);

        QString finalMessage = message;

        // 如果启用了时间戳，添加时间戳前缀
        if (m_timestampEnabled)
        {
            QString timestamp = QDateTime::currentDateTime().toString("[hh:mm:ss.zzz] ");
            finalMessage = timestamp + message;
        }

        appendToDisplay(finalMessage);
        qDebug() << "ProfessionalTerminal::appendMessage() - 消息添加完成";
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::appendMessage() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::appendMessage() - 未知异常";
    }
}

void ProfessionalTerminal::appendToDisplay(const QString &text)
{
    try
    {
        if (text.isEmpty() || !m_display)
            return;

        // 检查文本长度
        if (text.length() > 10000)
        {
            qDebug() << "ProfessionalTerminal::appendToDisplay() - 文本过长，截断";
            appendToDisplay(text.left(10000));
            return;
        }

        // 检查文档大小，防止内存溢出
        QTextDocument *doc = m_display->document();
        if (doc && doc->characterCount() > 100000)
        {
            qDebug() << "ProfessionalTerminal::appendToDisplay() - 文档过大，清理";
            // 保留最后的50000个字符
            QTextCursor clearCursor(doc);
            clearCursor.movePosition(QTextCursor::Start);
            clearCursor.movePosition(QTextCursor::NextCharacter, QTextCursor::KeepAnchor,
                                     doc->characterCount() - 50000);
            clearCursor.removeSelectedText();
        }

        // 保存当前光标位置
        QTextCursor cursor = m_display->textCursor();
        bool wasAtEnd = cursor.atEnd();
        Q_UNUSED(wasAtEnd);

        // 移动到文档末尾
        cursor.movePosition(QTextCursor::End);

        // 处理时间戳
        QString finalText = text;
        if (m_timestampEnabled)
        {
            QString processedText;
            QString timestamp = QDateTime::currentDateTime().toString("[hh:mm:ss.zzz] ");

            for (int i = 0; i < text.length(); ++i)
            {
                QChar ch = text[i];

                // 如果在行首且不是换行符，添加时间戳
                if (m_atLineStart && ch != '\n')
                {
                    processedText += timestamp;
                    m_atLineStart = false;
                }

                processedText += ch;

                // 如果遇到换行符，标记下一个字符为行首
                if (ch == '\n')
                {
                    m_atLineStart = true;
                }
            }
            finalText = processedText;
        }
        else
        {
            // 即使不启用时间戳，也要跟踪行首状态
            for (int i = 0; i < text.length(); ++i)
            {
                QChar ch = text[i];
                if (ch != '\n')
                {
                    m_atLineStart = false;
                }
                else
                {
                    m_atLineStart = true;
                }
            }
        }

        // 设置字符格式为当前终端字体
        QTextCharFormat format;
        format.setFont(m_terminalFont);
        cursor.setCharFormat(format);

        // 插入处理后的文本
        cursor.insertText(finalText);

        // 设置光标到末尾并确保滚动到底部
        m_display->setTextCursor(cursor);

        // 强制滚动到底部，确保新内容可见
        scrollToBottom();

        // 确保光标可见
        m_display->ensureCursorVisible();

        updateCursor();
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::appendToDisplay() - 异常:" << e.what();
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::appendToDisplay() - 未知异常";
    }
}

void ProfessionalTerminal::scrollToBottom()
{
    if (!m_display)
        return;

    QScrollBar *scrollBar = m_display->verticalScrollBar();
    if (scrollBar)
    {
        // 使用QTimer::singleShot确保在UI更新后再滚动
        QTimer::singleShot(0, [scrollBar]()
                           { scrollBar->setValue(scrollBar->maximum()); });
    }
}

void ProfessionalTerminal::updateCursor()
{
    if (!m_display)
    {
        qDebug() << "ProfessionalTerminal::updateCursor() - display为空";
        return;
    }

    // 减少调试输出
    // qDebug() << "ProfessionalTerminal::updateCursor() - 开始更新光标";

    // 确保光标在文档末尾
    QTextCursor cursor = m_display->textCursor();
    cursor.movePosition(QTextCursor::End);
    m_display->setTextCursor(cursor);

    // 确保光标可见
    m_display->ensureCursorVisible();

    // 设置焦点以确保光标闪烁
    m_display->setFocus();

    // 减少调试输出
    // qDebug() << "ProfessionalTerminal::updateCursor() - 光标更新完成";
}

bool ProfessionalTerminal::eventFilter(QObject *obj, QEvent *event)
{
    try
    {
        if (!obj || !event)
        {
            return QWidget::eventFilter(obj, event);
        }

        // 详细记录所有事件
        if (obj == m_display)
        {
            // 对于鼠标事件，详细记录并让QTextEdit处理
            if (event->type() == QEvent::MouseButtonPress ||
                event->type() == QEvent::MouseButtonRelease ||
                event->type() == QEvent::MouseButtonDblClick ||
                event->type() == QEvent::MouseMove ||
                event->type() == QEvent::ContextMenu ||
                event->type() == QEvent::Wheel)
            {
                // 只记录非移动事件，避免日志过多
                if (event->type() != QEvent::MouseMove)
                {
                    qDebug() << "ProfessionalTerminal::eventFilter() - m_display鼠标事件:" << event->type() << "让QTextEdit处理";

                    // 如果是鼠标按下事件，添加更多调试信息
                    if (event->type() == QEvent::MouseButtonPress)
                    {
                        QMouseEvent *mouseEvent = static_cast<QMouseEvent *>(event);
                        qDebug() << "ProfessionalTerminal::eventFilter() - 鼠标按下详情: 按钮=" << mouseEvent->button() << "位置=" << mouseEvent->pos();
                    }
                }
                return false; // 让QTextEdit处理
            }
        }
        else
        {
            // 记录非m_display的事件
            if (event->type() == QEvent::MouseButtonPress ||
                event->type() == QEvent::MouseButtonRelease ||
                event->type() == QEvent::MouseButtonDblClick ||
                event->type() == QEvent::MouseMove)
            {
                qDebug() << "ProfessionalTerminal::eventFilter() - 非m_display鼠标事件:" << event->type() << "对象:" << obj;
            }
            return QWidget::eventFilter(obj, event);
        }

        // 只拦截键盘输入事件，允许其他事件通过
        if (event->type() != QEvent::KeyPress)
        {
            return false; // 让QTextEdit处理其他事件
        }

        QKeyEvent *keyEvent = static_cast<QKeyEvent *>(event);
        if (!keyEvent)
        {
            return QWidget::eventFilter(obj, event);
        }

        // 允许复制粘贴等快捷键正常工作
        if (keyEvent->modifiers() & Qt::ControlModifier)
        {
            // Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X 等快捷键让QTextEdit自己处理
            if (keyEvent->key() == Qt::Key_C ||
                keyEvent->key() == Qt::Key_V ||
                keyEvent->key() == Qt::Key_A ||
                keyEvent->key() == Qt::Key_X ||
                keyEvent->key() == Qt::Key_Z ||
                keyEvent->key() == Qt::Key_Y)
            {
                qDebug() << "ProfessionalTerminal::eventFilter() - 允许快捷键:" << keyEvent->key();
                return false; // 让QTextEdit处理
            }
        }

        // 即使未连接也处理键盘事件，只是不发送到连接
        // 这样用户可以在未连接时输入命令，连接后再发送
        // 减少调试输出
        // qDebug() << "ProfessionalTerminal::eventFilter() - 键盘事件:" << keyEvent->key() << keyEvent->text();

        // 处理特殊按键
        if (handleSpecialKeys(keyEvent))
        {
            return true; // 事件已处理
        }

        // 处理普通字符输入
        QString text = keyEvent->text();
        if (!text.isEmpty() && text.length() > 0 && text.at(0).isPrint())
        {
            // 对于可打印字符，允许重复但限制频率
            static qint64 lastCharTime = 0;
            qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

            // 限制字符输入频率为每30ms一次，允许快速输入
            if (keyEvent->isAutoRepeat() && currentTime - lastCharTime < 30)
            {
                return true;
            }

            lastCharTime = currentTime;
            qDebug() << "ProfessionalTerminal::eventFilter() - 发送字符:" << text;

            // 直接发送字符到串口
            emit dataToSend(text.toUtf8());

            // 如果启用回显，显示输入的字符
            if (m_echoEnabled)
            {
                appendToDisplay(text);
            }

            return true; // 阻止默认处理
        }

        return QWidget::eventFilter(obj, event);
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::eventFilter() - 异常:" << e.what();
        return QWidget::eventFilter(obj, event);
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::eventFilter() - 未知异常";
        return QWidget::eventFilter(obj, event);
    }
}

void ProfessionalTerminal::keyPressEvent(QKeyEvent *event)
{
    // 现在主要的键盘事件处理
    qDebug() << "ProfessionalTerminal::keyPressEvent() - 处理键盘事件:" << event->key();

    // 允许复制粘贴等快捷键正常工作
    if (event->modifiers() & Qt::ControlModifier)
    {
        // Ctrl+C, Ctrl+V, Ctrl+A, Ctrl+X 等快捷键让QTextEdit自己处理
        if (event->key() == Qt::Key_C ||
            event->key() == Qt::Key_V ||
            event->key() == Qt::Key_A ||
            event->key() == Qt::Key_X ||
            event->key() == Qt::Key_Z ||
            event->key() == Qt::Key_Y)
        {
            qDebug() << "ProfessionalTerminal::keyPressEvent() - 允许快捷键:" << event->key();
            QWidget::keyPressEvent(event);
            return;
        }
    }

    // 处理特殊按键
    if (handleSpecialKeys(event))
    {
        return; // 事件已处理
    }

    // 处理普通字符输入
    QString text = event->text();
    if (!text.isEmpty() && text.length() > 0 && text.at(0).isPrint())
    {
        qDebug() << "ProfessionalTerminal::keyPressEvent() - 发送字符:" << text;

        // 直接发送字符到串口
        emit dataToSend(text.toUtf8());

        // 如果启用回显，显示输入的字符
        if (m_echoEnabled)
        {
            appendToDisplay(text);
        }

        return; // 阻止默认处理
    }

    QWidget::keyPressEvent(event);
}

void ProfessionalTerminal::mousePressEvent(QMouseEvent *event)
{
    qDebug() << "ProfessionalTerminal::mousePressEvent() - 鼠标按下:" << event->button() << "位置:" << event->pos();

    // 调试：检查m_display的状态
    if (m_display)
    {
        qDebug() << "ProfessionalTerminal::mousePressEvent() - m_display几何:" << m_display->geometry();
        qDebug() << "ProfessionalTerminal::mousePressEvent() - m_display可见:" << m_display->isVisible();
        qDebug() << "ProfessionalTerminal::mousePressEvent() - m_display启用:" << m_display->isEnabled();
        qDebug() << "ProfessionalTerminal::mousePressEvent() - m_display焦点策略:" << m_display->focusPolicy();
        qDebug() << "ProfessionalTerminal::mousePressEvent() - m_display文本交互标志:" << m_display->textInteractionFlags();

        // 尝试直接给m_display设置焦点
        m_display->setFocus(Qt::MouseFocusReason);
        qDebug() << "ProfessionalTerminal::mousePressEvent() - 已设置焦点到m_display";

        // 测试：尝试通过代码选择文本
        QTextCursor cursor = m_display->textCursor();
        cursor.movePosition(QTextCursor::Start);
        cursor.movePosition(QTextCursor::Right, QTextCursor::KeepAnchor, 10);
        m_display->setTextCursor(cursor);
        qDebug() << "ProfessionalTerminal::mousePressEvent() - 测试代码选择文本，选中文本:" << cursor.selectedText();
    }

    // 完全不处理，让Qt默认机制处理
    QWidget::mousePressEvent(event);
}

bool ProfessionalTerminal::event(QEvent *event)
{
    // 记录所有事件，特别是鼠标事件
    if (event->type() == QEvent::MouseButtonPress ||
        event->type() == QEvent::MouseButtonRelease ||
        event->type() == QEvent::MouseButtonDblClick)
    {
        qDebug() << "ProfessionalTerminal::event() - 接收到鼠标事件:" << event->type();
    }

    return QWidget::event(event);
}

void ProfessionalTerminal::mouseReleaseEvent(QMouseEvent *event)
{
    qDebug() << "ProfessionalTerminal::mouseReleaseEvent() - 鼠标释放:" << event->button() << "位置:" << event->pos();
    QWidget::mouseReleaseEvent(event);
}

void ProfessionalTerminal::mouseMoveEvent(QMouseEvent *event)
{
    // 只在按下鼠标时记录移动事件，避免过多日志
    if (event->buttons() != Qt::NoButton)
    {
        qDebug() << "ProfessionalTerminal::mouseMoveEvent() - 鼠标移动:" << event->pos();
    }
    QWidget::mouseMoveEvent(event);
}

bool ProfessionalTerminal::handleSpecialKeys(QKeyEvent *event)
{
    switch (event->key())
    {
    case Qt::Key_Return:
    case Qt::Key_Enter:
        // 改进的回车键处理 - 允许重复但限制频率
        {
            static qint64 lastEnterTime = 0;
            qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

            // 减少间隔到50ms，提高响应性
            if (currentTime - lastEnterTime < 50)
            {
                qDebug() << "ProfessionalTerminal::handleSpecialKeys() - 回车键频率限制";
                return true;
            }

            lastEnterTime = currentTime;
            qDebug() << "ProfessionalTerminal::handleSpecialKeys() - 发送回车键";
            emit dataToSend("\n"); // 发送换行符，适合ADB shell和大多数终端
            return true;
        }

    case Qt::Key_Backspace:
        // 退格键允许重复，但限制频率
        {
            static qint64 lastBackspaceTime = 0;
            qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

            if (currentTime - lastBackspaceTime < 50) // 50ms间隔
            {
                return true;
            }

            lastBackspaceTime = currentTime;
            emit dataToSend("\b");
            return true;
        }

    case Qt::Key_Tab:
        if (event->isAutoRepeat())
        {
            return true; // 忽略重复的Tab键
        }
        emit dataToSend("\t");
        return true;

    case Qt::Key_Escape:
        if (event->isAutoRepeat())
        {
            return true; // 忽略重复的Escape键
        }
        emit dataToSend("\x1b");
        return true;

    case Qt::Key_Up:
        // 方向键允许重复，限制频率为每80ms一次
        {
            static qint64 lastUpTime = 0;
            qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

            if (currentTime - lastUpTime < 80)
            {
                return true;
            }

            lastUpTime = currentTime;
            emit dataToSend("\x1b[A");
            return true;
        }

    case Qt::Key_Down:
    {
        static qint64 lastDownTime = 0;
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

        if (currentTime - lastDownTime < 80)
        {
            return true;
        }

        lastDownTime = currentTime;
        emit dataToSend("\x1b[B");
        return true;
    }

    case Qt::Key_Right:
    {
        static qint64 lastRightTime = 0;
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

        if (currentTime - lastRightTime < 80)
        {
            return true;
        }

        lastRightTime = currentTime;
        emit dataToSend("\x1b[C");
        return true;
    }

    case Qt::Key_Left:
    {
        static qint64 lastLeftTime = 0;
        qint64 currentTime = QDateTime::currentMSecsSinceEpoch();

        if (currentTime - lastLeftTime < 80)
        {
            return true;
        }

        lastLeftTime = currentTime;
        emit dataToSend("\x1b[D");
        return true;
    }

    default:
        // 处理Ctrl组合键
        if (event->modifiers() & Qt::ControlModifier)
        {
            int key = event->key();
            if (key >= Qt::Key_A && key <= Qt::Key_Z)
            {
                char ctrlChar = key - Qt::Key_A + 1;
                emit dataToSend(QByteArray(1, ctrlChar));
                return true;
            }
        }
        break;
    }

    return false;
}

void ProfessionalTerminal::wheelEvent(QWheelEvent *event)
{
    try
    {
        qDebug() << "ProfessionalTerminal::wheelEvent() - 收到滚轮事件";

        if (!m_display)
        {
            qDebug() << "ProfessionalTerminal::wheelEvent() - 显示器为空";
            event->ignore();
            return;
        }

        // 检查是否按住Ctrl键
        if (event->modifiers() & Qt::ControlModifier)
        {
            qDebug() << "ProfessionalTerminal::wheelEvent() - Ctrl+滚轮调整字体大小";

            int delta = event->angleDelta().y();
            QFont currentFont = m_display->font();
            int currentSize = currentFont.pointSize();

            if (delta > 0)
            {
                // 放大字体
                int newSize = qMin(currentSize + 1, 24);
                currentFont.setPointSize(newSize);
                m_display->setFont(currentFont);
                m_terminalFont = currentFont;
                qDebug() << "字体大小增加到:" << newSize;

                // 发送字体变化信号
                emit fontSizeChanged(newSize);
            }
            else if (delta < 0)
            {
                // 缩小字体
                int newSize = qMax(currentSize - 1, 8);
                currentFont.setPointSize(newSize);
                m_display->setFont(currentFont);
                m_terminalFont = currentFont;
                qDebug() << "字体大小减少到:" << newSize;

                // 发送字体变化信号
                emit fontSizeChanged(newSize);
            }

            event->accept();
            return;
        }

        qDebug() << "ProfessionalTerminal::wheelEvent() - 安全转发滚轮事件";
        // 安全地转发滚轮事件到显示区域
        if (m_display->verticalScrollBar())
        {
            QScrollBar *scrollBar = m_display->verticalScrollBar();
            int delta = event->angleDelta().y();
            int currentValue = scrollBar->value();
            int newValue = currentValue - delta / 8; // 调整滚动速度
            scrollBar->setValue(newValue);
        }

        qDebug() << "ProfessionalTerminal::wheelEvent() - 滚轮事件处理完成";
        event->accept();
    }
    catch (const std::exception &e)
    {
        qDebug() << "ProfessionalTerminal::wheelEvent() - 异常:" << e.what();
        event->ignore();
    }
    catch (...)
    {
        qDebug() << "ProfessionalTerminal::wheelEvent() - 未知异常";
        event->ignore();
    }
}

void ProfessionalTerminal::onCursorPositionChanged()
{
    updateCursor();
}

void ProfessionalTerminal::onTextChanged()
{
    // 文本变化时的处理
}

void ProfessionalTerminal::setFont(const QFont &font)
{
    m_terminalFont = font;
    m_display->setFont(font);
}

void ProfessionalTerminal::setTerminalFont(const QString &family, int size, const QString &weight)
{
    qDebug() << "ProfessionalTerminal::setTerminalFont() - Start setting font:" << family << size << weight;

    // 验证输入参数
    if (size <= 0)
    {
        qDebug() << "ProfessionalTerminal::setTerminalFont() - ERROR: Invalid font size:" << size;
        size = 10; // 使用默认大小
    }

    QFont font(family);
    font.setPointSize(size); // 明确设置点大小
    font.setFixedPitch(true);

    // 如果点大小设置失败，尝试像素大小
    if (font.pointSize() != size)
    {
        qDebug() << "Point size failed, trying pixel size";
        font.setPixelSize(size);
    }

    if (weight == "bold")
    {
        font.setWeight(QFont::Bold);
    }
    else
    {
        font.setWeight(QFont::Normal);
    }

    qDebug() << "ProfessionalTerminal::setTerminalFont() - Created font object:" << font.family() << font.pointSize() << font.weight();

    m_terminalFont = font;

    if (m_display)
    {
        QFont oldFont = m_display->font();
        qDebug() << "ProfessionalTerminal::setTerminalFont() - Old font:" << oldFont.family() << oldFont.pointSize() << oldFont.weight();

        // 尝试多种方法设置字体
        m_display->setFont(font);

        // 方法1：设置文档字体
        if (m_display->document())
        {
            m_display->document()->setDefaultFont(font);
            qDebug() << "ProfessionalTerminal::setTerminalFont() - Document font set";
        }

        // 方法2：设置当前字符格式
        QTextCharFormat format;
        format.setFont(font);
        m_display->setCurrentCharFormat(format);
        qDebug() << "ProfessionalTerminal::setTerminalFont() - Char format set";

        // 方法3：选择全部文本并应用字体
        QTextCursor cursor = m_display->textCursor();
        cursor.select(QTextCursor::Document);
        cursor.setCharFormat(format);
        m_display->setTextCursor(cursor);
        qDebug() << "ProfessionalTerminal::setTerminalFont() - Applied to all text";

        QFont newFont = m_display->font();
        qDebug() << "ProfessionalTerminal::setTerminalFont() - New font:" << newFont.family() << newFont.pointSize() << newFont.weight();

        // 强制刷新显示
        m_display->update();
        m_display->repaint();
        m_display->viewport()->update();

        qDebug() << "ProfessionalTerminal::setTerminalFont() - Font setting completed with refresh";

        // 验证字体是否真的被应用了
        QFont verifyFont = m_display->font();
        qDebug() << "ProfessionalTerminal::setTerminalFont() - Verification: actual font is"
                 << verifyFont.family() << verifyFont.pointSize() << "pt";

        // 如果字体设置失败，尝试强制设置样式表
        if (verifyFont.family() != family || verifyFont.pointSize() != size)
        {
            qDebug() << "ProfessionalTerminal::setTerminalFont() - Font verification failed, trying stylesheet approach";

            // 暂时禁用样式表设置，测试是否是样式表导致的问题
            qDebug() << "ProfessionalTerminal::setTerminalFont() - 暂时跳过样式表设置，测试文本选择功能";
            // QString styleSheet = QString("QTextEdit { font-family: '%1'; font-size: %2pt; }").arg(family).arg(size);
            // m_display->setStyleSheet(styleSheet);
        }

        // 重要：在字体设置完成后，重新确保文本选择功能
        qDebug() << "ProfessionalTerminal::setTerminalFont() - 重新设置文本交互标志";
        Qt::TextInteractionFlags oldFlags = m_display->textInteractionFlags();
        m_display->setTextInteractionFlags(Qt::TextSelectableByMouse | Qt::TextSelectableByKeyboard);
        Qt::TextInteractionFlags newFlags = m_display->textInteractionFlags();
        qDebug() << "ProfessionalTerminal::setTerminalFont() - 文本交互标志: 旧=" << oldFlags << "新=" << newFlags;

        // 强制重新设置其他可能影响文本选择的属性
        m_display->setReadOnly(true);
        m_display->viewport()->setCursor(Qt::IBeamCursor);
        m_display->setFocusPolicy(Qt::StrongFocus);

        // 尝试清除可能影响鼠标事件的属性
        m_display->setAttribute(Qt::WA_AcceptTouchEvents, false);
        m_display->viewport()->setAttribute(Qt::WA_AcceptTouchEvents, false);

        qDebug() << "ProfessionalTerminal::setTerminalFont() - 重新设置所有文本选择相关属性完成";
    }
    else
    {
        qDebug() << "ProfessionalTerminal::setTerminalFont() - WARNING: m_display is null";
    }
}

void ProfessionalTerminal::setColors(const QColor &background, const QColor &foreground)
{
    m_backgroundColor = background;
    m_foregroundColor = foreground;
    setupColors();
}

void ProfessionalTerminal::setEchoMode(bool enabled)
{
    m_echoEnabled = enabled;
}

void ProfessionalTerminal::setTimestampEnabled(bool enabled)
{
    qDebug() << "ProfessionalTerminal::setTimestampEnabled() - 设置时间戳状态:" << enabled;

    if (m_timestampEnabled != enabled)
    {
        m_timestampEnabled = enabled;

        // 切换时间戳状态时，重置行首状态以确保一致性
        m_atLineStart = true;

        // 发出状态变化信号
        emit timestampStateChanged(enabled);
        qDebug() << "ProfessionalTerminal::setTimestampEnabled() - 发出timestampStateChanged信号:" << enabled;
    }
}

QString ProfessionalTerminal::removeAnsiEscapeSequences(const QString &text)
{
    QString result = text;

    // 移除ANSI转义序列
    // ESC[...m (颜色和样式)
    result.remove(QRegExp("\x1b\\[[0-9;]*m"));

    // ESC[...H (光标位置)
    result.remove(QRegExp("\x1b\\[[0-9;]*H"));

    // ESC[...J (清除屏幕)
    result.remove(QRegExp("\x1b\\[[0-9;]*J"));

    // ESC[...K (清除行)
    result.remove(QRegExp("\x1b\\[[0-9;]*K"));

    // ESC[...A, ESC[...B, ESC[...C, ESC[...D (光标移动)
    result.remove(QRegExp("\x1b\\[[0-9;]*[ABCD]"));

    // 其他常见的转义序列
    result.remove(QRegExp("\x1b\\[[0-9;]*[a-zA-Z]"));

    // 移除其他控制字符，但保留换行符和制表符
    for (int i = 0; i < result.length(); ++i)
    {
        QChar ch = result.at(i);
        if (ch.unicode() < 32 && ch != '\n' && ch != '\t')
        {
            result[i] = ' '; // 替换为空格而不是删除，保持格式
        }
    }

    return result;
}

void ProfessionalTerminal::showContextMenu(const QPoint &pos)
{
    QMenu contextMenu(this);

    // 清理输出
    QAction *clearAction = contextMenu.addAction("🗑️ 清理输出");
    clearAction->setShortcut(QKeySequence("Ctrl+L"));
    connect(clearAction, &QAction::triggered, this, &ProfessionalTerminal::clearTerminal);

    contextMenu.addSeparator();

    // 复制选中内容
    QAction *copyAction = contextMenu.addAction("📋 复制");
    copyAction->setShortcut(QKeySequence::Copy);
    copyAction->setEnabled(m_display && m_display->textCursor().hasSelection());
    connect(copyAction, &QAction::triggered, this, &ProfessionalTerminal::copySelection);

    // 粘贴
    QAction *pasteAction = contextMenu.addAction("📄 粘贴");
    pasteAction->setShortcut(QKeySequence::Paste);
    connect(pasteAction, &QAction::triggered, this, &ProfessionalTerminal::pasteText);

    // 全选
    QAction *selectAllAction = contextMenu.addAction("🔘 全选");
    selectAllAction->setShortcut(QKeySequence::SelectAll);
    connect(selectAllAction, &QAction::triggered, this, &ProfessionalTerminal::selectAll);

    contextMenu.addSeparator();

    // 查找
    QAction *findAction = contextMenu.addAction("🔍 查找");
    findAction->setShortcut(QKeySequence::Find);
    connect(findAction, &QAction::triggered, this, QOverload<>::of(&ProfessionalTerminal::findText));

    // 保存到文件
    QAction *saveAction = contextMenu.addAction("💾 保存到文件");
    saveAction->setShortcut(QKeySequence("Ctrl+S"));
    connect(saveAction, &QAction::triggered, this, &ProfessionalTerminal::saveToFile);

    contextMenu.addSeparator();

    // 字体大小调整
    QMenu *fontMenu = contextMenu.addMenu("🔤 字体大小");

    QAction *increaseFontAction = fontMenu->addAction("➕ 增大字体");
    increaseFontAction->setShortcut(QKeySequence("Ctrl++"));
    connect(increaseFontAction, &QAction::triggered, this, [this]()
            {
        QFont currentFont = m_display->font();
        int currentSize = currentFont.pointSize();
        int newSize = qMin(currentSize + 1, 24);
        currentFont.setPointSize(newSize);
        m_display->setFont(currentFont);
        m_terminalFont = currentFont;
        emit fontSizeChanged(newSize);
        appendMessage(QString("🔤 字体大小调整为: %1pt\n").arg(newSize)); });

    QAction *decreaseFontAction = fontMenu->addAction("➖ 减小字体");
    decreaseFontAction->setShortcut(QKeySequence("Ctrl+-"));
    connect(decreaseFontAction, &QAction::triggered, this, [this]()
            {
        QFont currentFont = m_display->font();
        int currentSize = currentFont.pointSize();
        int newSize = qMax(currentSize - 1, 8);
        currentFont.setPointSize(newSize);
        m_display->setFont(currentFont);
        m_terminalFont = currentFont;
        emit fontSizeChanged(newSize);
        appendMessage(QString("🔤 字体大小调整为: %1pt\n").arg(newSize)); });

    QAction *resetFontAction = fontMenu->addAction("🔄 重置字体");
    connect(resetFontAction, &QAction::triggered, this, [this]()
            {
        QFont defaultFont("Consolas", 10);
        defaultFont.setFixedPitch(true);
        m_display->setFont(defaultFont);
        m_terminalFont = defaultFont;
        emit fontSizeChanged(10);
        appendMessage("🔤 字体大小重置为: 10pt\n"); });

    contextMenu.addSeparator();

    // 时间戳切换
    QAction *timestampAction = contextMenu.addAction(m_timestampEnabled ? "🕐 禁用时间戳" : "🕐 启用时间戳");
    connect(timestampAction, &QAction::triggered, this, [this]()
            {
        setTimestampEnabled(!m_timestampEnabled);
        QString msg = m_timestampEnabled ? "✅ 时间戳已启用\n" : "❌ 时间戳已禁用\n";
        appendMessage(msg); });

    // 显示菜单
    contextMenu.exec(m_display->mapToGlobal(pos));
}

void ProfessionalTerminal::clearTerminal()
{
    clear();
    appendMessage("🗑️ 终端已清理\n");
}

void ProfessionalTerminal::copySelection()
{
    if (m_display && m_display->textCursor().hasSelection())
    {
        m_display->copy();
        // 移除多余的复制提示消息，保持日志区域清洁
        qDebug() << "ProfessionalTerminal::copySelection() - Text copied to clipboard";
    }
}

void ProfessionalTerminal::pasteText()
{
    if (m_display)
    {
        QClipboard *clipboard = QApplication::clipboard();
        QString text = clipboard->text();
        if (!text.isEmpty())
        {
            // 将粘贴的文本作为命令发送
            emit dataToSend(text.toUtf8());
            appendMessage(QString("📄 已粘贴: %1\n").arg(text.left(50)));
        }
    }
}

void ProfessionalTerminal::selectAll()
{
    if (m_display)
    {
        m_display->selectAll();
        appendMessage("🔘 已全选\n");
    }
}

void ProfessionalTerminal::saveToFile()
{
    if (!m_display)
        return;

    QString fileName = QFileDialog::getSaveFileName(this,
                                                    "保存终端内容",
                                                    QString("terminal_log_%1.txt").arg(QDateTime::currentDateTime().toString("yyyyMMdd_hhmmss")),
                                                    "文本文件 (*.txt);;所有文件 (*.*)");

    if (!fileName.isEmpty())
    {
        QFile file(fileName);
        if (file.open(QIODevice::WriteOnly | QIODevice::Text))
        {
            QTextStream out(&file);
            out << m_display->toPlainText();
            file.close();
            appendMessage(QString("💾 已保存到: %1\n").arg(fileName));
        }
        else
        {
            appendMessage("❌ 保存失败\n");
        }
    }
}

void ProfessionalTerminal::findText()
{
    if (!m_display)
        return;

    bool ok;
    QString searchText = QInputDialog::getText(this, "查找", "请输入要查找的文本:", QLineEdit::Normal, "", &ok);

    if (ok && !searchText.isEmpty())
    {
        findText(searchText);
    }
}

void ProfessionalTerminal::findText(const QString &searchText)
{
    if (!m_display || searchText.isEmpty())
        return;

    // 从当前位置开始查找
    QTextCursor cursor = m_display->textCursor();
    QTextCursor found = m_display->document()->find(searchText, cursor);

    if (found.isNull())
    {
        // 如果没找到，从文档开头重新查找
        cursor.movePosition(QTextCursor::Start);
        found = m_display->document()->find(searchText, cursor);
    }

    if (!found.isNull())
    {
        // 找到了，选中并滚动到该位置
        m_display->setTextCursor(found);
        m_display->ensureCursorVisible();

        // 高亮显示找到的文本
        QTextCharFormat highlightFormat;
        highlightFormat.setBackground(QColor(255, 255, 0, 100)); // 黄色半透明背景
        found.setCharFormat(highlightFormat);

        appendMessage(QString("🔍 找到: %1 (位置: %2)\n").arg(searchText).arg(found.position()));
    }
    else
    {
        appendMessage(QString("❌ 未找到: %1\n").arg(searchText));
    }
}
