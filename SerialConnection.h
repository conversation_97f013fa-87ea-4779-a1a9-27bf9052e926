#ifndef SERIALCONNECTION_H
#define SERIALCONNECTION_H

#include <QObject>
#include <QSerialPort>
#include <QSerialPortInfo>
#include <QTimer>
#include <QVariantMap>
#include <QMutex>

class SerialConnection : public QObject
{
    Q_OBJECT

public:
    explicit SerialConnection(QObject *parent = nullptr);
    ~SerialConnection();

    // 连接管理
    bool connect(const QVariantMap &params);
    void disconnect();
    bool isConnected() const;

    // 数据传输
    bool sendData(const QByteArray &data);
    bool sendCommand(const QString &command);

    // 串口信息
    static QStringList getAvailablePorts();
    static QList<QSerialPortInfo> getPortInfoList();
    QString currentPortName() const;
    QSerialPort::BaudRate currentBaudRate() const;

    // 登录处理
    bool handleLogin(const QString &username, const QString &password);
    void setLoginCredentials(const QString &username, const QString &password);

    // 配置
    void setReadTimeout(int msecs);
    int readTimeout() const;
    void setWriteTimeout(int msecs);
    int writeTimeout() const;

signals:
    void connected();
    void disconnected();
    void dataReceived(const QByteArray &data);
    void errorOccurred(const QString &error);
    void loginPromptDetected(const QString &prompt);
    void loginCompleted(bool success);

private slots:
    void onReadyRead();
    void onErrorOccurred(QSerialPort::SerialPortError error);
    void onLoginTimeout();
    void onDelayedAction();

private:
    void processReceivedData();
    bool isLoginPrompt(const QString &text) const;
    bool isCommandPrompt(const QString &text) const;
    void handleLoginFlow(const QString &data);
    bool isConnectedInternal() const; // 不加锁的内部版本
    QString formatBaudRate(int baudRate) const;
    QSerialPort::BaudRate intToBaudRate(int baudRate) const;
    QSerialPort::DataBits intToDataBits(int dataBits) const;
    QSerialPort::StopBits floatToStopBits(float stopBits) const;
    QSerialPort::Parity stringToParity(const QString &parity) const;
    QSerialPort::FlowControl stringToFlowControl(const QString &flowControl) const;

private:
    QSerialPort *m_serialPort;

    // 连接参数
    QString m_portName;
    int m_baudRate;
    int m_dataBits;
    float m_stopBits;
    QString m_parity;
    QString m_flowControl;

    // 登录参数
    QString m_username;
    QString m_password;
    bool m_autoLogin;

    // 数据处理
    QByteArray m_dataBuffer;
    QString m_lineBuffer;

    // 登录状态
    enum LoginState
    {
        NotLoggedIn,
        WaitingForLogin,
        WaitingForPassword,
        LoggedIn,
        LoginFailed
    };
    LoginState m_loginState;

    // 定时器
    QTimer *m_loginTimer;
    QTimer *m_delayTimer;

    // 延迟执行的动作
    enum DelayedAction
    {
        NoAction,
        SendInitialCarriageReturn,
        SendUsername,
        SendPassword
    };
    DelayedAction m_pendingAction;
    QString m_pendingData;

    // 超时设置
    int m_readTimeout;
    int m_writeTimeout;

    // 线程安全
    mutable QMutex m_mutex;

    // 登录提示符
    QStringList m_loginPrompts;
    QStringList m_passwordPrompts;
    QStringList m_commandPrompts;
};

#endif // SERIALCONNECTION_H
