#include "TerminalConfigDialog.h"
#include <QApplication>
#include <QMessageBox>
#include <QStandardPaths>
#include <QDir>
#include <QDebug>

TerminalConfigDialog::TerminalConfigDialog(QWidget *parent)
    : QDialog(parent)
{
    setWindowTitle("终端配置");
    setModal(true);
    resize(500, 600);

    setupUI();
    loadConfig();
}

TerminalConfigDialog::~TerminalConfigDialog()
{
}

void TerminalConfigDialog::setupUI()
{
    m_mainLayout = new QVBoxLayout(this);

    // 创建滚动区域
    m_scrollArea = new QScrollArea;
    m_scrollWidget = new QWidget;
    m_scrollLayout = new QVBoxLayout(m_scrollWidget);

    // 字体配置组
    m_fontGroup = new QGroupBox("字体设置");
    QFormLayout *fontLayout = new QFormLayout(m_fontGroup);

    m_fontFamilyCombo = new QComboBox;
    m_fontFamilyCombo->addItems({"Consolas", "Courier New", "Monaco", "Menlo", "DejaVu Sans Mono", "Liberation Mono", "Microsoft YaHei", "SimHei"});
    fontLayout->addRow("字体:", m_fontFamilyCombo);

    m_fontSizeSpin = new QSpinBox;
    m_fontSizeSpin->setRange(8, 24);
    m_fontSizeSpin->setSuffix(" pt");
    m_fontSizeSpin->setValue(10);
    fontLayout->addRow("字体大小:", m_fontSizeSpin);

    m_fontWeightCombo = new QComboBox;
    m_fontWeightCombo->addItem("正常", "normal");
    m_fontWeightCombo->addItem("粗体", "bold");
    fontLayout->addRow("字体粗细:", m_fontWeightCombo);

    // 时间戳配置组
    m_timestampGroup = new QGroupBox("时间戳设置");
    QFormLayout *timestampLayout = new QFormLayout(m_timestampGroup);

    m_timestampEnabledCheck = new QCheckBox("启用时间戳");
    timestampLayout->addRow(m_timestampEnabledCheck);

    m_timestampFormatCombo = new QComboBox;
    m_timestampFormatCombo->addItem("短格式 (14:30:25)", "hh:mm:ss");
    m_timestampFormatCombo->addItem("中格式 (12-25 14:30:25)", "MM-dd hh:mm:ss");
    m_timestampFormatCombo->addItem("长格式 (2024-12-25 14:30:25)", "yyyy-MM-dd hh:mm:ss");
    timestampLayout->addRow("时间戳格式:", m_timestampFormatCombo);

    // 历史记录配置组
    m_historyGroup = new QGroupBox("历史记录设置");
    QFormLayout *historyLayout = new QFormLayout(m_historyGroup);

    m_maxHistorySpin = new QSpinBox;
    m_maxHistorySpin->setRange(100, 10000);
    m_maxHistorySpin->setSuffix(" 条");
    m_maxHistorySpin->setValue(1000);
    historyLayout->addRow("最大历史记录:", m_maxHistorySpin);

    m_echoEnabledCheck = new QCheckBox("启用回显");
    m_echoEnabledCheck->setToolTip("启用后会显示输入的命令");
    historyLayout->addRow(m_echoEnabledCheck);

    // 显示配置组
    m_displayGroup = new QGroupBox("显示设置");
    QFormLayout *displayLayout = new QFormLayout(m_displayGroup);

    m_cursorWidthSpin = new QSpinBox;
    m_cursorWidthSpin->setRange(1, 10);
    m_cursorWidthSpin->setSuffix(" px");
    m_cursorWidthSpin->setValue(3);
    displayLayout->addRow("光标宽度:", m_cursorWidthSpin);

    m_wordWrapCheck = new QCheckBox("自动换行");
    m_wordWrapCheck->setChecked(true);
    displayLayout->addRow(m_wordWrapCheck);

    m_scrollBarPolicyCombo = new QComboBox;
    m_scrollBarPolicyCombo->addItem("自动显示", Qt::ScrollBarAsNeeded);
    m_scrollBarPolicyCombo->addItem("总是显示", Qt::ScrollBarAlwaysOn);
    m_scrollBarPolicyCombo->addItem("总是隐藏", Qt::ScrollBarAlwaysOff);
    displayLayout->addRow("滚动条:", m_scrollBarPolicyCombo);

    // 添加到滚动布局
    m_scrollLayout->addWidget(m_fontGroup);
    m_scrollLayout->addWidget(m_timestampGroup);
    m_scrollLayout->addWidget(m_historyGroup);
    m_scrollLayout->addWidget(m_displayGroup);
    m_scrollLayout->addStretch();

    m_scrollArea->setWidget(m_scrollWidget);
    m_scrollArea->setWidgetResizable(true);

    // 按钮
    QHBoxLayout *buttonLayout = new QHBoxLayout;
    m_okBtn = new QPushButton("确定");
    m_cancelBtn = new QPushButton("取消");
    m_resetBtn = new QPushButton("重置");
    m_applyBtn = new QPushButton("应用");

    // 添加测试按钮
    QPushButton *testBtn = new QPushButton("测试字体");
    testBtn->setStyleSheet("background-color: #ff6b6b; color: white;");

    // 确保按钮是启用的
    m_okBtn->setEnabled(true);
    m_cancelBtn->setEnabled(true);
    m_resetBtn->setEnabled(true);
    m_applyBtn->setEnabled(true);

    qDebug() << "TerminalConfigDialog::setupUI() - Buttons created and enabled";

    buttonLayout->addWidget(m_resetBtn);
    buttonLayout->addWidget(testBtn);
    buttonLayout->addStretch();
    buttonLayout->addWidget(m_applyBtn);
    buttonLayout->addWidget(m_okBtn);
    buttonLayout->addWidget(m_cancelBtn);

    // 设置按钮最小尺寸，确保可点击
    m_applyBtn->setMinimumSize(80, 30);
    m_okBtn->setMinimumSize(80, 30);
    m_cancelBtn->setMinimumSize(80, 30);
    m_resetBtn->setMinimumSize(80, 30);

    qDebug() << "TerminalConfigDialog::setupUI() - Button layout completed";

    // 连接信号 - 使用更明确的连接方式
    bool ok1 = connect(m_okBtn, SIGNAL(clicked()), this, SLOT(onOkClicked()));
    bool ok2 = connect(m_cancelBtn, SIGNAL(clicked()), this, SLOT(onCancelClicked()));
    bool ok3 = connect(m_resetBtn, SIGNAL(clicked()), this, SLOT(onResetClicked()));
    bool ok4 = connect(m_applyBtn, SIGNAL(clicked()), this, SLOT(onApplyClicked()));

    qDebug() << "TerminalConfigDialog::setupUI() - Signal connections:" << ok1 << ok2 << ok3 << ok4;

    // 测试：添加一个强制应用的连接
    connect(m_applyBtn, &QPushButton::clicked, [this]()
            {
                qDebug() << "TerminalConfigDialog - Apply button lambda triggered!";
                qDebug() << "Current font size value:" << m_fontSizeSpin->value();

                // 强制更新所有控件的值
                m_fontSizeSpin->clearFocus();
                m_fontFamilyCombo->clearFocus();
                m_fontWeightCombo->clearFocus();

                // 强制触发保存配置
                this->saveConfig();
                qDebug() << "TerminalConfigDialog - Forced saveConfig() called"; });

    // 测试：监听字体大小变化
    connect(m_fontSizeSpin, QOverload<int>::of(&QSpinBox::valueChanged), [this](int value)
            { qDebug() << "TerminalConfigDialog - Font size changed to:" << value; });

    // 测试按钮功能
    connect(testBtn, &QPushButton::clicked, [this]()
            {
                qDebug() << "=== TEST BUTTON CLICKED ===";
                qDebug() << "Font family:" << m_fontFamilyCombo->currentText();
                qDebug() << "Font size:" << m_fontSizeSpin->value();
                qDebug() << "Font weight:" << m_fontWeightCombo->currentData().toString();

                // 直接发送信号测试
                emit configChanged("terminal/font_family", m_fontFamilyCombo->currentText());
                emit configChanged("terminal/font_size", m_fontSizeSpin->value());
                emit configChanged("terminal/font_weight", m_fontWeightCombo->currentData().toString());

                qDebug() << "=== TEST SIGNALS SENT ==="; });

    // 添加到主布局
    m_mainLayout->addWidget(m_scrollArea);
    m_mainLayout->addLayout(buttonLayout);
}

void TerminalConfigDialog::loadConfig()
{
    // 设置默认值
    m_fontFamilyCombo->setCurrentText("Consolas");
    m_fontSizeSpin->setValue(10);
    m_fontWeightCombo->setCurrentIndex(0); // normal

    m_timestampEnabledCheck->setChecked(true);
    m_timestampFormatCombo->setCurrentIndex(0); // hh:mm:ss

    m_maxHistorySpin->setValue(1000);
    m_echoEnabledCheck->setChecked(true);

    m_cursorWidthSpin->setValue(3);
    m_wordWrapCheck->setChecked(true);
    m_scrollBarPolicyCombo->setCurrentIndex(0); // AsNeeded
}

void TerminalConfigDialog::saveConfig()
{
    qDebug() << "TerminalConfigDialog::saveConfig() - start";

    // 发送配置更改信号
    QString fontFamily = m_fontFamilyCombo->currentText();
    int fontSize = m_fontSizeSpin->value();
    QString fontWeight = m_fontWeightCombo->currentData().toString();

    qDebug() << "Font config to save:" << fontFamily << fontSize << fontWeight;
    qDebug() << "Font size spin current value:" << m_fontSizeSpin->value();
    qDebug() << "Font size spin range:" << m_fontSizeSpin->minimum() << "to" << m_fontSizeSpin->maximum();

    emit configChanged("terminal/font_family", fontFamily);
    emit configChanged("terminal/font_size", fontSize);
    emit configChanged("terminal/font_weight", fontWeight);

    emit configChanged("terminal/timestamp_enabled", m_timestampEnabledCheck->isChecked());
    emit configChanged("terminal/timestamp_format", m_timestampFormatCombo->currentData().toString());

    emit configChanged("terminal/max_history", m_maxHistorySpin->value());
    emit configChanged("terminal/echo_enabled", m_echoEnabledCheck->isChecked());

    emit configChanged("terminal/cursor_width", m_cursorWidthSpin->value());
    emit configChanged("terminal/word_wrap", m_wordWrapCheck->isChecked());
    emit configChanged("terminal/scroll_bar_policy", m_scrollBarPolicyCombo->currentData().toInt());
}

void TerminalConfigDialog::setConfig(const QVariantMap &config)
{
    m_config = config;
    updateUI();
}

QVariantMap TerminalConfigDialog::getConfig() const
{
    return m_config;
}

void TerminalConfigDialog::updateUI()
{
    qDebug() << "TerminalConfigDialog::updateUI() - start";
    qDebug() << "Config content:" << m_config;

    // 根据配置更新UI
    if (m_config.contains("font_family"))
    {
        QString fontFamily = m_config["font_family"].toString();
        qDebug() << "Set font family:" << fontFamily;
        m_fontFamilyCombo->setCurrentText(fontFamily);
    }
    if (m_config.contains("font_size"))
    {
        int fontSize = m_config["font_size"].toInt();
        qDebug() << "Set font size:" << fontSize;
        m_fontSizeSpin->setValue(fontSize);
        qDebug() << "Font size spin value after setting:" << m_fontSizeSpin->value();
    }
    else
    {
        qDebug() << "No font_size in config, using default";
        m_fontSizeSpin->setValue(10);
    }
    if (m_config.contains("font_weight"))
    {
        QString weight = m_config["font_weight"].toString();
        int index = m_fontWeightCombo->findData(weight);
        if (index >= 0)
        {
            m_fontWeightCombo->setCurrentIndex(index);
        }
    }
    if (m_config.contains("timestamp_enabled"))
    {
        m_timestampEnabledCheck->setChecked(m_config["timestamp_enabled"].toBool());
    }
    if (m_config.contains("timestamp_format"))
    {
        QString format = m_config["timestamp_format"].toString();
        int index = m_timestampFormatCombo->findData(format);
        if (index >= 0)
        {
            m_timestampFormatCombo->setCurrentIndex(index);
        }
    }
    if (m_config.contains("max_history"))
    {
        m_maxHistorySpin->setValue(m_config["max_history"].toInt());
    }
    if (m_config.contains("echo_enabled"))
    {
        m_echoEnabledCheck->setChecked(m_config["echo_enabled"].toBool());
    }
    if (m_config.contains("cursor_width"))
    {
        m_cursorWidthSpin->setValue(m_config["cursor_width"].toInt());
    }
    if (m_config.contains("word_wrap"))
    {
        m_wordWrapCheck->setChecked(m_config["word_wrap"].toBool());
    }
    if (m_config.contains("scroll_bar_policy"))
    {
        int policy = m_config["scroll_bar_policy"].toInt();
        int index = m_scrollBarPolicyCombo->findData(policy);
        if (index >= 0)
        {
            m_scrollBarPolicyCombo->setCurrentIndex(index);
        }
    }
}

void TerminalConfigDialog::onOkClicked()
{
    qDebug() << "TerminalConfigDialog::onOkClicked() - OK button clicked!";
    saveConfig();
    accept();
}

void TerminalConfigDialog::onCancelClicked()
{
    reject();
}

void TerminalConfigDialog::onResetClicked()
{
    loadConfig();
}

void TerminalConfigDialog::onApplyClicked()
{
    qDebug() << "TerminalConfigDialog::onApplyClicked() - Apply button clicked!";
    saveConfig();
    qDebug() << "TerminalConfigDialog::onApplyClicked() - saveConfig() completed";
}
