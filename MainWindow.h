#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QVBoxLayout>
#include <QHBoxLayout>
#include <QGridLayout>
#include <QSplitter>
#include <QFrame>
#include <QLabel>
#include <QPushButton>
#include <QLineEdit>
#include <QTextEdit>
#include <QComboBox>
#include <QListWidget>
#include <QTabWidget>
#include <QToolBar>
#include <QStatusBar>
#include <QMenuBar>
#include <QMenu>
#include <QAction>
#include <QTimer>
#include <QSettings>
#include <QDateTime>
#include <QTcpServer>
#include <QTcpSocket>
#include <QNetworkInterface>
#include <QCloseEvent>
#include <QResizeEvent>
#include <QShowEvent>

// 前向声明
class ConnectionManager;
class ConfigManager;
class LogManager;
class CommandHistory;
class InteractiveTerminal;
class ProfessionalTerminal;
class CommandListWidget;
class ConnectionDialog;
class LogConfigDialog;
class BackgroundConfigDialog;
class TerminalConfigDialog;

QT_BEGIN_NAMESPACE
class QAction;
class QMenu;
class QToolBar;
class QStatusBar;
QT_END_NAMESPACE

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

protected:
    void closeEvent(QCloseEvent *event) override;
    void resizeEvent(QResizeEvent *event) override;
    void showEvent(QShowEvent *event) override;

private slots:
    // 连接相关
    void showConnectionDialog();
    void connectDevice();
    void disconnectDevice();
    void onConnectionButtonClicked(); // 连接按钮点击处理
    void onConnectionStatusChanged(const QString &status, const QString &details);
    void onDataReceived(const QString &data);
    void onRawDataReceived(const QByteArray &data);
    void onErrorOccurred(const QString &error);
    void onConnectionEstablished();
    void onConnectionLost();

    // 命令相关
    void sendCommand();
    void executeQuickCommand();
    void toggleTimestamp();
    void executeCommonCommand(const QString &name, const QString &content);
    void onCommandEdited(const QString &oldName, const QString &newName, const QString &newContent);
    void onCommandDeleted(const QString &name);
    void onCommandAdded(const QString &name, const QString &content);
    void addNewCommand();

    // 新增的快捷功能
    void quickConnect();    // 快速连接
    void quickDisconnect(); // 快速断开
    void quickClear();      // 快速清空
    void quickPause();      // 快速暂停/恢复

    // 新增功能方法
    void showFindDialog();   // 显示查找对话框
    void toggleHighlight();  // 切换高亮功能
    void showRemoteDialog(); // 显示远程连接对话框

    // 远程连接功能
    void startRemoteServer(int port = 8080);                                           // 启动远程服务器
    void stopRemoteServer();                                                           // 停止远程服务器
    void connectToRemoteHost(const QString &host, int port, bool controlMode = false); // 连接到远程主机
    void disconnectFromRemoteHost();                                                   // 断开远程连接
    void sendRemoteMessage(const QString &message);                                    // 发送远程消息
    void broadcastToRemoteClients(const QString &message);                             // 广播消息到所有远程客户端

private slots:
    // 远程连接槽函数
    void onRemoteClientConnected();                                   // 远程客户端连接
    void onRemoteClientDisconnected();                                // 远程客户端断开
    void onRemoteDataReceived();                                      // 接收远程数据
    void onRemoteConnectionError(QAbstractSocket::SocketError error); // 远程连接错误

    // 配置相关
    void showConfigMenu();
    void showLogConfig();
    void showBackgroundConfig();
    void showUIConfig();
    void showTerminalConfig();
    void resetConfig();
    void onConfigChanged(const QString &key, const QVariant &value);

    // 导入导出
    void showImportExportMenu();
    void exportConfig();
    void importConfig();
    void exportCommands();
    void importCommands();

    // 界面相关
    void toggleCommandsPanel();
    void updateConnectionStatus();
    void updateConnectionDetails(); // 更新连接详细信息
    void updateCommandButtons();
    void performBatchUIUpdate(); // 批量UI更新
    void scheduleUIUpdate();     // 调度UI更新
    void applyBackgroundConfig();
    void applyFontConfig(); // 应用字体配置
    void onTerminalTimestampStateChanged(bool enabled);

    // 其他
    void showHelp();
    void autoSave();

private:
    void setupUI();
    void setupMenuBar();
    void setupToolBar();
    void setupCentralWidget();
    void setupStatusBar();
    void setupConnections();

    void createTopMenuArea();
    void createMainContentArea();
    void createHistoryPanel();
    void createMainWorkArea();
    void createCommandsPanel();
    void createQuickCommandsArea();
    void createLogDisplayArea();
    void createCommandInputArea();
    void createBottomStatusArea();

    void applyMacStyle();
    void loadWindowConfig();
    void saveWindowConfig();
    void applyPanelStates();
    void forceShowCommandButton();
    void loadCommonCommands();
    void saveCommonCommands();
    void applyConfig();

    // 背景配置辅助方法
    QString buildSimpleTerminalStyle(const QString &type, const QString &color, const QString &color2,
                                     const QString &image, const QString &imageMode, double opacity);
    void applyStyleToTerminals(const QString &style, double opacity = 1.0);

    // 历史连接管理
    void loadConnectionHistory();
    void saveConnectionHistory();
    void saveCurrentConnectionToHistory();
    void addConnectionToHistory(const QString &type, const QString &config);
    void toggleHistoryPanel();
    void onHistoryItemClicked(QListWidgetItem *item);
    void onHistoryItemDoubleClicked(QListWidgetItem *item);
    void onHistoryContextMenu(const QPoint &pos);
    void onLogSaveItemClicked(QListWidgetItem *item);

    // 历史连接操作方法
    void connectToSerial(const QString &config);
    void connectToTCP(const QString &config);
    void connectToADB(const QString &deviceId = QString());
    void connectToDeviceByType(const QString &type, const QString &config);
    void connectWithHistoryConfig(const QString &type, const QString &config);
    void renameHistoryItem(QListWidgetItem *item);
    void editHistoryItem(QListWidgetItem *item);
    void deleteHistoryItem(QListWidgetItem *item);
    void updateHistoryConnectionStatus();     // 更新历史连接状态显示
    void findAndHighlightCurrentConnection(); // 查找并高亮当前连接项

    // 日志保存方法
    void saveCurrentTerminalContent();
    void saveSessionLog();
    void saveRawData();

    QString buildBackgroundStyle(const QVariantMap &config);
    void updateMainWindowStyle(const QString &bgStyle);

private:
    // 核心组件
    ConnectionManager *m_connectionManager;
    ConfigManager *m_configManager;
    LogManager *m_logManager;
    CommandHistory *m_commandHistory;

    // UI组件
    QWidget *m_centralWidget;
    QSplitter *m_mainSplitter;
    QFrame *m_historyFrame;
    QFrame *m_mainWorkFrame;
    QFrame *m_commandsFrame;

    // 菜单和工具栏
    QMenuBar *m_menuBar;
    QToolBar *m_toolBar;
    QStatusBar *m_statusBar;

    // 顶部菜单区域
    QFrame *m_topMenuFrame;
    QLabel *m_menuTitle;
    QPushButton *m_configBtn;
    QPushButton *m_importExportBtn;
    QPushButton *m_helpBtn;

    // 快捷命令区域
    QFrame *m_quickCommandFrame;
    QPushButton *m_cmdBtn1;
    QPushButton *m_cmdBtn2;
    QPushButton *m_cmdBtn3;
    QPushButton *m_timestampBtn;
    QLineEdit *m_quickCmdInput;

    // 新增的快捷功能按钮
    QPushButton *m_quickConnectBtn;    // 连接按钮
    QPushButton *m_quickDisconnectBtn; // 断开按钮
    QPushButton *m_quickClearBtn;      // 清空按钮
    QPushButton *m_quickPauseBtn;      // 暂停按钮
    QPushButton *m_findBtn;            // 查找按钮
    QPushButton *m_highlightBtn;       // 高亮按钮
    QPushButton *m_remoteBtn;          // 远程按钮

    // 日志显示区域
    QFrame *m_logFrame;
    InteractiveTerminal *m_terminal;
    ProfessionalTerminal *m_professionalTerminal;

    // 命令输入区域
    QFrame *m_commandInputFrame;
    QLabel *m_statusLabel;
    QLineEdit *m_commandInput;
    QPushButton *m_sendBtn;
    QLabel *m_connectionTypeLabel;
    QPushButton *m_connectionBtn;

    // 历史连接面板
    QListWidget *m_historyList;
    QPushButton *m_hideHistoryBtn;
    QPushButton *m_showHistoryBtn;

    // 常用命令面板
    QLabel *m_commandsTitle;
    QPushButton *m_toggleCommandsBtn;
    CommandListWidget *m_commandsList;
    QPushButton *m_addCommandBtn;
    QPushButton *m_showCommandsBtn; // 显示命令栏的按钮

    // 状态信息
    QLabel *m_statusInfo;
    QLabel *m_connectionDetailsLabel; // 连接详细信息
    QLabel *m_connectionTimeLabel;    // 连接时间信息
    QLabel *m_versionLabel;

    // 对话框
    ConnectionDialog *m_connectionDialog;
    LogConfigDialog *m_logConfigDialog;
    BackgroundConfigDialog *m_backgroundConfigDialog;
    TerminalConfigDialog *m_terminalConfigDialog;

    // 状态变量
    bool m_commandsPanelVisible;
    bool m_isConnected;
    bool m_timestampEnabled;
    bool m_processingRawData;
    QString m_connectionType;
    QVariantMap m_connectionParams;
    QListWidgetItem *m_currentConnectionItem; // 当前连接的历史项
    QDateTime m_connectionStartTime;          // 连接开始时间

    // 性能优化相关
    bool m_uiUpdatePending;        // UI更新待处理标志
    QString m_pendingTerminalData; // 待处理的终端数据

    // 远程连接相关
    QTcpServer *m_remoteServer;          // 远程服务器
    QTcpSocket *m_remoteClient;          // 远程客户端连接
    QList<QTcpSocket *> m_remoteClients; // 远程客户端列表
    bool m_remoteServerEnabled;          // 远程服务器是否启用
    int m_remoteServerPort;              // 远程服务器端口

    // 定时器
    QTimer *m_autoSaveTimer;
    QTimer *m_statusUpdateTimer;
    QTimer *m_connectionTimeUpdateTimer; // 连接时间更新定时器
    QTimer *m_uiUpdateTimer;             // UI批量更新定时器

    // 设置
    QSettings *m_settings;
};

#endif // MAINWINDOW_H
